# 单阶段令牌认证系统 API 文档

## 概述

本文档描述了团队管理系统的单阶段令牌认证机制。与传统的两阶段令牌系统不同，新系统使用单一JWT令牌来承载用户身份和团队上下文信息，简化了认证流程并提高了用户体验。

## 认证流程

### 1. 用户登录
用户使用邮箱和密码登录，获得包含用户信息的JWT令牌。

**端点**: `POST /auth/login`

**请求体**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**响应**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiJ9...",
    "expiresIn": 604800000,
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "name": "用户名"
    },
    "teams": [
      {
        "id": 1,
        "isCreator": true,
        "lastAccessTime": "2023-01-01T00:00:00"
      }
    ]
  }
}
```

### 2. 团队选择
用户选择要进入的团队，系统更新令牌以包含团队信息。

**端点**: `POST /auth/select-team`

**请求头**: `Authorization: Bearer {current_token}`

**请求体**:
```json
{
  "teamId": 1
}
```

**响应**:
```json
{
  "code": 200,
  "message": "团队选择成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiJ9...",
    "expiresIn": 604800000,
    "team": {
      "id": 1,
      "isCreator": true
    }
  }
}
```

### 3. 团队切换
用户切换到其他团队。

**端点**: `POST /auth/switch-team`

**请求头**: `Authorization: Bearer {current_token}`

**请求体**:
```json
{
  "teamId": 2
}
```

**响应**: 与团队选择相同

### 4. 清除团队上下文
用户清除当前团队上下文，回到用户级别权限。

**端点**: `POST /auth/clear-team`

**请求头**: `Authorization: Bearer {current_token}`

**响应**:
```json
{
  "code": 200,
  "message": "团队上下文已清除",
  "data": "eyJhbGciOiJIUzI1NiJ9..."
}
```

### 5. 刷新令牌
刷新即将过期的令牌。

**端点**: `POST /auth/refresh-token`

**请求头**: `Authorization: Bearer {current_token}`

**响应**:
```json
{
  "code": 200,
  "message": "Token刷新成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiJ9...",
    "expiresIn": 604800000,
    "team": {
      "id": 1,
      "isCreator": true
    }
  }
}
```

## JWT令牌结构

### 用户令牌（无团队信息）
```json
{
  "userId": 1,
  "email": "<EMAIL>",
  "name": "用户名",
  "jti": "unique-token-id",
  "iat": 1640995200,
  "exp": 1641600000,
  "sub": "<EMAIL>"
}
```

### 团队令牌（包含团队信息）
```json
{
  "userId": 1,
  "email": "<EMAIL>",
  "name": "用户名",
  "teamId": 1,
  "isCreator": true,
  "jti": "unique-token-id",
  "iat": 1640995200,
  "exp": 1641600000,
  "sub": "<EMAIL>"
}
```

## 权限控制

### 权限级别
- **USER**: 用户级别权限，可以访问用户管理、团队列表等功能
- **TEAM**: 团队级别权限，可以访问团队内部功能

### API端点权限要求

#### 需要USER或TEAM权限的端点
- `/auth/select-team`
- `/auth/switch-team`
- `/auth/clear-team`
- `/auth/refresh-token`
- `/users/**`
- `/teams`
- `/subscriptions/**`

#### 需要TEAM权限的端点
- `/teams/current/**`

## 兼容性

为了保持向后兼容性，系统保留了以下兼容性端点：

- `POST /auth/team-login` (已废弃，使用 `/auth/select-team` 替代)
- `POST /auth/refresh-team-token` (已废弃，使用 `/auth/refresh-token` 替代)

## 安全特性

1. **令牌签名**: 使用HMAC SHA-256算法签名
2. **令牌过期**: 默认7天过期时间
3. **会话管理**: 基于数据库的会话验证
4. **团队隔离**: 严格的团队数据隔离
5. **权限检查**: API级别的权限验证

## 错误处理

### 常见错误码
- `401`: 未认证或令牌无效
- `403`: 权限不足
- `400`: 请求参数错误

### 错误响应格式
```json
{
  "code": 401,
  "message": "Token无效或已过期",
  "data": null
}
```
