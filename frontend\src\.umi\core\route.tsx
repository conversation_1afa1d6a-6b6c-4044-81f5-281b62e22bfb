// @ts-nocheck
// This file is generated by <PERSON><PERSON> automatically
// DO NOT CHANGE IT MANUALLY!
import React from 'react';

export async function getRoutes() {
  const routes = {"1":{"path":"/user","layout":false,"id":"1"},"2":{"name":"login","path":"/user/login","parentId":"1","id":"2"},"3":{"name":"team-select","path":"/user/team-select","parentId":"1","id":"3"},"4":{"path":"/team/create","layout":false,"id":"4"},"5":{"path":"/dashboard","name":"仪表盘","icon":"dashboard","parentId":"ant-design-pro-layout","id":"5"},"6":{"path":"/team","name":"团队管理","icon":"team","hideInMenu":true,"parentId":"ant-design-pro-layout","id":"6"},"7":{"path":"/personal-center","name":"个人中心","icon":"user","hideInMenu":true,"parentId":"ant-design-pro-layout","id":"7"},"8":{"path":"/user-manage","name":"用户管理","icon":"user","hideInMenu":true,"parentId":"ant-design-pro-layout","id":"8"},"9":{"path":"/subscription","name":"订阅管理","icon":"crown","hideInMenu":true,"parentId":"ant-design-pro-layout","id":"9"},"10":{"path":"/friend","name":"好友管理","icon":"userAdd","hideInMenu":true,"parentId":"ant-design-pro-layout","id":"10"},"11":{"path":"/help","name":"帮助中心","icon":"question","hideInMenu":true,"parentId":"ant-design-pro-layout","id":"11"},"12":{"path":"/","redirect":"/dashboard","parentId":"ant-design-pro-layout","id":"12"},"13":{"path":"*","layout":false,"id":"13"},"ant-design-pro-layout":{"id":"ant-design-pro-layout","path":"/","isLayout":true},"umi/plugin/openapi":{"path":"/umi/plugin/openapi","id":"umi/plugin/openapi"}} as const;
  return {
    routes,
    routeComponents: {
'1': React.lazy(() => import('./EmptyRoute')),
'2': React.lazy(() => import(/* webpackChunkName: "p__user__login__index" */'@/pages/user/login/index.tsx')),
'3': React.lazy(() => import(/* webpackChunkName: "p__user__team-select__index" */'@/pages/user/team-select/index.tsx')),
'4': React.lazy(() => import(/* webpackChunkName: "p__team__create__index" */'@/pages/team/create/index.tsx')),
'5': React.lazy(() => import(/* webpackChunkName: "p__Dashboard__index" */'@/pages/Dashboard/index.tsx')),
'6': React.lazy(() => import(/* webpackChunkName: "p__team__index" */'@/pages/team/index.tsx')),
'7': React.lazy(() => import(/* webpackChunkName: "p__personal-center__index" */'@/pages/personal-center/index.tsx')),
'8': React.lazy(() => import(/* webpackChunkName: "p__user__index" */'@/pages/user/index.tsx')),
'9': React.lazy(() => import(/* webpackChunkName: "p__subscription__index" */'@/pages/subscription/index.tsx')),
'10': React.lazy(() => import(/* webpackChunkName: "p__friend__index" */'@/pages/friend/index.tsx')),
'11': React.lazy(() => import(/* webpackChunkName: "p__help__index" */'@/pages/help/index.tsx')),
'12': React.lazy(() => import('./EmptyRoute')),
'13': React.lazy(() => import(/* webpackChunkName: "p__404" */'@/pages/404.tsx')),
'ant-design-pro-layout': React.lazy(() => import(/* webpackChunkName: "umi__plugin-layout__Layout" */'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-layout/Layout.tsx')),
'umi/plugin/openapi': React.lazy(() => import(/* webpackChunkName: "umi__plugin-openapi__openapi" */'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-openapi/openapi.tsx')),
},
  };
}
