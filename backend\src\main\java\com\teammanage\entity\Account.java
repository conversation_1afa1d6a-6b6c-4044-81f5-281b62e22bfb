package com.teammanage.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.Objects;

/**
 * 用户账户实体类
 *
 * <AUTHOR>
 * @since 1.0.0
 */

@TableName("account")
public class Account extends BaseEntity {

    /**
     * 邮箱
     */
    private String email;

    /**
     * 密码哈希
     */
    @JsonIgnore
    private String passwordHash;

    /**
     * 用户名
     */
    private String name;

    /**
     * 当前套餐ID
     */
    private Long defaultSubscriptionPlanId;

    // Getter and Setter methods
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }

    public String getPasswordHash() { return passwordHash; }
    public void setPasswordHash(String passwordHash) { this.passwordHash = passwordHash; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public Long getDefaultSubscriptionPlanId() { return defaultSubscriptionPlanId; }
    public void setDefaultSubscriptionPlanId(Long defaultSubscriptionPlanId) { this.defaultSubscriptionPlanId = defaultSubscriptionPlanId; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        Account account = (Account) o;
        return Objects.equals(email, account.email) &&
               Objects.equals(passwordHash, account.passwordHash) &&
               Objects.equals(name, account.name) &&
               Objects.equals(defaultSubscriptionPlanId, account.defaultSubscriptionPlanId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), email, passwordHash, name, defaultSubscriptionPlanId);
    }

}
