{"version": 3, "sources": ["src/pages/personal-center/components/FriendManageContent.tsx", "src/pages/personal-center/components/TeamManageContent.tsx", "src/pages/personal-center/index.tsx", "src/pages/team/detail/components/TeamDetailContent.tsx", "src/pages/team/detail/components/TeamMemberList.tsx"], "sourcesContent": ["/**\n * 个人中心 - 好友管理内容组件\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  List,\n  Avatar,\n  Button,\n  Space,\n  Typography,\n  Input,\n  message,\n  Empty,\n  Modal,\n  Popconfirm,\n  Form,\n  Tooltip,\n  Row,\n  Col,\n  Divider,\n  Pagination,\n  Spin,\n  Tabs,\n  Badge\n} from 'antd';\nimport {\n  UserOutlined,\n  DeleteOutlined,\n  SearchOutlined,\n  ExclamationCircleOutlined,\n  EditOutlined,\n  UserAddOutlined,\n  ReloadOutlined,\n  UnorderedListOutlined,\n  InboxOutlined\n} from '@ant-design/icons';\nimport { FriendService, UserService } from '@/services';\nimport type { Account } from '@/types/api';\n\nconst { Text, Title } = Typography;\nconst { Search } = Input;\n\nconst FriendManageContent: React.FC = () => {\n  // 标签页状态\n  const [activeTab, setActiveTab] = useState('list');\n  \n  // 好友列表状态\n  const [friends, setFriends] = useState<Account[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [searchKeyword, setSearchKeyword] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [pageSize] = useState(8);\n\n  // 操作状态\n  const [removing, setRemoving] = useState<number | null>(null);\n  const [remarkModalVisible, setRemarkModalVisible] = useState(false);\n  const [addFriendModalVisible, setAddFriendModalVisible] = useState(false);\n  const [currentFriend, setCurrentFriend] = useState<Account | null>(null);\n\n  // 表单\n  const [remarkForm] = Form.useForm();\n  const [addFriendForm] = Form.useForm();\n\n  // 添加好友相关状态\n  const [searchUsers, setSearchUsers] = useState<Account[]>([]);\n  const [searchingUsers, setSearchingUsers] = useState(false);\n  const [addingFriend, setAddingFriend] = useState<number | null>(null);\n\n  // 获取好友列表\n  const fetchFriends = async () => {\n    try {\n      setLoading(true);\n      const friendList = await FriendService.getFriends();\n      setFriends(friendList);\n    } catch (error) {\n      console.error('获取好友列表失败:', error);\n      message.error('获取好友列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchFriends();\n  }, []);\n\n  // 过滤好友列表\n  const filteredFriends = friends.filter(friend =>\n    friend.name?.toLowerCase().includes(searchKeyword.toLowerCase()) ||\n    friend.email?.toLowerCase().includes(searchKeyword.toLowerCase())\n  );\n\n  // 分页处理\n  const paginatedFriends = filteredFriends.slice(\n    (currentPage - 1) * pageSize,\n    currentPage * pageSize\n  );\n\n  // 删除好友\n  const handleRemoveFriend = async (friend: Account) => {\n    try {\n      setRemoving(friend.id);\n      await FriendService.removeFriend(friend.id);\n      message.success(`已删除好友 \"${friend.name}\"`);\n      fetchFriends();\n    } catch (error) {\n      console.error('删除好友失败:', error);\n      message.error('删除好友失败');\n    } finally {\n      setRemoving(null);\n    }\n  };\n\n  // 编辑备注\n  const handleEditRemark = async (friend: Account) => {\n    setCurrentFriend(friend);\n    setRemarkModalVisible(true);\n    \n    try {\n      // 获取当前备注\n      const remark = await FriendService.getFriendRemark(friend.id);\n      remarkForm.setFieldsValue({ remark });\n    } catch (error) {\n      console.error('获取好友备注失败:', error);\n      remarkForm.setFieldsValue({ remark: '' });\n    }\n  };\n\n  // 保存备注\n  const handleSaveRemark = async (values: { remark: string }) => {\n    if (!currentFriend) return;\n\n    try {\n      await FriendService.setFriendRemark({\n        friendId: currentFriend.id,\n        remark: values.remark\n      });\n      message.success('备注保存成功');\n      setRemarkModalVisible(false);\n      setCurrentFriend(null);\n      remarkForm.resetFields();\n      fetchFriends();\n    } catch (error) {\n      console.error('保存备注失败:', error);\n      message.error('保存备注失败');\n    }\n  };\n\n  // 搜索用户\n  const handleSearchUsers = async (email: string) => {\n    if (!email.trim()) {\n      setSearchUsers([]);\n      return;\n    }\n\n    try {\n      setSearchingUsers(true);\n      const users = await UserService.searchUsersByEmail(email);\n      // 过滤掉已经是好友的用户\n      const friendIds = friends.map(f => f.id);\n      const availableUsers = users.filter(user => !friendIds.includes(user.id));\n      setSearchUsers(availableUsers);\n    } catch (error) {\n      console.error('搜索用户失败:', error);\n      message.error('搜索用户失败');\n    } finally {\n      setSearchingUsers(false);\n    }\n  };\n\n  // 添加好友\n  const handleAddFriend = async (user: Account) => {\n    try {\n      setAddingFriend(user.id);\n      await FriendService.sendFriendRequest({ email: user.email });\n      message.success(`已向 \"${user.name}\" 发送好友请求`);\n      setSearchUsers(searchUsers.filter(u => u.id !== user.id));\n    } catch (error) {\n      console.error('发送好友请求失败:', error);\n      message.error('发送好友请求失败');\n    } finally {\n      setAddingFriend(null);\n    }\n  };\n\n  // 打开添加好友模态框\n  const handleOpenAddFriend = () => {\n    setAddFriendModalVisible(true);\n    setSearchUsers([]);\n    addFriendForm.resetFields();\n  };\n\n  // 好友列表组件\n  const renderFriendList = () => (\n    <div>\n      {/* 头部操作区 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} md={8}>\n          <Search\n            placeholder=\"搜索好友姓名或邮箱\"\n            allowClear\n            value={searchKeyword}\n            onChange={(e) => setSearchKeyword(e.target.value)}\n            prefix={<SearchOutlined />}\n          />\n        </Col>\n        <Col xs={24} sm={12} md={16} style={{ textAlign: 'right' }}>\n          <Space>\n            <Button\n              type=\"primary\"\n              icon={<UserAddOutlined />}\n              onClick={handleOpenAddFriend}\n            >\n              添加好友\n            </Button>\n            <Button\n              icon={<ReloadOutlined />}\n              onClick={fetchFriends}\n              loading={loading}\n            >\n              刷新\n            </Button>\n          </Space>\n        </Col>\n      </Row>\n\n      {/* 好友统计 */}\n      <Card size=\"small\" style={{ marginBottom: 16 }}>\n        <Space split={<Divider type=\"vertical\" />}>\n          <Text>\n            <strong>好友总数：</strong>\n            <Text type=\"success\">{friends.length}</Text>\n          </Text>\n          <Text>\n            <strong>当前显示：</strong>\n            <Text type=\"primary\">{filteredFriends.length}</Text>\n          </Text>\n        </Space>\n      </Card>\n\n      {/* 好友列表 */}\n      <Card>\n        <Spin spinning={loading}>\n          <List\n            dataSource={paginatedFriends}\n            renderItem={(friend) => (\n              <List.Item\n                actions={[\n                  <Tooltip key=\"remark\" title=\"编辑备注\">\n                    <Button\n                      type=\"text\"\n                      icon={<EditOutlined />}\n                      onClick={() => handleEditRemark(friend)}\n                      size=\"small\"\n                    >\n                      备注\n                    </Button>\n                  </Tooltip>,\n                  <Popconfirm\n                    key=\"delete\"\n                    title=\"确认删除好友\"\n                    description={`确定要删除好友 \"${friend.name}\" 吗？`}\n                    icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}\n                    onConfirm={() => handleRemoveFriend(friend)}\n                    okText=\"确认删除\"\n                    cancelText=\"取消\"\n                    okType=\"danger\"\n                  >\n                    <Button\n                      type=\"text\"\n                      danger\n                      icon={<DeleteOutlined />}\n                      loading={removing === friend.id}\n                      size=\"small\"\n                    >\n                      删除\n                    </Button>\n                  </Popconfirm>\n                ]}\n              >\n                <List.Item.Meta\n                  avatar={\n                    <Avatar \n                      size={48} \n                      icon={<UserOutlined />}\n                      style={{ backgroundColor: '#1890ff' }}\n                    >\n                      {friend.name?.charAt(0).toUpperCase()}\n                    </Avatar>\n                  }\n                  title={\n                    <Space>\n                      <Text strong>{friend.name}</Text>\n                      {friend.remark && (\n                        <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                          ({friend.remark})\n                        </Text>\n                      )}\n                    </Space>\n                  }\n                  description={\n                    <Space direction=\"vertical\" size={4}>\n                      <Text type=\"secondary\">{friend.email}</Text>\n                      <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                        添加时间: {friend.createdAt ? new Date(friend.createdAt).toLocaleDateString() : '未知'}\n                      </Text>\n                    </Space>\n                  }\n                />\n              </List.Item>\n            )}\n            locale={{\n              emptyText: (\n                <Empty\n                  image={Empty.PRESENTED_IMAGE_SIMPLE}\n                  description={\n                    searchKeyword \n                      ? `没有找到包含 \"${searchKeyword}\" 的好友`\n                      : \"暂无好友，点击上方按钮添加好友\"\n                  }\n                />\n              )\n            }}\n          />\n\n          {/* 分页 */}\n          {filteredFriends.length > pageSize && (\n            <div style={{ textAlign: 'center', marginTop: 16 }}>\n              <Pagination\n                current={currentPage}\n                pageSize={pageSize}\n                total={filteredFriends.length}\n                onChange={setCurrentPage}\n                showSizeChanger={false}\n                showQuickJumper\n                showTotal={(total, range) => \n                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`\n                }\n              />\n            </div>\n          )}\n        </Spin>\n      </Card>\n    </div>\n  );\n\n  // 添加好友组件\n  const renderAddFriend = () => (\n    <Card>\n      <Title level={4}>添加好友</Title>\n      <Form\n        form={addFriendForm}\n        layout=\"vertical\"\n        onFinish={(values) => handleSearchUsers(values.email)}\n      >\n        <Form.Item\n          label=\"搜索用户\"\n          name=\"email\"\n          rules={[\n            { required: true, message: '请输入邮箱' },\n            { type: 'email', message: '请输入有效的邮箱地址' }\n          ]}\n        >\n          <Search\n            placeholder=\"请输入用户邮箱进行搜索\"\n            enterButton=\"搜索\"\n            loading={searchingUsers}\n            onSearch={handleSearchUsers}\n          />\n        </Form.Item>\n      </Form>\n\n      {/* 搜索结果 */}\n      {searchUsers.length > 0 && (\n        <div style={{ marginTop: 24 }}>\n          <Title level={5}>搜索结果</Title>\n          <List\n            dataSource={searchUsers}\n            renderItem={(user) => (\n              <List.Item\n                actions={[\n                  <Button\n                    key=\"add\"\n                    type=\"primary\"\n                    icon={<UserAddOutlined />}\n                    loading={addingFriend === user.id}\n                    onClick={() => handleAddFriend(user)}\n                    size=\"small\"\n                  >\n                    添加好友\n                  </Button>\n                ]}\n              >\n                <List.Item.Meta\n                  avatar={\n                    <Avatar\n                      size={40}\n                      icon={<UserOutlined />}\n                      style={{ backgroundColor: '#52c41a' }}\n                    >\n                      {user.name?.charAt(0).toUpperCase()}\n                    </Avatar>\n                  }\n                  title={user.name}\n                  description={user.email}\n                />\n              </List.Item>\n            )}\n          />\n        </div>\n      )}\n\n      {searchUsers.length === 0 && addFriendForm.getFieldValue('email') && !searchingUsers && (\n        <Empty\n          image={Empty.PRESENTED_IMAGE_SIMPLE}\n          description=\"未找到匹配的用户\"\n          style={{ marginTop: 24 }}\n        />\n      )}\n    </Card>\n  );\n\n  const tabItems = [\n    {\n      key: 'list',\n      label: (\n        <Space>\n          <UnorderedListOutlined />\n          我的好友\n          <Badge count={friends.length} showZero />\n        </Space>\n      ),\n      children: renderFriendList()\n    },\n    {\n      key: 'add',\n      label: (\n        <Space>\n          <UserAddOutlined />\n          添加好友\n        </Space>\n      ),\n      children: renderAddFriend()\n    }\n  ];\n\n  return (\n    <div>\n      <Tabs\n        activeKey={activeTab}\n        onChange={setActiveTab}\n        items={tabItems}\n        size=\"large\"\n      />\n\n      {/* 备注编辑模态框 */}\n      <Modal\n        title=\"编辑好友备注\"\n        open={remarkModalVisible}\n        onCancel={() => {\n          setRemarkModalVisible(false);\n          setCurrentFriend(null);\n          remarkForm.resetFields();\n        }}\n        footer={null}\n        width={400}\n      >\n        <Form\n          form={remarkForm}\n          layout=\"vertical\"\n          onFinish={handleSaveRemark}\n        >\n          <Form.Item\n            label=\"好友备注\"\n            name=\"remark\"\n            rules={[\n              { max: 50, message: '备注长度不能超过50个字符' }\n            ]}\n          >\n            <Input.TextArea\n              placeholder=\"为好友添加备注...\"\n              rows={3}\n              maxLength={50}\n              showCount\n            />\n          </Form.Item>\n          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>\n            <Space>\n              <Button onClick={() => {\n                setRemarkModalVisible(false);\n                setCurrentFriend(null);\n                remarkForm.resetFields();\n              }}>\n                取消\n              </Button>\n              <Button type=\"primary\" htmlType=\"submit\">\n                保存\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default FriendManageContent;\n", "/**\n * 团队管理内容组件 - 用于个人中心\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Button,\n  Space,\n  message,\n  Modal,\n  Form,\n  Input,\n  List,\n  Avatar,\n  Tag,\n  Popconfirm,\n  Empty\n} from 'antd';\nimport {\n  TeamOutlined,\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  UserOutlined,\n  CrownOutlined,\n  ArrowLeftOutlined\n} from '@ant-design/icons';\nimport { history } from '@umijs/max';\nimport { TeamService } from '@/services';\nimport type { TeamDetailResponse, CreateTeamRequest, UpdateTeamRequest } from '@/types/api';\nimport TeamDetailContent from '@/pages/team/detail/components/TeamDetailContent';\n\nconst { TextArea } = Input;\n\nconst TeamManageContent: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);\n  const [createModalVisible, setCreateModalVisible] = useState(false);\n  const [editModalVisible, setEditModalVisible] = useState(false);\n  const [editingTeam, setEditingTeam] = useState<TeamDetailResponse | null>(null);\n  const [selectedTeam, setSelectedTeam] = useState<TeamDetailResponse | null>(null);\n  const [viewMode, setViewMode] = useState<'list' | 'detail'>('list');\n  const [createForm] = Form.useForm();\n  const [editForm] = Form.useForm();\n\n  useEffect(() => {\n    fetchTeams();\n  }, []);\n\n  /**\n   * 获取用户的团队列表\n   */\n  const fetchTeams = async () => {\n    try {\n      setLoading(true);\n      const teamList = await TeamService.getUserTeams();\n      setTeams(teamList);\n    } catch (error) {\n      console.error('获取团队列表失败:', error);\n      message.error('获取团队列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  /**\n   * 创建团队\n   */\n  const handleCreateTeam = async (values: CreateTeamRequest) => {\n    try {\n      await TeamService.createTeam(values);\n      message.success('团队创建成功');\n      setCreateModalVisible(false);\n      createForm.resetFields();\n      fetchTeams();\n    } catch (error) {\n      console.error('创建团队失败:', error);\n      message.error('创建团队失败');\n    }\n  };\n\n  /**\n   * 更新团队信息\n   */\n  const handleUpdateTeam = async (values: UpdateTeamRequest) => {\n    if (!editingTeam) return;\n\n    try {\n      await TeamService.updateTeam(editingTeam.id, values);\n      message.success('团队信息更新成功');\n      setEditModalVisible(false);\n      setEditingTeam(null);\n      editForm.resetFields();\n      fetchTeams();\n    } catch (error) {\n      console.error('更新团队失败:', error);\n      message.error('更新团队失败');\n    }\n  };\n\n  /**\n   * 删除团队\n   */\n  const handleDeleteTeam = async (team: TeamDetailResponse) => {\n    try {\n      await TeamService.deleteTeam(team.id);\n      message.success('团队删除成功');\n      // 如果删除的是当前查看的团队，返回列表\n      if (selectedTeam && selectedTeam.id === team.id) {\n        handleBackToList();\n      }\n      fetchTeams();\n    } catch (error) {\n      console.error('删除团队失败:', error);\n      message.error('删除团队失败');\n    }\n  };\n\n  /**\n   * 查看团队详情\n   */\n  const handleViewTeamDetail = (team: TeamDetailResponse) => {\n    setSelectedTeam(team);\n    setViewMode('detail');\n  };\n\n  /**\n   * 返回团队列表\n   */\n  const handleBackToList = () => {\n    setSelectedTeam(null);\n    setViewMode('list');\n  };\n\n  /**\n   * 开始编辑团队\n   */\n  const handleStartEdit = (team: TeamDetailResponse) => {\n    setEditingTeam(team);\n    editForm.setFieldsValue({\n      name: team.name,\n      description: team.description\n    });\n    setEditModalVisible(true);\n  };\n\n  // 如果是详情模式，显示团队详情\n  if (viewMode === 'detail' && selectedTeam) {\n    return (\n      <Card\n        title={\n          <Space>\n            <Button\n              icon={<ArrowLeftOutlined />}\n              onClick={handleBackToList}\n            >\n              返回团队列表\n            </Button>\n            <TeamOutlined />\n            {selectedTeam.name}\n          </Space>\n        }\n        extra={\n          selectedTeam.isCreator && (\n            <Space>\n              <Button\n                icon={<EditOutlined />}\n                onClick={() => handleStartEdit(selectedTeam)}\n              >\n                编辑团队\n              </Button>\n              <Popconfirm\n                title=\"确认删除团队\"\n                description={`确定要删除团队 \"${selectedTeam.name}\" 吗？此操作不可恢复。`}\n                onConfirm={() => handleDeleteTeam(selectedTeam)}\n                okText=\"确认删除\"\n                cancelText=\"取消\"\n                okType=\"danger\"\n              >\n                <Button\n                  danger\n                  icon={<DeleteOutlined />}\n                >\n                  删除团队\n                </Button>\n              </Popconfirm>\n            </Space>\n          )\n        }\n      >\n        <TeamDetailContent\n          teamDetail={selectedTeam}\n          loading={false}\n          onRefresh={() => {\n            fetchTeams();\n            // 刷新当前选中的团队详情\n            if (selectedTeam) {\n              TeamService.getTeamDetail(selectedTeam.id).then(detail => {\n                setSelectedTeam(detail);\n              }).catch(error => {\n                console.error('刷新团队详情失败:', error);\n              });\n            }\n          }}\n        />\n      </Card>\n    );\n  }\n\n  // 默认显示团队列表\n  return (\n    <Card\n      title={\n        <Space>\n          <TeamOutlined />\n          我的团队\n        </Space>\n      }\n      extra={\n        <Space>\n          <Button\n            type=\"primary\"\n            icon={<PlusOutlined />}\n            onClick={() => setCreateModalVisible(true)}\n          >\n            创建团队\n          </Button>\n          <Button\n            onClick={fetchTeams}\n            loading={loading}\n          >\n            刷新\n          </Button>\n        </Space>\n      }\n    >\n      {teams.length === 0 && !loading ? (\n        <Empty\n          image={Empty.PRESENTED_IMAGE_SIMPLE}\n          description=\"您还没有创建或加入任何团队\"\n        >\n          <Button \n            type=\"primary\" \n            icon={<PlusOutlined />}\n            onClick={() => setCreateModalVisible(true)}\n          >\n            创建第一个团队\n          </Button>\n        </Empty>\n      ) : (\n        <List\n          loading={loading}\n          itemLayout=\"horizontal\"\n          dataSource={teams}\n          renderItem={(team) => (\n            <List.Item\n              actions={[\n                <Button\n                  key=\"view\"\n                  type=\"primary\"\n                  size=\"small\"\n                  onClick={() => handleViewTeamDetail(team)}\n                >\n                  查看详情\n                </Button>\n              ]}\n            >\n              <List.Item.Meta\n                avatar={\n                  <Avatar \n                    icon={<TeamOutlined />} \n                    style={{ backgroundColor: '#1890ff' }}\n                  />\n                }\n                title={\n                  <Space>\n                    {team.name}\n                    {team.isCreator && (\n                      <Tag color=\"gold\" icon={<CrownOutlined />}>\n                        创建者\n                      </Tag>\n                    )}\n                  </Space>\n                }\n                description={\n                  <div>\n                    <div>{team.description || '暂无描述'}</div>\n                    <div style={{ marginTop: 4, color: '#666' }}>\n                      <Space>\n                        <span>\n                          <UserOutlined /> {team.memberCount} 名成员\n                        </span>\n                        <span>创建于 {new Date(team.createdAt).toLocaleDateString()}</span>\n                      </Space>\n                    </div>\n                  </div>\n                }\n              />\n            </List.Item>\n          )}\n        />\n      )}\n\n      {/* 创建团队弹窗 */}\n      <Modal\n        title=\"创建团队\"\n        open={createModalVisible}\n        onCancel={() => {\n          setCreateModalVisible(false);\n          createForm.resetFields();\n        }}\n        footer={null}\n      >\n        <Form\n          form={createForm}\n          layout=\"vertical\"\n          onFinish={handleCreateTeam}\n        >\n          <Form.Item\n            label=\"团队名称\"\n            name=\"name\"\n            rules={[\n              { required: true, message: '请输入团队名称' },\n              { max: 100, message: '团队名称不能超过100个字符' }\n            ]}\n          >\n            <Input placeholder=\"请输入团队名称\" />\n          </Form.Item>\n\n          <Form.Item\n            label=\"团队描述\"\n            name=\"description\"\n            rules={[\n              { max: 500, message: '团队描述不能超过500个字符' }\n            ]}\n          >\n            <TextArea \n              rows={4} \n              placeholder=\"请输入团队描述（可选）\" \n            />\n          </Form.Item>\n\n          <Form.Item>\n            <Space>\n              <Button type=\"primary\" htmlType=\"submit\">\n                创建团队\n              </Button>\n              <Button onClick={() => {\n                setCreateModalVisible(false);\n                createForm.resetFields();\n              }}>\n                取消\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 编辑团队弹窗 */}\n      <Modal\n        title=\"编辑团队\"\n        open={editModalVisible}\n        onCancel={() => {\n          setEditModalVisible(false);\n          setEditingTeam(null);\n          editForm.resetFields();\n        }}\n        footer={null}\n      >\n        <Form\n          form={editForm}\n          layout=\"vertical\"\n          onFinish={handleUpdateTeam}\n        >\n          <Form.Item\n            label=\"团队名称\"\n            name=\"name\"\n            rules={[\n              { required: true, message: '请输入团队名称' },\n              { max: 100, message: '团队名称不能超过100个字符' }\n            ]}\n          >\n            <Input placeholder=\"请输入团队名称\" />\n          </Form.Item>\n\n          <Form.Item\n            label=\"团队描述\"\n            name=\"description\"\n            rules={[\n              { max: 500, message: '团队描述不能超过500个字符' }\n            ]}\n          >\n            <TextArea \n              rows={4} \n              placeholder=\"请输入团队描述（可选）\" \n            />\n          </Form.Item>\n\n          <Form.Item>\n            <Space>\n              <Button type=\"primary\" htmlType=\"submit\">\n                保存修改\n              </Button>\n              <Button onClick={() => {\n                setEditModalVisible(false);\n                setEditingTeam(null);\n                editForm.resetFields();\n              }}>\n                取消\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </Card>\n  );\n};\n\nexport default TeamManageContent;\n", "/**\n * 个人中心页面 - 整合个人资料、团队管理、订阅管理\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Tabs,\n  Typography,\n  Space,\n  message\n} from 'antd';\nimport {\n  UserOutlined,\n  TeamOutlined,\n  CrownOutlined,\n  UsergroupAddOutlined\n} from '@ant-design/icons';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { useModel } from '@umijs/max';\nimport { SubscriptionService, TeamService } from '@/services';\nimport type { SubscriptionResponse, TeamDetailResponse } from '@/types/api';\n\n// 导入各个模块的内容组件\nimport UserProfileContent from '../user/components/UserProfileContent';\nimport UnifiedSubscriptionContent from '../subscription/components/UnifiedSubscriptionContent';\nimport TeamManageContent from './components/TeamManageContent';\nimport FriendManageContent from './components/FriendManageContent';\n\nconst { Title } = Typography;\n\nconst PersonalCenterPage: React.FC = () => {\n  const [activeTab, setActiveTab] = useState('profile');\n  const [currentSubscription, setCurrentSubscription] = useState<SubscriptionResponse | null>(null);\n  const [subscriptionLoading, setSubscriptionLoading] = useState(false);\n  const [currentTeamDetail, setCurrentTeamDetail] = useState<TeamDetailResponse | null>(null);\n  const [teamLoading, setTeamLoading] = useState(false);\n\n  const { initialState } = useModel('@@initialState');\n  const currentTeam = initialState?.currentTeam;\n\n  // 获取当前订阅信息\n  const fetchCurrentSubscription = async () => {\n    try {\n      setSubscriptionLoading(true);\n      const subscription = await SubscriptionService.getCurrentSubscription();\n      setCurrentSubscription(subscription);\n    } catch (error) {\n      console.error('获取当前订阅失败:', error);\n    } finally {\n      setSubscriptionLoading(false);\n    }\n  };\n\n  // 获取当前团队详情\n  const fetchCurrentTeamDetail = async () => {\n    if (!currentTeam?.id) return;\n\n    try {\n      setTeamLoading(true);\n      const teamDetail = await TeamService.getTeamDetail(currentTeam.id);\n      setCurrentTeamDetail(teamDetail);\n    } catch (error) {\n      console.error('获取团队详情失败:', error);\n    } finally {\n      setTeamLoading(false);\n    }\n  };\n\n  // 处理订阅变化\n  const handleSubscriptionChange = () => {\n    fetchCurrentSubscription();\n  };\n\n  // 根据当前选中的标签页加载对应数据\n  React.useEffect(() => {\n    if (activeTab.startsWith('subscription')) {\n      fetchCurrentSubscription();\n    } else if (activeTab === 'team-detail') {\n      fetchCurrentTeamDetail();\n    }\n  }, [activeTab, currentTeam?.id]);\n\n  const tabItems = [\n    {\n      key: 'profile',\n      label: (\n        <Space>\n          <UserOutlined />\n          个人资料\n        </Space>\n      ),\n      children: <UserProfileContent />\n    },\n    {\n      key: 'teams',\n      label: (\n        <Space>\n          <TeamOutlined />\n          我的团队\n        </Space>\n      ),\n      children: <TeamManageContent />\n    },\n    {\n      key: 'friends',\n      label: (\n        <Space>\n          <UsergroupAddOutlined />\n          好友管理\n        </Space>\n      ),\n      children: <FriendManageContent />\n    },\n    {\n      key: 'subscription',\n      label: (\n        <Space>\n          <CrownOutlined />\n          订阅管理\n        </Space>\n      ),\n      children: (\n        <UnifiedSubscriptionContent\n          currentSubscription={currentSubscription}\n          loading={subscriptionLoading}\n          onRefresh={fetchCurrentSubscription}\n        />\n      )\n    }\n  ];\n\n  return (\n    <PageContainer title=\"个人中心\">\n      <Card>\n        <Tabs\n          activeKey={activeTab}\n          onChange={setActiveTab}\n          items={tabItems}\n          size=\"large\"\n          tabPosition=\"left\"\n        />\n      </Card>\n    </PageContainer>\n  );\n};\n\nexport default PersonalCenterPage;\n", "/**\n * 团队详情内容组件\n */\n\nimport React, { useState } from 'react';\nimport {\n  Descriptions,\n  Button,\n  Space,\n  Typography,\n  message,\n  Modal,\n  Form,\n  Input,\n  Spin,\n  Divider,\n  Empty\n} from 'antd';\nimport {\n  TeamOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  ExclamationCircleOutlined\n} from '@ant-design/icons';\nimport { TeamService, AuthService } from '@/services';\nimport type { TeamDetailResponse, UpdateTeamRequest } from '@/types/api';\nimport { history, useModel } from '@umijs/max';\nimport TeamMemberList from './TeamMemberList';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\n\ninterface TeamDetailContentProps {\n  teamDetail: TeamDetailResponse | null;\n  loading: boolean;\n  onRefresh: () => void;\n}\n\nconst TeamDetailContent: React.FC<TeamDetailContentProps> = ({\n  teamDetail,\n  loading,\n  onRefresh\n}) => {\n  const [editModalVisible, setEditModalVisible] = useState(false);\n  const [updating, setUpdating] = useState(false);\n  const [deleting, setDeleting] = useState(false);\n  const [form] = Form.useForm();\n  const { setInitialState } = useModel('@@initialState');\n\n  /**\n   * 处理编辑团队信息操作\n   *\n   * 执行步骤：\n   * 1. 验证团队详情数据存在\n   * 2. 将当前团队信息填充到表单中\n   * 3. 显示编辑模态框\n   */\n  const handleEdit = () => {\n    if (!teamDetail) return;\n    // 将当前团队信息填充到表单中\n    form.setFieldsValue({\n      name: teamDetail.name,\n      description: teamDetail.description,\n    });\n    setEditModalVisible(true);\n  };\n\n  /**\n   * 处理团队信息更新操作\n   *\n   * 执行流程：\n   * 1. 验证团队详情数据存在\n   * 2. 设置更新状态，显示加载动画\n   * 3. 构造更新请求数据\n   * 4. 调用API更新团队信息\n   * 5. 关闭编辑模态框并显示成功消息\n   * 6. 刷新团队详情数据\n   * 7. 处理错误情况\n   *\n   * @param values 表单提交的值，包含团队名称和描述\n   */\n  const handleUpdate = async (values: any) => {\n    if (!teamDetail) return;\n\n    try {\n      setUpdating(true);\n      // 构造更新请求数据\n      const updateData: UpdateTeamRequest = {\n        name: values.name,\n        description: values.description,\n      };\n\n      await TeamService.updateCurrentTeam(updateData);\n      setEditModalVisible(false);\n      message.success('团队信息更新成功');\n      onRefresh(); // 刷新团队详情\n    } catch (error) {\n      console.error('更新团队信息失败:', error);\n      message.error('更新团队信息失败，请稍后重试');\n    } finally {\n      setUpdating(false);\n    }\n  };\n\n  /**\n   * 处理删除团队操作\n   *\n   * 执行流程：\n   * 1. 显示确认对话框，详细说明删除后果\n   * 2. 用户确认后调用删除API\n   * 3. 清除本地团队状态和Token\n   * 4. 跳转到团队选择页面\n   * 5. 处理错误情况\n   *\n   * 安全措施：\n   * - 只有团队创建者可以看到删除按钮\n   * - 二次确认防止误操作\n   * - 详细说明删除后果\n   */\n  const handleDelete = () => {\n    if (!teamDetail) return;\n\n    Modal.confirm({\n      title: '确认删除团队',\n      icon: <ExclamationCircleOutlined />,\n      content: (\n        <div>\n          <p>您确定要删除团队 <strong>\"{teamDetail.name}\"</strong> 吗？</p>\n          <p style={{ color: '#ff4d4f', marginBottom: 0 }}>\n            ⚠️ 此操作不可撤销，删除后将：\n          </p>\n          <ul style={{ color: '#ff4d4f', marginTop: 8, paddingLeft: 20 }}>\n            <li>永久删除团队及所有相关数据</li>\n            <li>移除所有团队成员</li>\n            <li>无法恢复团队信息</li>\n          </ul>\n        </div>\n      ),\n      okText: '确认删除',\n      okType: 'danger',\n      cancelText: '取消',\n      confirmLoading: deleting,\n      onOk: async () => {\n        try {\n          setDeleting(true);\n          await TeamService.deleteCurrentTeam();\n          message.success('团队删除成功');\n\n          // 清除当前团队状态并跳转到团队选择页面\n          await setInitialState((s) => ({ ...s, currentTeam: null }));\n          AuthService.clearTeamToken();\n          history.push('/user/team-select');\n        } catch (error) {\n          console.error('删除团队失败:', error);\n          message.error('删除团队失败，请稍后重试');\n        } finally {\n          setDeleting(false);\n        }\n      },\n    });\n  };\n\n  if (loading) {\n    return (\n      <div style={{ textAlign: 'center', padding: '50px 0' }}>\n        <Spin size=\"large\" />\n      </div>\n    );\n  }\n\n  if (!teamDetail) {\n    return (\n      <Empty\n        image={Empty.PRESENTED_IMAGE_SIMPLE}\n        description=\"请先选择一个团队\"\n      />\n    );\n  }\n\n  return (\n    <div>\n      {/* 团队基本信息 */}\n      <div style={{ marginBottom: 24 }}>\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>\n          <Space>\n            <TeamOutlined style={{ fontSize: 24, color: '#1890ff' }} />\n            <Title level={3} style={{ margin: 0 }}>\n              {teamDetail.name}\n            </Title>\n            {teamDetail.isCreator && (\n              <Text type=\"secondary\" style={{ fontSize: 14 }}>\n                (管理员)\n              </Text>\n            )}\n          </Space>\n          {teamDetail.isCreator && (\n            <Space>\n              <Button\n                icon={<EditOutlined />}\n                onClick={handleEdit}\n              >\n                编辑团队\n              </Button>\n              <Button\n                danger\n                icon={<DeleteOutlined />}\n                onClick={handleDelete}\n                loading={deleting}\n              >\n                删除团队\n              </Button>\n            </Space>\n          )}\n        </div>\n\n        <Descriptions column={2} bordered>\n          <Descriptions.Item label=\"团队名称\">\n            {teamDetail.name}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"成员数量\">\n            {teamDetail.memberCount} 人\n          </Descriptions.Item>\n          <Descriptions.Item label=\"创建时间\">\n            {new Date(teamDetail.createdAt).toLocaleString()}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"更新时间\">\n            {new Date(teamDetail.updatedAt).toLocaleString()}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"团队描述\" span={2}>\n            {teamDetail.description || '暂无描述'}\n          </Descriptions.Item>\n        </Descriptions>\n      </div>\n\n      <Divider />\n\n      {/* 团队成员列表 */}\n      <TeamMemberList teamId={teamDetail.id} isCreator={teamDetail.isCreator} />\n\n      {/* 编辑团队模态框 */}\n      <Modal\n        title=\"编辑团队信息\"\n        open={editModalVisible}\n        onCancel={() => setEditModalVisible(false)}\n        footer={null}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleUpdate}\n        >\n          <Form.Item\n            label=\"团队名称\"\n            name=\"name\"\n            rules={[\n              { required: true, message: '请输入团队名称' },\n              { min: 2, max: 50, message: '团队名称长度应在2-50个字符之间' }\n            ]}\n          >\n            <Input placeholder=\"请输入团队名称\" />\n          </Form.Item>\n\n          <Form.Item\n            label=\"团队描述\"\n            name=\"description\"\n            rules={[\n              { max: 200, message: '团队描述不能超过200个字符' }\n            ]}\n          >\n            <TextArea\n              rows={4}\n              placeholder=\"请输入团队描述（可选）\"\n              showCount\n              maxLength={200}\n            />\n          </Form.Item>\n\n          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>\n            <Space>\n              <Button onClick={() => setEditModalVisible(false)}>\n                取消\n              </Button>\n              <Button type=\"primary\" htmlType=\"submit\" loading={updating}>\n                保存\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n\n    </div>\n  );\n};\n\nexport default TeamDetailContent;\n", "/**\n * 团队成员列表组件\n *\n * 功能特性：\n * - 展示团队所有成员信息（头像、姓名、邮箱、角色、状态等）\n * - 支持成员搜索和筛选功能\n * - 提供成员管理操作（移除成员、角色变更等）\n * - 区分创建者和普通成员的权限显示\n * - 响应式表格设计，适配不同屏幕尺寸\n *\n * 权限控制：\n * - 只有团队创建者可以看到管理操作按钮\n * - 创建者不能移除自己\n * - 普通成员只能查看成员列表\n *\n * 交互设计：\n * - 支持批量操作（预留功能）\n * - 提供详细的操作确认对话框\n * - 实时更新成员状态和数量\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Avatar,\n  Tag,\n  Button,\n  Space,\n  message,\n  Modal,\n  Input,\n  Tooltip,\n  Dropdown,\n  Checkbox,\n  Typography,\n  Badge,\n  Divider,\n  Select\n} from 'antd';\nimport {\n  UserOutlined,\n  CrownOutlined,\n  DeleteOutlined,\n  SearchOutlined,\n  MoreOutlined,\n  UserSwitchOutlined,\n  StopOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined,\n  FilterOutlined\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport type { MenuProps } from 'antd';\nimport { TeamService } from '@/services';\nimport type { TeamMemberResponse } from '@/types/api';\n\nconst { Text } = Typography;\nconst { Option } = Select;\n\n/**\n * 团队成员列表组件的Props接口\n */\ninterface TeamMemberListProps {\n  /** 团队ID，用于获取成员列表 */\n  teamId: number;\n  /** 当前用户是否为团队创建者，控制管理功能的显示 */\n  isCreator: boolean;\n  /** 成员变更时的回调函数，用于通知父组件刷新数据 */\n  onMemberChange?: () => void;\n}\n\nconst TeamMemberList: React.FC<TeamMemberListProps> = ({\n  teamId,\n  isCreator,\n  onMemberChange,\n}) => {\n  const [loading, setLoading] = useState(true);\n  const [members, setMembers] = useState<TeamMemberResponse[]>([]);\n  const [searchText, setSearchText] = useState('');\n  const [filteredMembers, setFilteredMembers] = useState<TeamMemberResponse[]>([]);\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [statusFilter, setStatusFilter] = useState<string>('all');\n\n  useEffect(() => {\n    fetchMembers();\n  }, [teamId]);\n\n  /**\n   * 成员列表过滤效果\n   *\n   * 过滤条件：\n   * 1. 搜索文本：匹配成员姓名或邮箱（不区分大小写）\n   * 2. 状态筛选：全部/活跃/非活跃/创建者/普通成员\n   *\n   * 安全性：\n   * - 添加空值检查，防止数据异常导致的错误\n   * - 确保成员对象的必要属性存在\n   */\n  useEffect(() => {\n    // 过滤成员列表 - 添加空值检查\n    if (!members || !Array.isArray(members)) {\n      setFilteredMembers([]);\n      return;\n    }\n\n    const filtered = members.filter(member => {\n      // 确保member对象存在且有必要的属性\n      if (!member || !member.name || !member.email) {\n        return false;\n      }\n\n      // 搜索文本匹配（姓名或邮箱）\n      const matchesSearch = !searchText ||\n        member.name.toLowerCase().includes(searchText.toLowerCase()) ||\n        member.email.toLowerCase().includes(searchText.toLowerCase());\n\n      // 状态筛选匹配\n      const matchesStatus = statusFilter === 'all' ||\n        (statusFilter === 'active' && member.isActive) ||\n        (statusFilter === 'inactive' && !member.isActive) ||\n        (statusFilter === 'creator' && member.isCreator) ||\n        (statusFilter === 'member' && !member.isCreator);\n\n      return matchesSearch && matchesStatus;\n    });\n    setFilteredMembers(filtered);\n  }, [members, searchText, statusFilter]);\n\n  /**\n   * 获取团队成员列表\n   *\n   * 功能：\n   * - 调用API获取当前团队的所有成员\n   * - 设置加载状态，提供用户反馈\n   * - 处理错误情况，确保组件稳定性\n   *\n   * 数据处理：\n   * - 确保返回数据为数组格式，防止渲染错误\n   * - 错误时设置空数组，保持组件正常显示\n   */\n  const fetchMembers = async () => {\n    try {\n      setLoading(true);\n      const response = await TeamService.getTeamMembers({ current: 1, pageSize: 1000 });\n      // 确保返回的数据是数组格式，防止渲染错误\n      setMembers(response?.list || []);\n    } catch (error) {\n      console.error('获取团队成员失败:', error);\n      message.error('获取团队成员失败');\n      // 出错时设置为空数组，保持组件正常显示\n      setMembers([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleRemoveMember = (member: TeamMemberResponse) => {\n    if (member.isCreator) {\n      message.warning('不能移除团队创建者');\n      return;\n    }\n\n    Modal.confirm({\n      title: '确认移除成员',\n      content: `确定要移除成员 \"${member.name}\" 吗？`,\n      okText: '确认',\n      cancelText: '取消',\n      onOk: async () => {\n        try {\n          await TeamService.removeMember(member.id);\n          message.success('成员移除成功');\n          fetchMembers();\n          onMemberChange?.();\n        } catch (error) {\n          console.error('移除成员失败:', error);\n        }\n      },\n    });\n  };\n\n  const handleBatchRemove = () => {\n    const selectedMembers = members.filter(member =>\n      selectedRowKeys.includes(member.id) && !member.isCreator\n    );\n\n    if (selectedMembers.length === 0) {\n      message.warning('请选择要移除的成员');\n      return;\n    }\n\n    Modal.confirm({\n      title: '批量移除成员',\n      content: `确定要移除选中的 ${selectedMembers.length} 名成员吗？`,\n      okText: '确认',\n      cancelText: '取消',\n      onOk: async () => {\n        try {\n          await Promise.all(\n            selectedMembers.map(member => TeamService.removeMember(member.id))\n          );\n          message.success(`成功移除 ${selectedMembers.length} 名成员`);\n          setSelectedRowKeys([]);\n          fetchMembers();\n          onMemberChange?.();\n        } catch (error) {\n          console.error('批量移除成员失败:', error);\n          message.error('批量移除失败');\n        }\n      },\n    });\n  };\n\n  const columns: ColumnsType<TeamMemberResponse> = [\n    {\n      title: '成员',\n      dataIndex: 'name',\n      key: 'name',\n      render: (name, record) => (\n        <Space>\n          <Avatar size=\"small\" icon={<UserOutlined />} />\n          <div>\n            <div>{name}</div>\n            <div style={{ fontSize: 12, color: '#999' }}>{record.email}</div>\n          </div>\n        </Space>\n      ),\n    },\n    {\n      title: '角色',\n      dataIndex: 'isCreator',\n      key: 'role',\n      width: 100,\n      render: (isCreator) => (\n        <Tag color={isCreator ? 'gold' : 'blue'} icon={isCreator ? <CrownOutlined /> : <UserOutlined />}>\n          {isCreator ? '创建者' : '成员'}\n        </Tag>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'isActive',\n      key: 'status',\n      width: 80,\n      render: (isActive) => (\n        <Tag color={isActive ? 'green' : 'red'}>\n          {isActive ? '活跃' : '停用'}\n        </Tag>\n      ),\n    },\n    {\n      title: '加入时间',\n      dataIndex: 'assignedAt',\n      key: 'assignedAt',\n      width: 150,\n      render: (assignedAt) => new Date(assignedAt).toLocaleDateString(),\n    },\n    {\n      title: '最后访问',\n      dataIndex: 'lastAccessTime',\n      key: 'lastAccessTime',\n      width: 150,\n      render: (lastAccessTime) => {\n        const date = new Date(lastAccessTime);\n        const now = new Date();\n        const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));\n        \n        let color = 'green';\n        if (diffDays > 7) color = 'orange';\n        if (diffDays > 30) color = 'red';\n        \n        return (\n          <Tooltip title={date.toLocaleString()}>\n            <Tag color={color}>\n              {diffDays === 0 ? '今天' : `${diffDays}天前`}\n            </Tag>\n          </Tooltip>\n        );\n      },\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 120,\n      render: (_, record) => {\n        if (!isCreator || record.isCreator) {\n          return <Text type=\"secondary\">-</Text>;\n        }\n\n        const menuItems: MenuProps['items'] = [\n          {\n            key: 'remove',\n            label: '移除成员',\n            icon: <DeleteOutlined />,\n            danger: true,\n            onClick: () => handleRemoveMember(record),\n          },\n        ];\n\n        return (\n          <Space size=\"small\">\n            <Button\n              type=\"text\"\n              danger\n              size=\"small\"\n              icon={<DeleteOutlined />}\n              onClick={() => handleRemoveMember(record)}\n            >\n              移除\n            </Button>\n            <Dropdown menu={{ items: menuItems }} trigger={['click']}>\n              <Button\n                type=\"text\"\n                size=\"small\"\n                icon={<MoreOutlined />}\n              />\n            </Dropdown>\n          </Space>\n        );\n      },\n    },\n  ];\n\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: (newSelectedRowKeys: React.Key[]) => {\n      setSelectedRowKeys(newSelectedRowKeys);\n    },\n    getCheckboxProps: (record: TeamMemberResponse) => ({\n      disabled: record.isCreator, // 创建者不能被选中\n    }),\n  };\n\n  return (\n    <Card\n      title={\n        <Space>\n          <Text strong>团队成员</Text>\n          <Badge count={filteredMembers.length} showZero />\n        </Space>\n      }\n      extra={\n        <Space>\n          <Select\n            value={statusFilter}\n            onChange={setStatusFilter}\n            style={{ width: 120 }}\n            size=\"small\"\n          >\n            <Option value=\"all\">全部</Option>\n            <Option value=\"active\">活跃</Option>\n            <Option value=\"inactive\">停用</Option>\n            <Option value=\"creator\">创建者</Option>\n            <Option value=\"member\">成员</Option>\n          </Select>\n          <Input\n            placeholder=\"搜索成员\"\n            prefix={<SearchOutlined />}\n            value={searchText}\n            onChange={(e) => setSearchText(e.target.value)}\n            style={{ width: 200 }}\n            size=\"small\"\n          />\n        </Space>\n      }\n    >\n      {selectedRowKeys.length > 0 && isCreator && (\n        <div style={{ marginBottom: 16, padding: 12, background: '#f5f5f5', borderRadius: 6 }}>\n          <Space>\n            <Text>已选择 {selectedRowKeys.length} 名成员</Text>\n            <Button\n              size=\"small\"\n              danger\n              icon={<DeleteOutlined />}\n              onClick={handleBatchRemove}\n            >\n              批量移除\n            </Button>\n            <Button\n              size=\"small\"\n              onClick={() => setSelectedRowKeys([])}\n            >\n              取消选择\n            </Button>\n          </Space>\n        </div>\n      )}\n\n      <Table\n        columns={columns}\n        dataSource={filteredMembers}\n        rowKey=\"id\"\n        loading={loading}\n        rowSelection={isCreator ? rowSelection : undefined}\n        pagination={{\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total) => `共 ${total} 名成员`,\n          pageSize: 10,\n        }}\n      />\n    </Card>\n  );\n};\n\nexport default TeamMemberList;\n"], "names": [], "mappings": ";;;AAAA;;CAEC;;;;4BA2fD;;;eAAA;;;;;;wEAzf2C;6BAsBpC;8BAWA;iCACoC;;;;;;;;;;AAG3C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,gBAAU;AAClC,MAAM,EAAE,MAAM,EAAE,GAAG,WAAK;AAExB,MAAM,sBAAgC;;IACpC,QAAQ;IACR,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAC;IAE3C,SAAS;IACT,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAY,EAAE;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAC;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAAC;IAC/C,MAAM,CAAC,SAAS,GAAG,IAAA,eAAQ,EAAC;IAE5B,OAAO;IACP,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAgB;IACxD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;IAC7D,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,IAAA,eAAQ,EAAC;IACnE,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAiB;IAEnE,KAAK;IACL,MAAM,CAAC,WAAW,GAAG,UAAI,CAAC,OAAO;IACjC,MAAM,CAAC,cAAc,GAAG,UAAI,CAAC,OAAO;IAEpC,WAAW;IACX,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAAY,EAAE;IAC5D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,eAAQ,EAAC;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAgB;IAEhE,SAAS;IACT,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,MAAM,aAAa,MAAM,uBAAa,CAAC,UAAU;YACjD,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,QAAQ;IACR,IAAA,gBAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,SAAS;IACT,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA;YACrC,cACA;eADA,EAAA,eAAA,OAAO,IAAI,cAAX,mCAAA,aAAa,WAAW,GAAG,QAAQ,CAAC,cAAc,WAAW,UAC7D,gBAAA,OAAO,KAAK,cAAZ,oCAAA,cAAc,WAAW,GAAG,QAAQ,CAAC,cAAc,WAAW;;IAGhE,OAAO;IACP,MAAM,mBAAmB,gBAAgB,KAAK,CAC5C,AAAC,CAAA,cAAc,CAAA,IAAK,UACpB,cAAc;IAGhB,OAAO;IACP,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,YAAY,OAAO,EAAE;YACrB,MAAM,uBAAa,CAAC,YAAY,CAAC,OAAO,EAAE;YAC1C,aAAO,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC;YACxC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,YAAY;QACd;IACF;IAEA,OAAO;IACP,MAAM,mBAAmB,OAAO;QAC9B,iBAAiB;QACjB,sBAAsB;QAEtB,IAAI;YACF,SAAS;YACT,MAAM,SAAS,MAAM,uBAAa,CAAC,eAAe,CAAC,OAAO,EAAE;YAC5D,WAAW,cAAc,CAAC;gBAAE;YAAO;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,WAAW,cAAc,CAAC;gBAAE,QAAQ;YAAG;QACzC;IACF;IAEA,OAAO;IACP,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,eAAe;QAEpB,IAAI;YACF,MAAM,uBAAa,CAAC,eAAe,CAAC;gBAClC,UAAU,cAAc,EAAE;gBAC1B,QAAQ,OAAO,MAAM;YACvB;YACA,aAAO,CAAC,OAAO,CAAC;YAChB,sBAAsB;YACtB,iBAAiB;YACjB,WAAW,WAAW;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,OAAO;IACP,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,eAAe,EAAE;YACjB;QACF;QAEA,IAAI;YACF,kBAAkB;YAClB,MAAM,QAAQ,MAAM,qBAAW,CAAC,kBAAkB,CAAC;YACnD,cAAc;YACd,MAAM,YAAY,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;YACvC,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAA,OAAQ,CAAC,UAAU,QAAQ,CAAC,KAAK,EAAE;YACvE,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,OAAO;IACP,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,gBAAgB,KAAK,EAAE;YACvB,MAAM,uBAAa,CAAC,iBAAiB,CAAC;gBAAE,OAAO,KAAK,KAAK;YAAC;YAC1D,aAAO,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,QAAQ,CAAC;YAC1C,eAAe,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE;QACzD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,YAAY;IACZ,MAAM,sBAAsB;QAC1B,yBAAyB;QACzB,eAAe,EAAE;QACjB,cAAc,WAAW;IAC3B;IAEA,SAAS;IACT,MAAM,mBAAmB,kBACvB,2BAAC;;8BAEC,2BAAC,SAAG;oBAAC,QAAQ;wBAAC;wBAAI;qBAAG;oBAAE,OAAO;wBAAE,cAAc;oBAAG;;sCAC/C,2BAAC,SAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvB,cAAA,2BAAC;gCACC,aAAY;gCACZ,UAAU;gCACV,OAAO;gCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gCAChD,sBAAQ,2BAAC,qBAAc;;;;;;;;;;;;;;;sCAG3B,2BAAC,SAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;4BAAI,OAAO;gCAAE,WAAW;4BAAQ;sCACvD,cAAA,2BAAC,WAAK;;kDACJ,2BAAC,YAAM;wCACL,MAAK;wCACL,oBAAM,2BAAC,sBAAe;;;;;wCACtB,SAAS;kDACV;;;;;;kDAGD,2BAAC,YAAM;wCACL,oBAAM,2BAAC,qBAAc;;;;;wCACrB,SAAS;wCACT,SAAS;kDACV;;;;;;;;;;;;;;;;;;;;;;;8BAQP,2BAAC,UAAI;oBAAC,MAAK;oBAAQ,OAAO;wBAAE,cAAc;oBAAG;8BAC3C,cAAA,2BAAC,WAAK;wBAAC,qBAAO,2BAAC,aAAO;4BAAC,MAAK;;;;;;;0CAC1B,2BAAC;;kDACC,2BAAC;kDAAO;;;;;;kDACR,2BAAC;wCAAK,MAAK;kDAAW,QAAQ,MAAM;;;;;;;;;;;;0CAEtC,2BAAC;;kDACC,2BAAC;kDAAO;;;;;;kDACR,2BAAC;wCAAK,MAAK;kDAAW,gBAAgB,MAAM;;;;;;;;;;;;;;;;;;;;;;;8BAMlD,2BAAC,UAAI;8BACH,cAAA,2BAAC,UAAI;wBAAC,UAAU;;0CACd,2BAAC,UAAI;gCACH,YAAY;gCACZ,YAAY,CAAC;wCA0CF;yDAzCT,2BAAC,UAAI,CAAC,IAAI;wCACR,SAAS;0DACP,2BAAC,aAAO;gDAAc,OAAM;0DAC1B,cAAA,2BAAC,YAAM;oDACL,MAAK;oDACL,oBAAM,2BAAC,mBAAY;;;;;oDACnB,SAAS,IAAM,iBAAiB;oDAChC,MAAK;8DACN;;;;;;+CANU;;;;;0DAUb,2BAAC,gBAAU;gDAET,OAAM;gDACN,aAAa,CAAC,SAAS,EAAE,OAAO,IAAI,CAAC,IAAI,CAAC;gDAC1C,oBAAM,2BAAC,gCAAyB;oDAAC,OAAO;wDAAE,OAAO;oDAAM;;;;;;gDACvD,WAAW,IAAM,mBAAmB;gDACpC,QAAO;gDACP,YAAW;gDACX,QAAO;0DAEP,cAAA,2BAAC,YAAM;oDACL,MAAK;oDACL,MAAM;oDACN,oBAAM,2BAAC,qBAAc;;;;;oDACrB,SAAS,aAAa,OAAO,EAAE;oDAC/B,MAAK;8DACN;;;;;;+CAfG;;;;;yCAmBP;kDAED,cAAA,2BAAC,UAAI,CAAC,IAAI,CAAC,IAAI;4CACb,sBACE,2BAAC,YAAM;gDACL,MAAM;gDACN,oBAAM,2BAAC,mBAAY;;;;;gDACnB,OAAO;oDAAE,iBAAiB;gDAAU;2DAEnC,eAAA,OAAO,IAAI,cAAX,mCAAA,aAAa,MAAM,CAAC,GAAG,WAAW;;;;;;4CAGvC,qBACE,2BAAC,WAAK;;kEACJ,2BAAC;wDAAK,MAAM;kEAAE,OAAO,IAAI;;;;;;oDACxB,OAAO,MAAM,kBACZ,2BAAC;wDAAK,MAAK;wDAAY,OAAO;4DAAE,UAAU;wDAAG;;4DAAG;4DAC5C,OAAO,MAAM;4DAAC;;;;;;;;;;;;;4CAKxB,2BACE,2BAAC,WAAK;gDAAC,WAAU;gDAAW,MAAM;;kEAChC,2BAAC;wDAAK,MAAK;kEAAa,OAAO,KAAK;;;;;;kEACpC,2BAAC;wDAAK,MAAK;wDAAY,OAAO;4DAAE,UAAU;wDAAG;;4DAAG;4DACvC,OAAO,SAAS,GAAG,IAAI,KAAK,OAAO,SAAS,EAAE,kBAAkB,KAAK;;;;;;;;;;;;;;;;;;;;;;;;gCAOxF,QAAQ;oCACN,yBACE,2BAAC,WAAK;wCACJ,OAAO,WAAK,CAAC,sBAAsB;wCACnC,aACE,gBACI,CAAC,QAAQ,EAAE,cAAc,KAAK,CAAC,GAC/B;;;;;;gCAIZ;;;;;;4BAID,gBAAgB,MAAM,GAAG,0BACxB,2BAAC;gCAAI,OAAO;oCAAE,WAAW;oCAAU,WAAW;gCAAG;0CAC/C,cAAA,2BAAC,gBAAU;oCACT,SAAS;oCACT,UAAU;oCACV,OAAO,gBAAgB,MAAM;oCAC7B,UAAU;oCACV,iBAAiB;oCACjB,eAAe;oCACf,WAAW,CAAC,OAAO,QACjB,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAU1D,SAAS;IACT,MAAM,kBAAkB,kBACtB,2BAAC,UAAI;;8BACH,2BAAC;oBAAM,OAAO;8BAAG;;;;;;8BACjB,2BAAC,UAAI;oBACH,MAAM;oBACN,QAAO;oBACP,UAAU,CAAC,SAAW,kBAAkB,OAAO,KAAK;8BAEpD,cAAA,2BAAC,UAAI,CAAC,IAAI;wBACR,OAAM;wBACN,MAAK;wBACL,OAAO;4BACL;gCAAE,UAAU;gCAAM,SAAS;4BAAQ;4BACnC;gCAAE,MAAM;gCAAS,SAAS;4BAAa;yBACxC;kCAED,cAAA,2BAAC;4BACC,aAAY;4BACZ,aAAY;4BACZ,SAAS;4BACT,UAAU;;;;;;;;;;;;;;;;gBAMf,YAAY,MAAM,GAAG,mBACpB,2BAAC;oBAAI,OAAO;wBAAE,WAAW;oBAAG;;sCAC1B,2BAAC;4BAAM,OAAO;sCAAG;;;;;;sCACjB,2BAAC,UAAI;4BACH,YAAY;4BACZ,YAAY,CAAC;oCAsBF;qDArBT,2BAAC,UAAI,CAAC,IAAI;oCACR,SAAS;sDACP,2BAAC,YAAM;4CAEL,MAAK;4CACL,oBAAM,2BAAC,sBAAe;;;;;4CACtB,SAAS,iBAAiB,KAAK,EAAE;4CACjC,SAAS,IAAM,gBAAgB;4CAC/B,MAAK;sDACN;2CANK;;;;;qCASP;8CAED,cAAA,2BAAC,UAAI,CAAC,IAAI,CAAC,IAAI;wCACb,sBACE,2BAAC,YAAM;4CACL,MAAM;4CACN,oBAAM,2BAAC,mBAAY;;;;;4CACnB,OAAO;gDAAE,iBAAiB;4CAAU;uDAEnC,aAAA,KAAK,IAAI,cAAT,iCAAA,WAAW,MAAM,CAAC,GAAG,WAAW;;;;;;wCAGrC,OAAO,KAAK,IAAI;wCAChB,aAAa,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;gBAQlC,YAAY,MAAM,KAAK,KAAK,cAAc,aAAa,CAAC,YAAY,CAAC,gCACpE,2BAAC,WAAK;oBACJ,OAAO,WAAK,CAAC,sBAAsB;oBACnC,aAAY;oBACZ,OAAO;wBAAE,WAAW;oBAAG;;;;;;;;;;;;IAM/B,MAAM,WAAW;QACf;YACE,KAAK;YACL,qBACE,2BAAC,WAAK;;kCACJ,2BAAC,4BAAqB;;;;;oBAAG;kCAEzB,2BAAC,WAAK;wBAAC,OAAO,QAAQ,MAAM;wBAAE,QAAQ;;;;;;;;;;;;YAG1C,UAAU;QACZ;QACA;YACE,KAAK;YACL,qBACE,2BAAC,WAAK;;kCACJ,2BAAC,sBAAe;;;;;oBAAG;;;;;;;YAIvB,UAAU;QACZ;KACD;IAED,qBACE,2BAAC;;0BACC,2BAAC,UAAI;gBACH,WAAW;gBACX,UAAU;gBACV,OAAO;gBACP,MAAK;;;;;;0BAIP,2BAAC,WAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU;oBACR,sBAAsB;oBACtB,iBAAiB;oBACjB,WAAW,WAAW;gBACxB;gBACA,QAAQ;gBACR,OAAO;0BAEP,cAAA,2BAAC,UAAI;oBACH,MAAM;oBACN,QAAO;oBACP,UAAU;;sCAEV,2BAAC,UAAI,CAAC,IAAI;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO;gCACL;oCAAE,KAAK;oCAAI,SAAS;gCAAgB;6BACrC;sCAED,cAAA,2BAAC,WAAK,CAAC,QAAQ;gCACb,aAAY;gCACZ,MAAM;gCACN,WAAW;gCACX,SAAS;;;;;;;;;;;sCAGb,2BAAC,UAAI,CAAC,IAAI;4BAAC,OAAO;gCAAE,cAAc;gCAAG,WAAW;4BAAQ;sCACtD,cAAA,2BAAC,WAAK;;kDACJ,2BAAC,YAAM;wCAAC,SAAS;4CACf,sBAAsB;4CACtB,iBAAiB;4CACjB,WAAW,WAAW;wCACxB;kDAAG;;;;;;kDAGH,2BAAC,YAAM;wCAAC,MAAK;wCAAU,UAAS;kDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvD;GA/cM;;QAkBiB,UAAI,CAAC;QACF,UAAI,CAAC;;;KAnBzB;IAidN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7ff;;CAEC;;;;4BAiaD;;;eAAA;;;;;;;wEA/Z2C;6BAcpC;8BASA;iCAEqB;mFAEE;;;;;;;;;;AAE9B,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAK;AAE1B,MAAM,oBAA8B;;IAClC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAuB,EAAE;IAC3D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAA4B;IAC1E,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAA4B;IAC5E,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAoB;IAC5D,MAAM,CAAC,WAAW,GAAG,UAAI,CAAC,OAAO;IACjC,MAAM,CAAC,SAAS,GAAG,UAAI,CAAC,OAAO;IAE/B,IAAA,gBAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL;;GAEC,GACD,MAAM,aAAa;QACjB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,qBAAW,CAAC,YAAY;YAC/C,SAAS;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA;;GAEC,GACD,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,qBAAW,CAAC,UAAU,CAAC;YAC7B,aAAO,CAAC,OAAO,CAAC;YAChB,sBAAsB;YACtB,WAAW,WAAW;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA;;GAEC,GACD,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,aAAa;QAElB,IAAI;YACF,MAAM,qBAAW,CAAC,UAAU,CAAC,YAAY,EAAE,EAAE;YAC7C,aAAO,CAAC,OAAO,CAAC;YAChB,oBAAoB;YACpB,eAAe;YACf,SAAS,WAAW;YACpB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA;;GAEC,GACD,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,qBAAW,CAAC,UAAU,CAAC,KAAK,EAAE;YACpC,aAAO,CAAC,OAAO,CAAC;YAChB,qBAAqB;YACrB,IAAI,gBAAgB,aAAa,EAAE,KAAK,KAAK,EAAE,EAC7C;YAEF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA;;GAEC,GACD,MAAM,uBAAuB,CAAC;QAC5B,gBAAgB;QAChB,YAAY;IACd;IAEA;;GAEC,GACD,MAAM,mBAAmB;QACvB,gBAAgB;QAChB,YAAY;IACd;IAEA;;GAEC,GACD,MAAM,kBAAkB,CAAC;QACvB,eAAe;QACf,SAAS,cAAc,CAAC;YACtB,MAAM,KAAK,IAAI;YACf,aAAa,KAAK,WAAW;QAC/B;QACA,oBAAoB;IACtB;IAEA,iBAAiB;IACjB,IAAI,aAAa,YAAY,cAC3B,qBACE,2BAAC,UAAI;QACH,qBACE,2BAAC,WAAK;;8BACJ,2BAAC,YAAM;oBACL,oBAAM,2BAAC,wBAAiB;;;;;oBACxB,SAAS;8BACV;;;;;;8BAGD,2BAAC,mBAAY;;;;;gBACZ,aAAa,IAAI;;;;;;;QAGtB,OACE,aAAa,SAAS,kBACpB,2BAAC,WAAK;;8BACJ,2BAAC,YAAM;oBACL,oBAAM,2BAAC,mBAAY;;;;;oBACnB,SAAS,IAAM,gBAAgB;8BAChC;;;;;;8BAGD,2BAAC,gBAAU;oBACT,OAAM;oBACN,aAAa,CAAC,SAAS,EAAE,aAAa,IAAI,CAAC,YAAY,CAAC;oBACxD,WAAW,IAAM,iBAAiB;oBAClC,QAAO;oBACP,YAAW;oBACX,QAAO;8BAEP,cAAA,2BAAC,YAAM;wBACL,MAAM;wBACN,oBAAM,2BAAC,qBAAc;;;;;kCACtB;;;;;;;;;;;;;;;;;kBAQT,cAAA,2BAAC,0BAAiB;YAChB,YAAY;YACZ,SAAS;YACT,WAAW;gBACT;gBACA,cAAc;gBACd,IAAI,cACF,qBAAW,CAAC,aAAa,CAAC,aAAa,EAAE,EAAE,IAAI,CAAC,CAAA;oBAC9C,gBAAgB;gBAClB,GAAG,KAAK,CAAC,CAAA;oBACP,QAAQ,KAAK,CAAC,aAAa;gBAC7B;YAEJ;;;;;;;;;;;IAMR,WAAW;IACX,qBACE,2BAAC,UAAI;QACH,qBACE,2BAAC,WAAK;;8BACJ,2BAAC,mBAAY;;;;;gBAAG;;;;;;;QAIpB,qBACE,2BAAC,WAAK;;8BACJ,2BAAC,YAAM;oBACL,MAAK;oBACL,oBAAM,2BAAC,mBAAY;;;;;oBACnB,SAAS,IAAM,sBAAsB;8BACtC;;;;;;8BAGD,2BAAC,YAAM;oBACL,SAAS;oBACT,SAAS;8BACV;;;;;;;;;;;;;YAMJ,MAAM,MAAM,KAAK,KAAK,CAAC,wBACtB,2BAAC,WAAK;gBACJ,OAAO,WAAK,CAAC,sBAAsB;gBACnC,aAAY;0BAEZ,cAAA,2BAAC,YAAM;oBACL,MAAK;oBACL,oBAAM,2BAAC,mBAAY;;;;;oBACnB,SAAS,IAAM,sBAAsB;8BACtC;;;;;;;;;;qCAKH,2BAAC,UAAI;gBACH,SAAS;gBACT,YAAW;gBACX,YAAY;gBACZ,YAAY,CAAC,qBACX,2BAAC,UAAI,CAAC,IAAI;wBACR,SAAS;0CACP,2BAAC,YAAM;gCAEL,MAAK;gCACL,MAAK;gCACL,SAAS,IAAM,qBAAqB;0CACrC;+BAJK;;;;;yBAOP;kCAED,cAAA,2BAAC,UAAI,CAAC,IAAI,CAAC,IAAI;4BACb,sBACE,2BAAC,YAAM;gCACL,oBAAM,2BAAC,mBAAY;;;;;gCACnB,OAAO;oCAAE,iBAAiB;gCAAU;;;;;;4BAGxC,qBACE,2BAAC,WAAK;;oCACH,KAAK,IAAI;oCACT,KAAK,SAAS,kBACb,2BAAC,SAAG;wCAAC,OAAM;wCAAO,oBAAM,2BAAC,oBAAa;;;;;kDAAK;;;;;;;;;;;;4BAMjD,2BACE,2BAAC;;kDACC,2BAAC;kDAAK,KAAK,WAAW,IAAI;;;;;;kDAC1B,2BAAC;wCAAI,OAAO;4CAAE,WAAW;4CAAG,OAAO;wCAAO;kDACxC,cAAA,2BAAC,WAAK;;8DACJ,2BAAC;;sEACC,2BAAC,mBAAY;;;;;wDAAG;wDAAE,KAAK,WAAW;wDAAC;;;;;;;8DAErC,2BAAC;;wDAAK;wDAAK,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYxE,2BAAC,WAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU;oBACR,sBAAsB;oBACtB,WAAW,WAAW;gBACxB;gBACA,QAAQ;0BAER,cAAA,2BAAC,UAAI;oBACH,MAAM;oBACN,QAAO;oBACP,UAAU;;sCAEV,2BAAC,UAAI,CAAC,IAAI;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO;gCACL;oCAAE,UAAU;oCAAM,SAAS;gCAAU;gCACrC;oCAAE,KAAK;oCAAK,SAAS;gCAAiB;6BACvC;sCAED,cAAA,2BAAC,WAAK;gCAAC,aAAY;;;;;;;;;;;sCAGrB,2BAAC,UAAI,CAAC,IAAI;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO;gCACL;oCAAE,KAAK;oCAAK,SAAS;gCAAiB;6BACvC;sCAED,cAAA,2BAAC;gCACC,MAAM;gCACN,aAAY;;;;;;;;;;;sCAIhB,2BAAC,UAAI,CAAC,IAAI;sCACR,cAAA,2BAAC,WAAK;;kDACJ,2BAAC,YAAM;wCAAC,MAAK;wCAAU,UAAS;kDAAS;;;;;;kDAGzC,2BAAC,YAAM;wCAAC,SAAS;4CACf,sBAAsB;4CACtB,WAAW,WAAW;wCACxB;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASX,2BAAC,WAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU;oBACR,oBAAoB;oBACpB,eAAe;oBACf,SAAS,WAAW;gBACtB;gBACA,QAAQ;0BAER,cAAA,2BAAC,UAAI;oBACH,MAAM;oBACN,QAAO;oBACP,UAAU;;sCAEV,2BAAC,UAAI,CAAC,IAAI;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO;gCACL;oCAAE,UAAU;oCAAM,SAAS;gCAAU;gCACrC;oCAAE,KAAK;oCAAK,SAAS;gCAAiB;6BACvC;sCAED,cAAA,2BAAC,WAAK;gCAAC,aAAY;;;;;;;;;;;sCAGrB,2BAAC,UAAI,CAAC,IAAI;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO;gCACL;oCAAE,KAAK;oCAAK,SAAS;gCAAiB;6BACvC;sCAED,cAAA,2BAAC;gCACC,MAAM;gCACN,aAAY;;;;;;;;;;;sCAIhB,2BAAC,UAAI,CAAC,IAAI;sCACR,cAAA,2BAAC,WAAK;;kDACJ,2BAAC,YAAM;wCAAC,MAAK;wCAAU,UAAS;kDAAS;;;;;;kDAGzC,2BAAC,YAAM;wCAAC,SAAS;4CACf,oBAAoB;4CACpB,eAAe;4CACf,SAAS,WAAW;wCACtB;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjB;GA9XM;;QAQiB,UAAI,CAAC;QACP,UAAI,CAAC;;;KATpB;IAgYN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnaf;;CAEC;;;;4BAiJD;;;eAAA;;;;;;;wEA/I2C;6BAOpC;8BAMA;sCACuB;4BACL;iCACwB;oFAIlB;4FACQ;mFACT;qFACE;;;;;;;;;;AAEhC,MAAM,EAAE,KAAK,EAAE,GAAG,gBAAU;AAE5B,MAAM,qBAA+B;;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAC;IAC3C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,IAAA,eAAQ,EAA8B;IAC5F,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,IAAA,eAAQ,EAAC;IAC/D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,IAAA,eAAQ,EAA4B;IACtF,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAAC;IAE/C,MAAM,EAAE,YAAY,EAAE,GAAG,IAAA,aAAQ,EAAC;IAClC,MAAM,cAAc,yBAAA,mCAAA,aAAc,WAAW;IAE7C,WAAW;IACX,MAAM,2BAA2B;QAC/B,IAAI;YACF,uBAAuB;YACvB,MAAM,eAAe,MAAM,6BAAmB,CAAC,sBAAsB;YACrE,uBAAuB;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B,SAAU;YACR,uBAAuB;QACzB;IACF;IAEA,WAAW;IACX,MAAM,yBAAyB;QAC7B,IAAI,EAAC,wBAAA,kCAAA,YAAa,EAAE,GAAE;QAEtB,IAAI;YACF,eAAe;YACf,MAAM,aAAa,MAAM,qBAAW,CAAC,aAAa,CAAC,YAAY,EAAE;YACjE,qBAAqB;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B,SAAU;YACR,eAAe;QACjB;IACF;IAOA,mBAAmB;IACnB,cAAK,CAAC,SAAS,CAAC;QACd,IAAI,UAAU,UAAU,CAAC,iBACvB;aACK,IAAI,cAAc,eACvB;IAEJ,GAAG;QAAC;QAAW,wBAAA,kCAAA,YAAa,EAAE;KAAC;IAE/B,MAAM,WAAW;QACf;YACE,KAAK;YACL,qBACE,2BAAC,WAAK;;kCACJ,2BAAC,mBAAY;;;;;oBAAG;;;;;;;YAIpB,wBAAU,2BAAC,2BAAkB;;;;;QAC/B;QACA;YACE,KAAK;YACL,qBACE,2BAAC,WAAK;;kCACJ,2BAAC,mBAAY;;;;;oBAAG;;;;;;;YAIpB,wBAAU,2BAAC,0BAAiB;;;;;QAC9B;QACA;YACE,KAAK;YACL,qBACE,2BAAC,WAAK;;kCACJ,2BAAC,2BAAoB;;;;;oBAAG;;;;;;;YAI5B,wBAAU,2BAAC,4BAAmB;;;;;QAChC;QACA;YACE,KAAK;YACL,qBACE,2BAAC,WAAK;;kCACJ,2BAAC,oBAAa;;;;;oBAAG;;;;;;;YAIrB,wBACE,2BAAC,mCAA0B;gBACzB,qBAAqB;gBACrB,SAAS;gBACT,WAAW;;;;;;QAGjB;KACD;IAED,qBACE,2BAAC,4BAAa;QAAC,OAAM;kBACnB,cAAA,2BAAC,UAAI;sBACH,cAAA,2BAAC,UAAI;gBACH,WAAW;gBACX,UAAU;gBACV,OAAO;gBACP,MAAK;gBACL,aAAY;;;;;;;;;;;;;;;;AAKtB;GAlHM;;QAOqB,aAAQ;;;KAP7B;IAoHN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnJf;;CAEC;;;;4BAqSD;;;eAAA;;;;;;;wEAnSgC;6BAazB;8BAMA;iCACkC;4BAEP;gFACP;;;;;;;;;;AAE3B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAClC,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAK;AAQ1B,MAAM,oBAAsD,CAAC,EAC3D,UAAU,EACV,OAAO,EACP,SAAS,EACV;;IACC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;IACzC,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;IAC3B,MAAM,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;IAErC;;;;;;;GAOC,GACD,MAAM,aAAa;QACjB,IAAI,CAAC,YAAY;QACjB,gBAAgB;QAChB,KAAK,cAAc,CAAC;YAClB,MAAM,WAAW,IAAI;YACrB,aAAa,WAAW,WAAW;QACrC;QACA,oBAAoB;IACtB;IAEA;;;;;;;;;;;;;GAaC,GACD,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,YAAY;QAEjB,IAAI;YACF,YAAY;YACZ,WAAW;YACX,MAAM,aAAgC;gBACpC,MAAM,OAAO,IAAI;gBACjB,aAAa,OAAO,WAAW;YACjC;YAEA,MAAM,qBAAW,CAAC,iBAAiB,CAAC;YACpC,oBAAoB;YACpB,aAAO,CAAC,OAAO,CAAC;YAChB,aAAa,SAAS;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,YAAY;QACd;IACF;IAEA;;;;;;;;;;;;;;GAcC,GACD,MAAM,eAAe;QACnB,IAAI,CAAC,YAAY;QAEjB,WAAK,CAAC,OAAO,CAAC;YACZ,OAAO;YACP,oBAAM,2BAAC,gCAAyB;;;;;YAChC,uBACE,2BAAC;;kCACC,2BAAC;;4BAAE;0CAAS,2BAAC;;oCAAO;oCAAE,WAAW,IAAI;oCAAC;;;;;;;4BAAU;;;;;;;kCAChD,2BAAC;wBAAE,OAAO;4BAAE,OAAO;4BAAW,cAAc;wBAAE;kCAAG;;;;;;kCAGjD,2BAAC;wBAAG,OAAO;4BAAE,OAAO;4BAAW,WAAW;4BAAG,aAAa;wBAAG;;0CAC3D,2BAAC;0CAAG;;;;;;0CACJ,2BAAC;0CAAG;;;;;;0CACJ,2BAAC;0CAAG;;;;;;;;;;;;;;;;;;YAIV,QAAQ;YACR,QAAQ;YACR,YAAY;YACZ,gBAAgB;YAChB,MAAM;gBACJ,IAAI;oBACF,YAAY;oBACZ,MAAM,qBAAW,CAAC,iBAAiB;oBACnC,aAAO,CAAC,OAAO,CAAC;oBAEhB,qBAAqB;oBACrB,MAAM,gBAAgB,CAAC,IAAO,CAAA;4BAAE,GAAG,CAAC;4BAAE,aAAa;wBAAK,CAAA;oBACxD,qBAAW,CAAC,cAAc;oBAC1B,YAAO,CAAC,IAAI,CAAC;gBACf,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,WAAW;oBACzB,aAAO,CAAC,KAAK,CAAC;gBAChB,SAAU;oBACR,YAAY;gBACd;YACF;QACF;IACF;IAEA,IAAI,SACF,qBACE,2BAAC;QAAI,OAAO;YAAE,WAAW;YAAU,SAAS;QAAS;kBACnD,cAAA,2BAAC,UAAI;YAAC,MAAK;;;;;;;;;;;IAKjB,IAAI,CAAC,YACH,qBACE,2BAAC,WAAK;QACJ,OAAO,WAAK,CAAC,sBAAsB;QACnC,aAAY;;;;;;IAKlB,qBACE,2BAAC;;0BAEC,2BAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAG;;kCAC7B,2BAAC;wBAAI,OAAO;4BAAE,SAAS;4BAAQ,gBAAgB;4BAAiB,YAAY;4BAAU,cAAc;wBAAG;;0CACrG,2BAAC,WAAK;;kDACJ,2BAAC,mBAAY;wCAAC,OAAO;4CAAE,UAAU;4CAAI,OAAO;wCAAU;;;;;;kDACtD,2BAAC;wCAAM,OAAO;wCAAG,OAAO;4CAAE,QAAQ;wCAAE;kDACjC,WAAW,IAAI;;;;;;oCAEjB,WAAW,SAAS,kBACnB,2BAAC;wCAAK,MAAK;wCAAY,OAAO;4CAAE,UAAU;wCAAG;kDAAG;;;;;;;;;;;;4BAKnD,WAAW,SAAS,kBACnB,2BAAC,WAAK;;kDACJ,2BAAC,YAAM;wCACL,oBAAM,2BAAC,mBAAY;;;;;wCACnB,SAAS;kDACV;;;;;;kDAGD,2BAAC,YAAM;wCACL,MAAM;wCACN,oBAAM,2BAAC,qBAAc;;;;;wCACrB,SAAS;wCACT,SAAS;kDACV;;;;;;;;;;;;;;;;;;kCAOP,2BAAC,kBAAY;wBAAC,QAAQ;wBAAG,QAAQ;;0CAC/B,2BAAC,kBAAY,CAAC,IAAI;gCAAC,OAAM;0CACtB,WAAW,IAAI;;;;;;0CAElB,2BAAC,kBAAY,CAAC,IAAI;gCAAC,OAAM;;oCACtB,WAAW,WAAW;oCAAC;;;;;;;0CAE1B,2BAAC,kBAAY,CAAC,IAAI;gCAAC,OAAM;0CACtB,IAAI,KAAK,WAAW,SAAS,EAAE,cAAc;;;;;;0CAEhD,2BAAC,kBAAY,CAAC,IAAI;gCAAC,OAAM;0CACtB,IAAI,KAAK,WAAW,SAAS,EAAE,cAAc;;;;;;0CAEhD,2BAAC,kBAAY,CAAC,IAAI;gCAAC,OAAM;gCAAO,MAAM;0CACnC,WAAW,WAAW,IAAI;;;;;;;;;;;;;;;;;;0BAKjC,2BAAC,aAAO;;;;;0BAGR,2BAAC,uBAAc;gBAAC,QAAQ,WAAW,EAAE;gBAAE,WAAW,WAAW,SAAS;;;;;;0BAGtE,2BAAC,WAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU,IAAM,oBAAoB;gBACpC,QAAQ;0BAER,cAAA,2BAAC,UAAI;oBACH,MAAM;oBACN,QAAO;oBACP,UAAU;;sCAEV,2BAAC,UAAI,CAAC,IAAI;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO;gCACL;oCAAE,UAAU;oCAAM,SAAS;gCAAU;gCACrC;oCAAE,KAAK;oCAAG,KAAK;oCAAI,SAAS;gCAAoB;6BACjD;sCAED,cAAA,2BAAC,WAAK;gCAAC,aAAY;;;;;;;;;;;sCAGrB,2BAAC,UAAI,CAAC,IAAI;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO;gCACL;oCAAE,KAAK;oCAAK,SAAS;gCAAiB;6BACvC;sCAED,cAAA,2BAAC;gCACC,MAAM;gCACN,aAAY;gCACZ,SAAS;gCACT,WAAW;;;;;;;;;;;sCAIf,2BAAC,UAAI,CAAC,IAAI;4BAAC,OAAO;gCAAE,cAAc;gCAAG,WAAW;4BAAQ;sCACtD,cAAA,2BAAC,WAAK;;kDACJ,2BAAC,YAAM;wCAAC,SAAS,IAAM,oBAAoB;kDAAQ;;;;;;kDAGnD,2BAAC,YAAM;wCAAC,MAAK;wCAAU,UAAS;wCAAS,SAAS;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW1E;GA/PM;;QAQW,UAAI,CAAC;QACQ,aAAQ;;;KAThC;IAiQN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvSf;;;;;;;;;;;;;;;;;;;CAmBC;;;;4BAkYD;;;eAAA;;;;;;wEAhY2C;6BAkBpC;8BAYA;iCAGqB;;;;;;;;;;AAG5B,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;AAC3B,MAAM,EAAE,MAAM,EAAE,GAAG,YAAM;AAczB,MAAM,iBAAgD,CAAC,EACrD,MAAM,EACN,SAAS,EACT,cAAc,EACf;;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAuB,EAAE;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAuB,EAAE;IAC/E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAc,EAAE;IACtE,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAS;IAEzD,IAAA,gBAAS,EAAC;QACR;IACF,GAAG;QAAC;KAAO;IAEX;;;;;;;;;;GAUC,GACD,IAAA,gBAAS,EAAC;QACR,kBAAkB;QAClB,IAAI,CAAC,WAAW,CAAC,MAAM,OAAO,CAAC,UAAU;YACvC,mBAAmB,EAAE;YACrB;QACF;QAEA,MAAM,WAAW,QAAQ,MAAM,CAAC,CAAA;YAC9B,sBAAsB;YACtB,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,KAAK,EAC1C,OAAO;YAGT,gBAAgB;YAChB,MAAM,gBAAgB,CAAC,cACrB,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;YAE5D,SAAS;YACT,MAAM,gBAAgB,iBAAiB,SACpC,iBAAiB,YAAY,OAAO,QAAQ,IAC5C,iBAAiB,cAAc,CAAC,OAAO,QAAQ,IAC/C,iBAAiB,aAAa,OAAO,SAAS,IAC9C,iBAAiB,YAAY,CAAC,OAAO,SAAS;YAEjD,OAAO,iBAAiB;QAC1B;QACA,mBAAmB;IACrB,GAAG;QAAC;QAAS;QAAY;KAAa;IAEtC;;;;;;;;;;;GAWC,GACD,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,qBAAW,CAAC,cAAc,CAAC;gBAAE,SAAS;gBAAG,UAAU;YAAK;YAC/E,sBAAsB;YACtB,WAAW,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI,EAAE;QACjC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;YACd,qBAAqB;YACrB,WAAW,EAAE;QACf,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,OAAO,SAAS,EAAE;YACpB,aAAO,CAAC,OAAO,CAAC;YAChB;QACF;QAEA,WAAK,CAAC,OAAO,CAAC;YACZ,OAAO;YACP,SAAS,CAAC,SAAS,EAAE,OAAO,IAAI,CAAC,IAAI,CAAC;YACtC,QAAQ;YACR,YAAY;YACZ,MAAM;gBACJ,IAAI;oBACF,MAAM,qBAAW,CAAC,YAAY,CAAC,OAAO,EAAE;oBACxC,aAAO,CAAC,OAAO,CAAC;oBAChB;oBACA,2BAAA,6BAAA;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,WAAW;gBAC3B;YACF;QACF;IACF;IAEA,MAAM,oBAAoB;QACxB,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA,SACrC,gBAAgB,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,SAAS;QAG1D,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,aAAO,CAAC,OAAO,CAAC;YAChB;QACF;QAEA,WAAK,CAAC,OAAO,CAAC;YACZ,OAAO;YACP,SAAS,CAAC,SAAS,EAAE,gBAAgB,MAAM,CAAC,MAAM,CAAC;YACnD,QAAQ;YACR,YAAY;YACZ,MAAM;gBACJ,IAAI;oBACF,MAAM,QAAQ,GAAG,CACf,gBAAgB,GAAG,CAAC,CAAA,SAAU,qBAAW,CAAC,YAAY,CAAC,OAAO,EAAE;oBAElE,aAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,gBAAgB,MAAM,CAAC,IAAI,CAAC;oBACpD,mBAAmB,EAAE;oBACrB;oBACA,2BAAA,6BAAA;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,aAAa;oBAC3B,aAAO,CAAC,KAAK,CAAC;gBAChB;YACF;QACF;IACF;IAEA,MAAM,UAA2C;QAC/C;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,MAAM,uBACb,2BAAC,WAAK;;sCACJ,2BAAC,YAAM;4BAAC,MAAK;4BAAQ,oBAAM,2BAAC,mBAAY;;;;;;;;;;sCACxC,2BAAC;;8CACC,2BAAC;8CAAK;;;;;;8CACN,2BAAC;oCAAI,OAAO;wCAAE,UAAU;wCAAI,OAAO;oCAAO;8CAAI,OAAO,KAAK;;;;;;;;;;;;;;;;;;QAIlE;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,0BACP,2BAAC,SAAG;oBAAC,OAAO,YAAY,SAAS;oBAAQ,MAAM,0BAAY,2BAAC,oBAAa;;;;+CAAM,2BAAC,mBAAY;;;;;8BACzF,YAAY,QAAQ;;;;;;QAG3B;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,yBACP,2BAAC,SAAG;oBAAC,OAAO,WAAW,UAAU;8BAC9B,WAAW,OAAO;;;;;;QAGzB;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,aAAe,IAAI,KAAK,YAAY,kBAAkB;QACjE;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;YACP,QAAQ,CAAC;gBACP,MAAM,OAAO,IAAI,KAAK;gBACtB,MAAM,MAAM,IAAI;gBAChB,MAAM,WAAW,KAAK,KAAK,CAAC,AAAC,CAAA,IAAI,OAAO,KAAK,KAAK,OAAO,EAAC,IAAM;gBAEhE,IAAI,QAAQ;gBACZ,IAAI,WAAW,GAAG,QAAQ;gBAC1B,IAAI,WAAW,IAAI,QAAQ;gBAE3B,qBACE,2BAAC,aAAO;oBAAC,OAAO,KAAK,cAAc;8BACjC,cAAA,2BAAC,SAAG;wBAAC,OAAO;kCACT,aAAa,IAAI,OAAO,CAAC,EAAE,SAAS,EAAE,CAAC;;;;;;;;;;;YAIhD;QACF;QACA;YACE,OAAO;YACP,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAG;gBACV,IAAI,CAAC,aAAa,OAAO,SAAS,EAChC,qBAAO,2BAAC;oBAAK,MAAK;8BAAY;;;;;;gBAGhC,MAAM,YAAgC;oBACpC;wBACE,KAAK;wBACL,OAAO;wBACP,oBAAM,2BAAC,qBAAc;;;;;wBACrB,QAAQ;wBACR,SAAS,IAAM,mBAAmB;oBACpC;iBACD;gBAED,qBACE,2BAAC,WAAK;oBAAC,MAAK;;sCACV,2BAAC,YAAM;4BACL,MAAK;4BACL,MAAM;4BACN,MAAK;4BACL,oBAAM,2BAAC,qBAAc;;;;;4BACrB,SAAS,IAAM,mBAAmB;sCACnC;;;;;;sCAGD,2BAAC,cAAQ;4BAAC,MAAM;gCAAE,OAAO;4BAAU;4BAAG,SAAS;gCAAC;6BAAQ;sCACtD,cAAA,2BAAC,YAAM;gCACL,MAAK;gCACL,MAAK;gCACL,oBAAM,2BAAC,mBAAY;;;;;;;;;;;;;;;;;;;;;YAK7B;QACF;KACD;IAED,MAAM,eAAe;QACnB;QACA,UAAU,CAAC;YACT,mBAAmB;QACrB;QACA,kBAAkB,CAAC,SAAgC,CAAA;gBACjD,UAAU,OAAO,SAAS;YAC5B,CAAA;IACF;IAEA,qBACE,2BAAC,UAAI;QACH,qBACE,2BAAC,WAAK;;8BACJ,2BAAC;oBAAK,MAAM;8BAAC;;;;;;8BACb,2BAAC,WAAK;oBAAC,OAAO,gBAAgB,MAAM;oBAAE,QAAQ;;;;;;;;;;;;QAGlD,qBACE,2BAAC,WAAK;;8BACJ,2BAAC,YAAM;oBACL,OAAO;oBACP,UAAU;oBACV,OAAO;wBAAE,OAAO;oBAAI;oBACpB,MAAK;;sCAEL,2BAAC;4BAAO,OAAM;sCAAM;;;;;;sCACpB,2BAAC;4BAAO,OAAM;sCAAS;;;;;;sCACvB,2BAAC;4BAAO,OAAM;sCAAW;;;;;;sCACzB,2BAAC;4BAAO,OAAM;sCAAU;;;;;;sCACxB,2BAAC;4BAAO,OAAM;sCAAS;;;;;;;;;;;;8BAEzB,2BAAC,WAAK;oBACJ,aAAY;oBACZ,sBAAQ,2BAAC,qBAAc;;;;;oBACvB,OAAO;oBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oBAC7C,OAAO;wBAAE,OAAO;oBAAI;oBACpB,MAAK;;;;;;;;;;;;;YAKV,gBAAgB,MAAM,GAAG,KAAK,2BAC7B,2BAAC;gBAAI,OAAO;oBAAE,cAAc;oBAAI,SAAS;oBAAI,YAAY;oBAAW,cAAc;gBAAE;0BAClF,cAAA,2BAAC,WAAK;;sCACJ,2BAAC;;gCAAK;gCAAK,gBAAgB,MAAM;gCAAC;;;;;;;sCAClC,2BAAC,YAAM;4BACL,MAAK;4BACL,MAAM;4BACN,oBAAM,2BAAC,qBAAc;;;;;4BACrB,SAAS;sCACV;;;;;;sCAGD,2BAAC,YAAM;4BACL,MAAK;4BACL,SAAS,IAAM,mBAAmB,EAAE;sCACrC;;;;;;;;;;;;;;;;;0BAOP,2BAAC,WAAK;gBACJ,SAAS;gBACT,YAAY;gBACZ,QAAO;gBACP,SAAS;gBACT,cAAc,YAAY,eAAe;gBACzC,YAAY;oBACV,iBAAiB;oBACjB,iBAAiB;oBACjB,WAAW,CAAC,QAAU,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC;oBACtC,UAAU;gBACZ;;;;;;;;;;;;AAIR;GA3UM;KAAA;IA6UN,WAAe"}