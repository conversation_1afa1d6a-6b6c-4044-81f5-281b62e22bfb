((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] || []).push([
        ['src/pages/personal-center/index.tsx'],
{ "src/pages/personal-center/components/FriendManageContent.tsx": function (module, exports, __mako_require__){
/**
 * 个人中心 - 好友管理内容组件
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _services = __mako_require__("src/services/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Text, Title } = _antd.Typography;
const { Search } = _antd.Input;
const FriendManageContent = ()=>{
    _s();
    // 标签页状态
    const [activeTab, setActiveTab] = (0, _react.useState)('list');
    // 好友列表状态
    const [friends, setFriends] = (0, _react.useState)([]);
    const [loading, setLoading] = (0, _react.useState)(false);
    const [searchKeyword, setSearchKeyword] = (0, _react.useState)('');
    const [currentPage, setCurrentPage] = (0, _react.useState)(1);
    const [pageSize] = (0, _react.useState)(8);
    // 操作状态
    const [removing, setRemoving] = (0, _react.useState)(null);
    const [remarkModalVisible, setRemarkModalVisible] = (0, _react.useState)(false);
    const [addFriendModalVisible, setAddFriendModalVisible] = (0, _react.useState)(false);
    const [currentFriend, setCurrentFriend] = (0, _react.useState)(null);
    // 表单
    const [remarkForm] = _antd.Form.useForm();
    const [addFriendForm] = _antd.Form.useForm();
    // 添加好友相关状态
    const [searchUsers, setSearchUsers] = (0, _react.useState)([]);
    const [searchingUsers, setSearchingUsers] = (0, _react.useState)(false);
    const [addingFriend, setAddingFriend] = (0, _react.useState)(null);
    // 获取好友列表
    const fetchFriends = async ()=>{
        try {
            setLoading(true);
            const friendList = await _services.FriendService.getFriends();
            setFriends(friendList);
        } catch (error) {
            console.error('获取好友列表失败:', error);
            _antd.message.error('获取好友列表失败');
        } finally{
            setLoading(false);
        }
    };
    // 初始化加载
    (0, _react.useEffect)(()=>{
        fetchFriends();
    }, []);
    // 过滤好友列表
    const filteredFriends = friends.filter((friend)=>{
        var _friend_name, _friend_email;
        return ((_friend_name = friend.name) === null || _friend_name === void 0 ? void 0 : _friend_name.toLowerCase().includes(searchKeyword.toLowerCase())) || ((_friend_email = friend.email) === null || _friend_email === void 0 ? void 0 : _friend_email.toLowerCase().includes(searchKeyword.toLowerCase()));
    });
    // 分页处理
    const paginatedFriends = filteredFriends.slice((currentPage - 1) * pageSize, currentPage * pageSize);
    // 删除好友
    const handleRemoveFriend = async (friend)=>{
        try {
            setRemoving(friend.id);
            await _services.FriendService.removeFriend(friend.id);
            _antd.message.success(`已删除好友 "${friend.name}"`);
            fetchFriends();
        } catch (error) {
            console.error('删除好友失败:', error);
            _antd.message.error('删除好友失败');
        } finally{
            setRemoving(null);
        }
    };
    // 编辑备注
    const handleEditRemark = async (friend)=>{
        setCurrentFriend(friend);
        setRemarkModalVisible(true);
        try {
            // 获取当前备注
            const remark = await _services.FriendService.getFriendRemark(friend.id);
            remarkForm.setFieldsValue({
                remark
            });
        } catch (error) {
            console.error('获取好友备注失败:', error);
            remarkForm.setFieldsValue({
                remark: ''
            });
        }
    };
    // 保存备注
    const handleSaveRemark = async (values)=>{
        if (!currentFriend) return;
        try {
            await _services.FriendService.setFriendRemark({
                friendId: currentFriend.id,
                remark: values.remark
            });
            _antd.message.success('备注保存成功');
            setRemarkModalVisible(false);
            setCurrentFriend(null);
            remarkForm.resetFields();
            fetchFriends();
        } catch (error) {
            console.error('保存备注失败:', error);
            _antd.message.error('保存备注失败');
        }
    };
    // 搜索用户
    const handleSearchUsers = async (email)=>{
        if (!email.trim()) {
            setSearchUsers([]);
            return;
        }
        try {
            setSearchingUsers(true);
            const users = await _services.UserService.searchUsersByEmail(email);
            // 过滤掉已经是好友的用户
            const friendIds = friends.map((f)=>f.id);
            const availableUsers = users.filter((user)=>!friendIds.includes(user.id));
            setSearchUsers(availableUsers);
        } catch (error) {
            console.error('搜索用户失败:', error);
            _antd.message.error('搜索用户失败');
        } finally{
            setSearchingUsers(false);
        }
    };
    // 添加好友
    const handleAddFriend = async (user)=>{
        try {
            setAddingFriend(user.id);
            await _services.FriendService.sendFriendRequest({
                email: user.email
            });
            _antd.message.success(`已向 "${user.name}" 发送好友请求`);
            setSearchUsers(searchUsers.filter((u)=>u.id !== user.id));
        } catch (error) {
            console.error('发送好友请求失败:', error);
            _antd.message.error('发送好友请求失败');
        } finally{
            setAddingFriend(null);
        }
    };
    // 打开添加好友模态框
    const handleOpenAddFriend = ()=>{
        setAddFriendModalVisible(true);
        setSearchUsers([]);
        addFriendForm.resetFields();
    };
    // 好友列表组件
    const renderFriendList = ()=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                    gutter: [
                        16,
                        16
                    ],
                    style: {
                        marginBottom: 24
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 24,
                            sm: 12,
                            md: 8,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Search, {
                                placeholder: "搜索好友姓名或邮箱",
                                allowClear: true,
                                value: searchKeyword,
                                onChange: (e)=>setSearchKeyword(e.target.value),
                                prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                                    fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                    lineNumber: 207,
                                    columnNumber: 21
                                }, void 0)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                lineNumber: 202,
                                columnNumber: 11
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                            lineNumber: 201,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 24,
                            sm: 12,
                            md: 16,
                            style: {
                                textAlign: 'right'
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserAddOutlined, {}, void 0, false, {
                                            fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                            lineNumber: 214,
                                            columnNumber: 21
                                        }, void 0),
                                        onClick: handleOpenAddFriend,
                                        children: "添加好友"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                        lineNumber: 212,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ReloadOutlined, {}, void 0, false, {
                                            fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                            lineNumber: 220,
                                            columnNumber: 21
                                        }, void 0),
                                        onClick: fetchFriends,
                                        loading: loading,
                                        children: "刷新"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                        lineNumber: 219,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                lineNumber: 211,
                                columnNumber: 11
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                            lineNumber: 210,
                            columnNumber: 9
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                    lineNumber: 200,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                    size: "small",
                    style: {
                        marginBottom: 16
                    },
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        split: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {
                            type: "vertical"
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                            lineNumber: 232,
                            columnNumber: 23
                        }, void 0),
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("strong", {
                                        children: "好友总数："
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                        lineNumber: 234,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "success",
                                        children: friends.length
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                        lineNumber: 235,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                lineNumber: 233,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("strong", {
                                        children: "当前显示："
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                        lineNumber: 238,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "primary",
                                        children: filteredFriends.length
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                        lineNumber: 239,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                lineNumber: 237,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                        lineNumber: 232,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                    lineNumber: 231,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                        spinning: loading,
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                                dataSource: paginatedFriends,
                                renderItem: (friend)=>{
                                    var _friend_name;
                                    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                                        actions: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                title: "编辑备注",
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    type: "text",
                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                        lineNumber: 255,
                                                        columnNumber: 29
                                                    }, void 0),
                                                    onClick: ()=>handleEditRemark(friend),
                                                    size: "small",
                                                    children: "备注"
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                    lineNumber: 253,
                                                    columnNumber: 21
                                                }, void 0)
                                            }, "remark", false, {
                                                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                lineNumber: 252,
                                                columnNumber: 19
                                            }, void 0),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                                                title: "确认删除好友",
                                                description: `确定要删除好友 "${friend.name}" 吗？`,
                                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {
                                                    style: {
                                                        color: 'red'
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                    lineNumber: 266,
                                                    columnNumber: 27
                                                }, void 0),
                                                onConfirm: ()=>handleRemoveFriend(friend),
                                                okText: "确认删除",
                                                cancelText: "取消",
                                                okType: "danger",
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    type: "text",
                                                    danger: true,
                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                        lineNumber: 275,
                                                        columnNumber: 29
                                                    }, void 0),
                                                    loading: removing === friend.id,
                                                    size: "small",
                                                    children: "删除"
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                    lineNumber: 272,
                                                    columnNumber: 21
                                                }, void 0)
                                            }, "delete", false, {
                                                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                lineNumber: 262,
                                                columnNumber: 19
                                            }, void 0)
                                        ],
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item.Meta, {
                                            avatar: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                                size: 48,
                                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                    fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                    lineNumber: 288,
                                                    columnNumber: 29
                                                }, void 0),
                                                style: {
                                                    backgroundColor: '#1890ff'
                                                },
                                                children: (_friend_name = friend.name) === null || _friend_name === void 0 ? void 0 : _friend_name.charAt(0).toUpperCase()
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                lineNumber: 286,
                                                columnNumber: 21
                                            }, void 0),
                                            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        strong: true,
                                                        children: friend.name
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                        lineNumber: 296,
                                                        columnNumber: 23
                                                    }, void 0),
                                                    friend.remark && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        type: "secondary",
                                                        style: {
                                                            fontSize: 12
                                                        },
                                                        children: [
                                                            "(",
                                                            friend.remark,
                                                            ")"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                        lineNumber: 298,
                                                        columnNumber: 25
                                                    }, void 0)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                lineNumber: 295,
                                                columnNumber: 21
                                            }, void 0),
                                            description: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                direction: "vertical",
                                                size: 4,
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        type: "secondary",
                                                        children: friend.email
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                        lineNumber: 306,
                                                        columnNumber: 23
                                                    }, void 0),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        type: "secondary",
                                                        style: {
                                                            fontSize: 12
                                                        },
                                                        children: [
                                                            "添加时间: ",
                                                            friend.createdAt ? new Date(friend.createdAt).toLocaleDateString() : '未知'
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                        lineNumber: 307,
                                                        columnNumber: 23
                                                    }, void 0)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                lineNumber: 305,
                                                columnNumber: 21
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                            lineNumber: 284,
                                            columnNumber: 17
                                        }, void 0)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                        lineNumber: 250,
                                        columnNumber: 15
                                    }, void 0);
                                },
                                locale: {
                                    emptyText: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Empty, {
                                        image: _antd.Empty.PRESENTED_IMAGE_SIMPLE,
                                        description: searchKeyword ? `没有找到包含 "${searchKeyword}" 的好友` : "暂无好友，点击上方按钮添加好友"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                        lineNumber: 317,
                                        columnNumber: 17
                                    }, void 0)
                                }
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                lineNumber: 247,
                                columnNumber: 11
                            }, this),
                            filteredFriends.length > pageSize && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    textAlign: 'center',
                                    marginTop: 16
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Pagination, {
                                    current: currentPage,
                                    pageSize: pageSize,
                                    total: filteredFriends.length,
                                    onChange: setCurrentPage,
                                    showSizeChanger: false,
                                    showQuickJumper: true,
                                    showTotal: (total, range)=>`第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                    lineNumber: 332,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                lineNumber: 331,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                        lineNumber: 246,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                    lineNumber: 245,
                    columnNumber: 7
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
            lineNumber: 198,
            columnNumber: 5
        }, this);
    // 添加好友组件
    const renderAddFriend = ()=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                    level: 4,
                    children: "添加好友"
                }, void 0, false, {
                    fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                    lineNumber: 353,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                    form: addFriendForm,
                    layout: "vertical",
                    onFinish: (values)=>handleSearchUsers(values.email),
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                        label: "搜索用户",
                        name: "email",
                        rules: [
                            {
                                required: true,
                                message: '请输入邮箱'
                            },
                            {
                                type: 'email',
                                message: '请输入有效的邮箱地址'
                            }
                        ],
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Search, {
                            placeholder: "请输入用户邮箱进行搜索",
                            enterButton: "搜索",
                            loading: searchingUsers,
                            onSearch: handleSearchUsers
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                            lineNumber: 367,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                        lineNumber: 359,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                    lineNumber: 354,
                    columnNumber: 7
                }, this),
                searchUsers.length > 0 && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        marginTop: 24
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                            level: 5,
                            children: "搜索结果"
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                            lineNumber: 379,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                            dataSource: searchUsers,
                            renderItem: (user)=>{
                                var _user_name;
                                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                                    actions: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "primary",
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserAddOutlined, {}, void 0, false, {
                                                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                lineNumber: 388,
                                                columnNumber: 27
                                            }, void 0),
                                            loading: addingFriend === user.id,
                                            onClick: ()=>handleAddFriend(user),
                                            size: "small",
                                            children: "添加好友"
                                        }, "add", false, {
                                            fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                            lineNumber: 385,
                                            columnNumber: 19
                                        }, void 0)
                                    ],
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item.Meta, {
                                        avatar: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                            size: 40,
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                lineNumber: 401,
                                                columnNumber: 29
                                            }, void 0),
                                            style: {
                                                backgroundColor: '#52c41a'
                                            },
                                            children: (_user_name = user.name) === null || _user_name === void 0 ? void 0 : _user_name.charAt(0).toUpperCase()
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                            lineNumber: 399,
                                            columnNumber: 21
                                        }, void 0),
                                        title: user.name,
                                        description: user.email
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                        lineNumber: 397,
                                        columnNumber: 17
                                    }, void 0)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                    lineNumber: 383,
                                    columnNumber: 15
                                }, void 0);
                            }
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                            lineNumber: 380,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                    lineNumber: 378,
                    columnNumber: 9
                }, this),
                searchUsers.length === 0 && addFriendForm.getFieldValue('email') && !searchingUsers && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Empty, {
                    image: _antd.Empty.PRESENTED_IMAGE_SIMPLE,
                    description: "未找到匹配的用户",
                    style: {
                        marginTop: 24
                    }
                }, void 0, false, {
                    fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                    lineNumber: 417,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
            lineNumber: 352,
            columnNumber: 5
        }, this);
    const tabItems = [
        {
            key: 'list',
            label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UnorderedListOutlined, {}, void 0, false, {
                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                        lineNumber: 431,
                        columnNumber: 11
                    }, this),
                    "我的好友",
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Badge, {
                        count: friends.length,
                        showZero: true
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                        lineNumber: 433,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                lineNumber: 430,
                columnNumber: 9
            }, this),
            children: renderFriendList()
        },
        {
            key: 'add',
            label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserAddOutlined, {}, void 0, false, {
                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                        lineNumber: 442,
                        columnNumber: 11
                    }, this),
                    "添加好友"
                ]
            }, void 0, true, {
                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                lineNumber: 441,
                columnNumber: 9
            }, this),
            children: renderAddFriend()
        }
    ];
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tabs, {
                activeKey: activeTab,
                onChange: setActiveTab,
                items: tabItems,
                size: "large"
            }, void 0, false, {
                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                lineNumber: 452,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: "编辑好友备注",
                open: remarkModalVisible,
                onCancel: ()=>{
                    setRemarkModalVisible(false);
                    setCurrentFriend(null);
                    remarkForm.resetFields();
                },
                footer: null,
                width: 400,
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                    form: remarkForm,
                    layout: "vertical",
                    onFinish: handleSaveRemark,
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            label: "好友备注",
                            name: "remark",
                            rules: [
                                {
                                    max: 50,
                                    message: '备注长度不能超过50个字符'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input.TextArea, {
                                placeholder: "为好友添加备注...",
                                rows: 3,
                                maxLength: 50,
                                showCount: true
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                lineNumber: 483,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                            lineNumber: 476,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            style: {
                                marginBottom: 0,
                                textAlign: 'right'
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        onClick: ()=>{
                                            setRemarkModalVisible(false);
                                            setCurrentFriend(null);
                                            remarkForm.resetFields();
                                        },
                                        children: "取消"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                        lineNumber: 492,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        htmlType: "submit",
                                        children: "保存"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                        lineNumber: 499,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                lineNumber: 491,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                            lineNumber: 490,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                    lineNumber: 471,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                lineNumber: 460,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
        lineNumber: 451,
        columnNumber: 5
    }, this);
};
_s(FriendManageContent, "HNQ3PsfhzwhDkMXHrwOnG8eiQ0M=", false, function() {
    return [
        _antd.Form.useForm,
        _antd.Form.useForm
    ];
});
_c = FriendManageContent;
var _default = FriendManageContent;
var _c;
$RefreshReg$(_c, "FriendManageContent");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/personal-center/components/TeamManageContent.tsx": function (module, exports, __mako_require__){
/**
 * 团队管理内容组件 - 用于个人中心
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _services = __mako_require__("src/services/index.ts");
var _TeamDetailContent = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team/detail/components/TeamDetailContent.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { TextArea } = _antd.Input;
const TeamManageContent = ()=>{
    _s();
    const [loading, setLoading] = (0, _react.useState)(false);
    const [teams, setTeams] = (0, _react.useState)([]);
    const [createModalVisible, setCreateModalVisible] = (0, _react.useState)(false);
    const [editModalVisible, setEditModalVisible] = (0, _react.useState)(false);
    const [editingTeam, setEditingTeam] = (0, _react.useState)(null);
    const [selectedTeam, setSelectedTeam] = (0, _react.useState)(null);
    const [viewMode, setViewMode] = (0, _react.useState)('list');
    const [createForm] = _antd.Form.useForm();
    const [editForm] = _antd.Form.useForm();
    (0, _react.useEffect)(()=>{
        fetchTeams();
    }, []);
    /**
   * 获取用户的团队列表
   */ const fetchTeams = async ()=>{
        try {
            setLoading(true);
            const teamList = await _services.TeamService.getUserTeams();
            setTeams(teamList);
        } catch (error) {
            console.error('获取团队列表失败:', error);
            _antd.message.error('获取团队列表失败');
        } finally{
            setLoading(false);
        }
    };
    /**
   * 创建团队
   */ const handleCreateTeam = async (values)=>{
        try {
            await _services.TeamService.createTeam(values);
            _antd.message.success('团队创建成功');
            setCreateModalVisible(false);
            createForm.resetFields();
            fetchTeams();
        } catch (error) {
            console.error('创建团队失败:', error);
            _antd.message.error('创建团队失败');
        }
    };
    /**
   * 更新团队信息
   */ const handleUpdateTeam = async (values)=>{
        if (!editingTeam) return;
        try {
            await _services.TeamService.updateTeam(editingTeam.id, values);
            _antd.message.success('团队信息更新成功');
            setEditModalVisible(false);
            setEditingTeam(null);
            editForm.resetFields();
            fetchTeams();
        } catch (error) {
            console.error('更新团队失败:', error);
            _antd.message.error('更新团队失败');
        }
    };
    /**
   * 查看团队详情
   */ const handleViewTeamDetail = (team)=>{
        setSelectedTeam(team);
        setViewMode('detail');
    };
    /**
   * 返回团队列表
   */ const handleBackToList = ()=>{
        setSelectedTeam(null);
        setViewMode('list');
    };
    // 如果是详情模式，显示团队详情
    if (viewMode === 'detail' && selectedTeam) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
        title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ArrowLeftOutlined, {}, void 0, false, {
                    fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                    lineNumber: 155,
                    columnNumber: 21
                }, void 0),
                onClick: handleBackToList,
                children: "返回团队列表"
            }, void 0, false, {
                fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                lineNumber: 154,
                columnNumber: 13
            }, void 0)
        }, void 0, false, {
            fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
            lineNumber: 153,
            columnNumber: 11
        }, void 0),
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamDetailContent.default, {
            teamDetail: selectedTeam,
            loading: false,
            onRefresh: ()=>{
                fetchTeams();
                // 刷新当前选中的团队详情
                if (selectedTeam) _services.TeamService.getTeamDetail(selectedTeam.id).then((detail)=>{
                    setSelectedTeam(detail);
                }).catch((error)=>{
                    console.error('刷新团队详情失败:', error);
                });
            }
        }, void 0, false, {
            fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
            lineNumber: 163,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
        lineNumber: 151,
        columnNumber: 7
    }, this);
    // 默认显示团队列表
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
        title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                    fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                    lineNumber: 187,
                    columnNumber: 11
                }, void 0),
                "我的团队"
            ]
        }, void 0, true, {
            fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
            lineNumber: 186,
            columnNumber: 9
        }, void 0),
        extra: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                    type: "primary",
                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                        fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                        lineNumber: 195,
                        columnNumber: 19
                    }, void 0),
                    onClick: ()=>setCreateModalVisible(true),
                    children: "创建团队"
                }, void 0, false, {
                    fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                    lineNumber: 193,
                    columnNumber: 11
                }, void 0),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                    onClick: fetchTeams,
                    loading: loading,
                    children: "刷新"
                }, void 0, false, {
                    fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                    lineNumber: 200,
                    columnNumber: 11
                }, void 0)
            ]
        }, void 0, true, {
            fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
            lineNumber: 192,
            columnNumber: 9
        }, void 0),
        children: [
            teams.length === 0 && !loading ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Empty, {
                image: _antd.Empty.PRESENTED_IMAGE_SIMPLE,
                description: "您还没有创建或加入任何团队",
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                    type: "primary",
                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                        fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                        lineNumber: 216,
                        columnNumber: 19
                    }, void 0),
                    onClick: ()=>setCreateModalVisible(true),
                    children: "创建第一个团队"
                }, void 0, false, {
                    fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                    lineNumber: 214,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                lineNumber: 210,
                columnNumber: 9
            }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                loading: loading,
                itemLayout: "horizontal",
                dataSource: teams,
                renderItem: (team)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                        actions: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "primary",
                                size: "small",
                                onClick: ()=>handleViewTeamDetail(team),
                                children: "查看详情"
                            }, "view", false, {
                                fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                lineNumber: 230,
                                columnNumber: 17
                            }, void 0)
                        ],
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item.Meta, {
                            avatar: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                    fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                    lineNumber: 243,
                                    columnNumber: 27
                                }, void 0),
                                style: {
                                    backgroundColor: '#1890ff'
                                }
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                lineNumber: 242,
                                columnNumber: 19
                            }, void 0),
                            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    team.name,
                                    team.isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                        color: "gold",
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                                            fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                            lineNumber: 251,
                                            columnNumber: 47
                                        }, void 0),
                                        children: "创建者"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                        lineNumber: 251,
                                        columnNumber: 23
                                    }, void 0)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                lineNumber: 248,
                                columnNumber: 19
                            }, void 0),
                            description: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        children: team.description || '暂无描述'
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                        lineNumber: 259,
                                        columnNumber: 21
                                    }, void 0),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        style: {
                                            marginTop: 4,
                                            color: '#666'
                                        },
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                            fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                                            lineNumber: 263,
                                                            columnNumber: 27
                                                        }, void 0),
                                                        " ",
                                                        team.memberCount,
                                                        " 名成员"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                                    lineNumber: 262,
                                                    columnNumber: 25
                                                }, void 0),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                    children: [
                                                        "创建于 ",
                                                        new Date(team.createdAt).toLocaleDateString()
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                                    lineNumber: 265,
                                                    columnNumber: 25
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                            lineNumber: 261,
                                            columnNumber: 23
                                        }, void 0)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                        lineNumber: 260,
                                        columnNumber: 21
                                    }, void 0)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                lineNumber: 258,
                                columnNumber: 19
                            }, void 0)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                            lineNumber: 240,
                            columnNumber: 15
                        }, void 0)
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                        lineNumber: 228,
                        columnNumber: 13
                    }, void 0)
            }, void 0, false, {
                fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                lineNumber: 223,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: "创建团队",
                open: createModalVisible,
                onCancel: ()=>{
                    setCreateModalVisible(false);
                    createForm.resetFields();
                },
                footer: null,
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                    form: createForm,
                    layout: "vertical",
                    onFinish: handleCreateTeam,
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            label: "团队名称",
                            name: "name",
                            rules: [
                                {
                                    required: true,
                                    message: '请输入团队名称'
                                },
                                {
                                    max: 100,
                                    message: '团队名称不能超过100个字符'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                placeholder: "请输入团队名称"
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                lineNumber: 299,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                            lineNumber: 291,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            label: "团队描述",
                            name: "description",
                            rules: [
                                {
                                    max: 500,
                                    message: '团队描述不能超过500个字符'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                rows: 4,
                                placeholder: "请输入团队描述（可选）"
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                lineNumber: 309,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                            lineNumber: 302,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        htmlType: "submit",
                                        children: "创建团队"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                        lineNumber: 317,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        onClick: ()=>{
                                            setCreateModalVisible(false);
                                            createForm.resetFields();
                                        },
                                        children: "取消"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                        lineNumber: 320,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                lineNumber: 316,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                            lineNumber: 315,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                    lineNumber: 286,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                lineNumber: 277,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: "编辑团队",
                open: editModalVisible,
                onCancel: ()=>{
                    setEditModalVisible(false);
                    setEditingTeam(null);
                    editForm.resetFields();
                },
                footer: null,
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                    form: editForm,
                    layout: "vertical",
                    onFinish: handleUpdateTeam,
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            label: "团队名称",
                            name: "name",
                            rules: [
                                {
                                    required: true,
                                    message: '请输入团队名称'
                                },
                                {
                                    max: 100,
                                    message: '团队名称不能超过100个字符'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                placeholder: "请输入团队名称"
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                lineNumber: 355,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                            lineNumber: 347,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            label: "团队描述",
                            name: "description",
                            rules: [
                                {
                                    max: 500,
                                    message: '团队描述不能超过500个字符'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                rows: 4,
                                placeholder: "请输入团队描述（可选）"
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                lineNumber: 365,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                            lineNumber: 358,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        htmlType: "submit",
                                        children: "保存修改"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                        lineNumber: 373,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        onClick: ()=>{
                                            setEditModalVisible(false);
                                            setEditingTeam(null);
                                            editForm.resetFields();
                                        },
                                        children: "取消"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                        lineNumber: 376,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                lineNumber: 372,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                            lineNumber: 371,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                    lineNumber: 342,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                lineNumber: 332,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
        lineNumber: 184,
        columnNumber: 5
    }, this);
};
_s(TeamManageContent, "QGLUw8XOl1PdD6TWuGs4MBKz1HQ=", false, function() {
    return [
        _antd.Form.useForm,
        _antd.Form.useForm
    ];
});
_c = TeamManageContent;
var _default = TeamManageContent;
var _c;
$RefreshReg$(_c, "TeamManageContent");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/personal-center/index.tsx": function (module, exports, __mako_require__){
/**
 * 个人中心页面 - 整合个人资料、团队管理、订阅管理
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _services = __mako_require__("src/services/index.ts");
var _UserProfileContent = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/user/components/UserProfileContent.tsx"));
var _UnifiedSubscriptionContent = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/subscription/components/UnifiedSubscriptionContent.tsx"));
var _TeamManageContent = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/components/TeamManageContent.tsx"));
var _FriendManageContent = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/components/FriendManageContent.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title } = _antd.Typography;
const PersonalCenterPage = ()=>{
    _s();
    const [activeTab, setActiveTab] = (0, _react.useState)('profile');
    const [currentSubscription, setCurrentSubscription] = (0, _react.useState)(null);
    const [subscriptionLoading, setSubscriptionLoading] = (0, _react.useState)(false);
    const [currentTeamDetail, setCurrentTeamDetail] = (0, _react.useState)(null);
    const [teamLoading, setTeamLoading] = (0, _react.useState)(false);
    const { initialState } = (0, _max.useModel)('@@initialState');
    const currentTeam = initialState === null || initialState === void 0 ? void 0 : initialState.currentTeam;
    // 获取当前订阅信息
    const fetchCurrentSubscription = async ()=>{
        try {
            setSubscriptionLoading(true);
            const subscription = await _services.SubscriptionService.getCurrentSubscription();
            setCurrentSubscription(subscription);
        } catch (error) {
            console.error('获取当前订阅失败:', error);
        } finally{
            setSubscriptionLoading(false);
        }
    };
    // 获取当前团队详情
    const fetchCurrentTeamDetail = async ()=>{
        if (!(currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id)) return;
        try {
            setTeamLoading(true);
            const teamDetail = await _services.TeamService.getTeamDetail(currentTeam.id);
            setCurrentTeamDetail(teamDetail);
        } catch (error) {
            console.error('获取团队详情失败:', error);
        } finally{
            setTeamLoading(false);
        }
    };
    // 根据当前选中的标签页加载对应数据
    _react.default.useEffect(()=>{
        if (activeTab.startsWith('subscription')) fetchCurrentSubscription();
        else if (activeTab === 'team-detail') fetchCurrentTeamDetail();
    }, [
        activeTab,
        currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id
    ]);
    const tabItems = [
        {
            key: 'profile',
            label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                        fileName: "src/pages/personal-center/index.tsx",
                        lineNumber: 89,
                        columnNumber: 11
                    }, this),
                    "个人资料"
                ]
            }, void 0, true, {
                fileName: "src/pages/personal-center/index.tsx",
                lineNumber: 88,
                columnNumber: 9
            }, this),
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_UserProfileContent.default, {}, void 0, false, {
                fileName: "src/pages/personal-center/index.tsx",
                lineNumber: 93,
                columnNumber: 17
            }, this)
        },
        {
            key: 'teams',
            label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                        fileName: "src/pages/personal-center/index.tsx",
                        lineNumber: 99,
                        columnNumber: 11
                    }, this),
                    "我的团队"
                ]
            }, void 0, true, {
                fileName: "src/pages/personal-center/index.tsx",
                lineNumber: 98,
                columnNumber: 9
            }, this),
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamManageContent.default, {}, void 0, false, {
                fileName: "src/pages/personal-center/index.tsx",
                lineNumber: 103,
                columnNumber: 17
            }, this)
        },
        {
            key: 'friends',
            label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UsergroupAddOutlined, {}, void 0, false, {
                        fileName: "src/pages/personal-center/index.tsx",
                        lineNumber: 109,
                        columnNumber: 11
                    }, this),
                    "好友管理"
                ]
            }, void 0, true, {
                fileName: "src/pages/personal-center/index.tsx",
                lineNumber: 108,
                columnNumber: 9
            }, this),
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_FriendManageContent.default, {}, void 0, false, {
                fileName: "src/pages/personal-center/index.tsx",
                lineNumber: 113,
                columnNumber: 17
            }, this)
        },
        {
            key: 'subscription',
            label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                        fileName: "src/pages/personal-center/index.tsx",
                        lineNumber: 119,
                        columnNumber: 11
                    }, this),
                    "订阅管理"
                ]
            }, void 0, true, {
                fileName: "src/pages/personal-center/index.tsx",
                lineNumber: 118,
                columnNumber: 9
            }, this),
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_UnifiedSubscriptionContent.default, {
                currentSubscription: currentSubscription,
                loading: subscriptionLoading,
                onRefresh: fetchCurrentSubscription
            }, void 0, false, {
                fileName: "src/pages/personal-center/index.tsx",
                lineNumber: 124,
                columnNumber: 9
            }, this)
        }
    ];
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        title: "个人中心",
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tabs, {
                activeKey: activeTab,
                onChange: setActiveTab,
                items: tabItems,
                size: "large",
                tabPosition: "left"
            }, void 0, false, {
                fileName: "src/pages/personal-center/index.tsx",
                lineNumber: 136,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "src/pages/personal-center/index.tsx",
            lineNumber: 135,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "src/pages/personal-center/index.tsx",
        lineNumber: 134,
        columnNumber: 5
    }, this);
};
_s(PersonalCenterPage, "VMosU3tOpL8D49eATzdu/M4aaVU=", false, function() {
    return [
        _max.useModel
    ];
});
_c = PersonalCenterPage;
var _default = PersonalCenterPage;
var _c;
$RefreshReg$(_c, "PersonalCenterPage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/team/detail/components/TeamDetailContent.tsx": function (module, exports, __mako_require__){
/**
 * 团队详情内容组件
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _services = __mako_require__("src/services/index.ts");
var _max = __mako_require__("src/.umi/exports.ts");
var _TeamMemberList = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team/detail/components/TeamMemberList.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text } = _antd.Typography;
const { TextArea } = _antd.Input;
const TeamDetailContent = ({ teamDetail, loading, onRefresh })=>{
    _s();
    const [editModalVisible, setEditModalVisible] = (0, _react.useState)(false);
    const [updating, setUpdating] = (0, _react.useState)(false);
    const [deleting, setDeleting] = (0, _react.useState)(false);
    const [form] = _antd.Form.useForm();
    const { setInitialState } = (0, _max.useModel)('@@initialState');
    /**
   * 处理编辑团队信息操作
   *
   * 执行步骤：
   * 1. 验证团队详情数据存在
   * 2. 将当前团队信息填充到表单中
   * 3. 显示编辑模态框
   */ const handleEdit = ()=>{
        if (!teamDetail) return;
        // 将当前团队信息填充到表单中
        form.setFieldsValue({
            name: teamDetail.name,
            description: teamDetail.description
        });
        setEditModalVisible(true);
    };
    /**
   * 处理团队信息更新操作
   *
   * 执行流程：
   * 1. 验证团队详情数据存在
   * 2. 设置更新状态，显示加载动画
   * 3. 构造更新请求数据
   * 4. 调用API更新团队信息
   * 5. 关闭编辑模态框并显示成功消息
   * 6. 刷新团队详情数据
   * 7. 处理错误情况
   *
   * @param values 表单提交的值，包含团队名称和描述
   */ const handleUpdate = async (values)=>{
        if (!teamDetail) return;
        try {
            setUpdating(true);
            // 构造更新请求数据
            const updateData = {
                name: values.name,
                description: values.description
            };
            await _services.TeamService.updateCurrentTeam(updateData);
            setEditModalVisible(false);
            _antd.message.success('团队信息更新成功');
            onRefresh(); // 刷新团队详情
        } catch (error) {
            console.error('更新团队信息失败:', error);
            _antd.message.error('更新团队信息失败，请稍后重试');
        } finally{
            setUpdating(false);
        }
    };
    /**
   * 处理删除团队操作
   *
   * 执行流程：
   * 1. 显示确认对话框，详细说明删除后果
   * 2. 用户确认后调用删除API
   * 3. 清除本地团队状态和Token
   * 4. 跳转到团队选择页面
   * 5. 处理错误情况
   *
   * 安全措施：
   * - 只有团队创建者可以看到删除按钮
   * - 二次确认防止误操作
   * - 详细说明删除后果
   */ const handleDelete = ()=>{
        if (!teamDetail) return;
        _antd.Modal.confirm({
            title: '确认删除团队',
            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {}, void 0, false, {
                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                lineNumber: 125,
                columnNumber: 13
            }, this),
            content: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("p", {
                        children: [
                            "您确定要删除团队 ",
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("strong", {
                                children: [
                                    '"',
                                    teamDetail.name,
                                    '"'
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 128,
                                columnNumber: 23
                            }, this),
                            " 吗？"
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                        lineNumber: 128,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("p", {
                        style: {
                            color: '#ff4d4f',
                            marginBottom: 0
                        },
                        children: "⚠️ 此操作不可撤销，删除后将："
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                        lineNumber: 129,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("ul", {
                        style: {
                            color: '#ff4d4f',
                            marginTop: 8,
                            paddingLeft: 20
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                children: "永久删除团队及所有相关数据"
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 133,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                children: "移除所有团队成员"
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 134,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                children: "无法恢复团队信息"
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 135,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                        lineNumber: 132,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                lineNumber: 127,
                columnNumber: 9
            }, this),
            okText: '确认删除',
            okType: 'danger',
            cancelText: '取消',
            confirmLoading: deleting,
            onOk: async ()=>{
                try {
                    setDeleting(true);
                    await _services.TeamService.deleteCurrentTeam();
                    _antd.message.success('团队删除成功');
                    // 清除当前团队状态并跳转到团队选择页面
                    await setInitialState((s)=>({
                            ...s,
                            currentTeam: null
                        }));
                    _services.AuthService.clearTeamToken();
                    _max.history.push('/user/team-select');
                } catch (error) {
                    console.error('删除团队失败:', error);
                    _antd.message.error('删除团队失败，请稍后重试');
                } finally{
                    setDeleting(false);
                }
            }
        });
    };
    if (loading) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        style: {
            textAlign: 'center',
            padding: '50px 0'
        },
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
            size: "large"
        }, void 0, false, {
            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
            lineNumber: 166,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
        lineNumber: 165,
        columnNumber: 7
    }, this);
    if (!teamDetail) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Empty, {
        image: _antd.Empty.PRESENTED_IMAGE_SIMPLE,
        description: "请先选择一个团队"
    }, void 0, false, {
        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
        lineNumber: 173,
        columnNumber: 7
    }, this);
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                style: {
                    marginBottom: 24
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            marginBottom: 16
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                        style: {
                                            fontSize: 24,
                                            color: '#1890ff'
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 186,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                        level: 3,
                                        style: {
                                            margin: 0
                                        },
                                        children: teamDetail.name
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 187,
                                        columnNumber: 13
                                    }, this),
                                    teamDetail.isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        style: {
                                            fontSize: 14
                                        },
                                        children: "(管理员)"
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 191,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 185,
                                columnNumber: 11
                            }, this),
                            teamDetail.isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                            lineNumber: 199,
                                            columnNumber: 23
                                        }, void 0),
                                        onClick: handleEdit,
                                        children: "编辑团队"
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 198,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        danger: true,
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                            lineNumber: 206,
                                            columnNumber: 23
                                        }, void 0),
                                        onClick: handleDelete,
                                        loading: deleting,
                                        children: "删除团队"
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 204,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 197,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                        lineNumber: 184,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions, {
                        column: 2,
                        bordered: true,
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                label: "成员数量",
                                children: [
                                    teamDetail.memberCount,
                                    " 人"
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 217,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                label: "创建时间",
                                children: new Date(teamDetail.createdAt).toLocaleString()
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 220,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                label: "更新时间",
                                children: new Date(teamDetail.updatedAt).toLocaleString()
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 223,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                label: "团队描述",
                                span: 2,
                                children: teamDetail.description || '暂无描述'
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 226,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                        lineNumber: 216,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                lineNumber: 183,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {}, void 0, false, {
                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                lineNumber: 232,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamMemberList.default, {
                teamId: teamDetail.id,
                isCreator: teamDetail.isCreator
            }, void 0, false, {
                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                lineNumber: 235,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: "编辑团队信息",
                open: editModalVisible,
                onCancel: ()=>setEditModalVisible(false),
                footer: null,
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                    form: form,
                    layout: "vertical",
                    onFinish: handleUpdate,
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            label: "团队名称",
                            name: "name",
                            rules: [
                                {
                                    required: true,
                                    message: '请输入团队名称'
                                },
                                {
                                    min: 2,
                                    max: 50,
                                    message: '团队名称长度应在2-50个字符之间'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                placeholder: "请输入团队名称"
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 257,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 249,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            label: "团队描述",
                            name: "description",
                            rules: [
                                {
                                    max: 200,
                                    message: '团队描述不能超过200个字符'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                rows: 4,
                                placeholder: "请输入团队描述（可选）",
                                showCount: true,
                                maxLength: 200
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 267,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 260,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            style: {
                                marginBottom: 0,
                                textAlign: 'right'
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        onClick: ()=>setEditModalVisible(false),
                                        children: "取消"
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 277,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        htmlType: "submit",
                                        loading: updating,
                                        children: "保存"
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 280,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 276,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 275,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                    lineNumber: 244,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                lineNumber: 238,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
        lineNumber: 181,
        columnNumber: 5
    }, this);
};
_s(TeamDetailContent, "UalB0vhOvdT9JI/tG4E1rE8Z8wk=", false, function() {
    return [
        _antd.Form.useForm,
        _max.useModel
    ];
});
_c = TeamDetailContent;
var _default = TeamDetailContent;
var _c;
$RefreshReg$(_c, "TeamDetailContent");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/team/detail/components/TeamMemberList.tsx": function (module, exports, __mako_require__){
/**
 * 团队成员列表组件
 *
 * 功能特性：
 * - 展示团队所有成员信息（头像、姓名、邮箱、角色、状态等）
 * - 支持成员搜索和筛选功能
 * - 提供成员管理操作（移除成员、角色变更等）
 * - 区分创建者和普通成员的权限显示
 * - 响应式表格设计，适配不同屏幕尺寸
 *
 * 权限控制：
 * - 只有团队创建者可以看到管理操作按钮
 * - 创建者不能移除自己
 * - 普通成员只能查看成员列表
 *
 * 交互设计：
 * - 支持批量操作（预留功能）
 * - 提供详细的操作确认对话框
 * - 实时更新成员状态和数量
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _services = __mako_require__("src/services/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Text } = _antd.Typography;
const { Option } = _antd.Select;
const TeamMemberList = ({ teamId, isCreator, onMemberChange })=>{
    _s();
    const [loading, setLoading] = (0, _react.useState)(true);
    const [members, setMembers] = (0, _react.useState)([]);
    const [searchText, setSearchText] = (0, _react.useState)('');
    const [filteredMembers, setFilteredMembers] = (0, _react.useState)([]);
    const [selectedRowKeys, setSelectedRowKeys] = (0, _react.useState)([]);
    const [statusFilter, setStatusFilter] = (0, _react.useState)('all');
    (0, _react.useEffect)(()=>{
        fetchMembers();
    }, [
        teamId
    ]);
    /**
   * 成员列表过滤效果
   *
   * 过滤条件：
   * 1. 搜索文本：匹配成员姓名或邮箱（不区分大小写）
   * 2. 状态筛选：全部/活跃/非活跃/创建者/普通成员
   *
   * 安全性：
   * - 添加空值检查，防止数据异常导致的错误
   * - 确保成员对象的必要属性存在
   */ (0, _react.useEffect)(()=>{
        // 过滤成员列表 - 添加空值检查
        if (!members || !Array.isArray(members)) {
            setFilteredMembers([]);
            return;
        }
        const filtered = members.filter((member)=>{
            // 确保member对象存在且有必要的属性
            if (!member || !member.name || !member.email) return false;
            // 搜索文本匹配（姓名或邮箱）
            const matchesSearch = !searchText || member.name.toLowerCase().includes(searchText.toLowerCase()) || member.email.toLowerCase().includes(searchText.toLowerCase());
            // 状态筛选匹配
            const matchesStatus = statusFilter === 'all' || statusFilter === 'active' && member.isActive || statusFilter === 'inactive' && !member.isActive || statusFilter === 'creator' && member.isCreator || statusFilter === 'member' && !member.isCreator;
            return matchesSearch && matchesStatus;
        });
        setFilteredMembers(filtered);
    }, [
        members,
        searchText,
        statusFilter
    ]);
    /**
   * 获取团队成员列表
   *
   * 功能：
   * - 调用API获取当前团队的所有成员
   * - 设置加载状态，提供用户反馈
   * - 处理错误情况，确保组件稳定性
   *
   * 数据处理：
   * - 确保返回数据为数组格式，防止渲染错误
   * - 错误时设置空数组，保持组件正常显示
   */ const fetchMembers = async ()=>{
        try {
            setLoading(true);
            const response = await _services.TeamService.getTeamMembers({
                current: 1,
                pageSize: 1000
            });
            // 确保返回的数据是数组格式，防止渲染错误
            setMembers((response === null || response === void 0 ? void 0 : response.list) || []);
        } catch (error) {
            console.error('获取团队成员失败:', error);
            _antd.message.error('获取团队成员失败');
            // 出错时设置为空数组，保持组件正常显示
            setMembers([]);
        } finally{
            setLoading(false);
        }
    };
    const handleRemoveMember = (member)=>{
        if (member.isCreator) {
            _antd.message.warning('不能移除团队创建者');
            return;
        }
        _antd.Modal.confirm({
            title: '确认移除成员',
            content: `确定要移除成员 "${member.name}" 吗？`,
            okText: '确认',
            cancelText: '取消',
            onOk: async ()=>{
                try {
                    await _services.TeamService.removeMember(member.id);
                    _antd.message.success('成员移除成功');
                    fetchMembers();
                    onMemberChange === null || onMemberChange === void 0 || onMemberChange();
                } catch (error) {
                    console.error('移除成员失败:', error);
                }
            }
        });
    };
    const handleBatchRemove = ()=>{
        const selectedMembers = members.filter((member)=>selectedRowKeys.includes(member.id) && !member.isCreator);
        if (selectedMembers.length === 0) {
            _antd.message.warning('请选择要移除的成员');
            return;
        }
        _antd.Modal.confirm({
            title: '批量移除成员',
            content: `确定要移除选中的 ${selectedMembers.length} 名成员吗？`,
            okText: '确认',
            cancelText: '取消',
            onOk: async ()=>{
                try {
                    await Promise.all(selectedMembers.map((member)=>_services.TeamService.removeMember(member.id)));
                    _antd.message.success(`成功移除 ${selectedMembers.length} 名成员`);
                    setSelectedRowKeys([]);
                    fetchMembers();
                    onMemberChange === null || onMemberChange === void 0 || onMemberChange();
                } catch (error) {
                    console.error('批量移除成员失败:', error);
                    _antd.message.error('批量移除失败');
                }
            }
        });
    };
    const columns = [
        {
            title: '成员',
            dataIndex: 'name',
            key: 'name',
            render: (name, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                            size: "small",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                                lineNumber: 221,
                                columnNumber: 38
                            }, void 0)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 221,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    children: name
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                                    lineNumber: 223,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        fontSize: 12,
                                        color: '#999'
                                    },
                                    children: record.email
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                                    lineNumber: 224,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 222,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 220,
                    columnNumber: 9
                }, this)
        },
        {
            title: '角色',
            dataIndex: 'isCreator',
            key: 'role',
            width: 100,
            render: (isCreator)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                    color: isCreator ? 'gold' : 'blue',
                    icon: isCreator ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                        fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                        lineNumber: 235,
                        columnNumber: 68
                    }, void 0) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                        fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                        lineNumber: 235,
                        columnNumber: 88
                    }, void 0),
                    children: isCreator ? '创建者' : '成员'
                }, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 235,
                    columnNumber: 9
                }, this)
        },
        {
            title: '状态',
            dataIndex: 'isActive',
            key: 'status',
            width: 80,
            render: (isActive)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                    color: isActive ? 'green' : 'red',
                    children: isActive ? '活跃' : '停用'
                }, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 246,
                    columnNumber: 9
                }, this)
        },
        {
            title: '加入时间',
            dataIndex: 'assignedAt',
            key: 'assignedAt',
            width: 150,
            render: (assignedAt)=>new Date(assignedAt).toLocaleDateString()
        },
        {
            title: '最后访问',
            dataIndex: 'lastAccessTime',
            key: 'lastAccessTime',
            width: 150,
            render: (lastAccessTime)=>{
                const date = new Date(lastAccessTime);
                const now = new Date();
                const diffDays = Math.floor((now.getTime() - date.getTime()) / 86400000);
                let color = 'green';
                if (diffDays > 7) color = 'orange';
                if (diffDays > 30) color = 'red';
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                    title: date.toLocaleString(),
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                        color: color,
                        children: diffDays === 0 ? '今天' : `${diffDays}天前`
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                        lineNumber: 274,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 273,
                    columnNumber: 11
                }, this);
            }
        },
        {
            title: '操作',
            key: 'action',
            width: 120,
            render: (_, record)=>{
                if (!isCreator || record.isCreator) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                    type: "secondary",
                    children: "-"
                }, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 287,
                    columnNumber: 18
                }, this);
                const menuItems = [
                    {
                        key: 'remove',
                        label: '移除成员',
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 294,
                            columnNumber: 19
                        }, this),
                        danger: true,
                        onClick: ()=>handleRemoveMember(record)
                    }
                ];
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    size: "small",
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "text",
                            danger: true,
                            size: "small",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                                lineNumber: 306,
                                columnNumber: 21
                            }, void 0),
                            onClick: ()=>handleRemoveMember(record),
                            children: "移除"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 302,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Dropdown, {
                            menu: {
                                items: menuItems
                            },
                            trigger: [
                                'click'
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "text",
                                size: "small",
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MoreOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                                    lineNumber: 315,
                                    columnNumber: 23
                                }, void 0)
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                                lineNumber: 312,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 311,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 301,
                    columnNumber: 11
                }, this);
            }
        }
    ];
    const rowSelection = {
        selectedRowKeys,
        onChange: (newSelectedRowKeys)=>{
            setSelectedRowKeys(newSelectedRowKeys);
        },
        getCheckboxProps: (record)=>({
                disabled: record.isCreator
            })
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
        title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                    strong: true,
                    children: "团队成员"
                }, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 338,
                    columnNumber: 11
                }, void 0),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Badge, {
                    count: filteredMembers.length,
                    showZero: true
                }, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 339,
                    columnNumber: 11
                }, void 0)
            ]
        }, void 0, true, {
            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
            lineNumber: 337,
            columnNumber: 9
        }, void 0),
        extra: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                    value: statusFilter,
                    onChange: setStatusFilter,
                    style: {
                        width: 120
                    },
                    size: "small",
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                            value: "all",
                            children: "全部"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 350,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                            value: "active",
                            children: "活跃"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 351,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                            value: "inactive",
                            children: "停用"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 352,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                            value: "creator",
                            children: "创建者"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 353,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                            value: "member",
                            children: "成员"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 354,
                            columnNumber: 13
                        }, void 0)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 344,
                    columnNumber: 11
                }, void 0),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                    placeholder: "搜索成员",
                    prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                        fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                        lineNumber: 358,
                        columnNumber: 21
                    }, void 0),
                    value: searchText,
                    onChange: (e)=>setSearchText(e.target.value),
                    style: {
                        width: 200
                    },
                    size: "small"
                }, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 356,
                    columnNumber: 11
                }, void 0)
            ]
        }, void 0, true, {
            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
            lineNumber: 343,
            columnNumber: 9
        }, void 0),
        children: [
            selectedRowKeys.length > 0 && isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                style: {
                    marginBottom: 16,
                    padding: 12,
                    background: '#f5f5f5',
                    borderRadius: 6
                },
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            children: [
                                "已选择 ",
                                selectedRowKeys.length,
                                " 名成员"
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 370,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            size: "small",
                            danger: true,
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                                lineNumber: 374,
                                columnNumber: 21
                            }, void 0),
                            onClick: handleBatchRemove,
                            children: "批量移除"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 371,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            size: "small",
                            onClick: ()=>setSelectedRowKeys([]),
                            children: "取消选择"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 379,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 369,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                lineNumber: 368,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Table, {
                columns: columns,
                dataSource: filteredMembers,
                rowKey: "id",
                loading: loading,
                rowSelection: isCreator ? rowSelection : undefined,
                pagination: {
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total)=>`共 ${total} 名成员`,
                    pageSize: 10
                }
            }, void 0, false, {
                fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                lineNumber: 389,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
        lineNumber: 335,
        columnNumber: 5
    }, this);
};
_s(TeamMemberList, "VFoYmIR+E+CHWgWFfALfNS25o10=");
_c = TeamMemberList;
var _default = TeamMemberList;
var _c;
$RefreshReg$(_c, "TeamMemberList");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=src_pages_personal-center_index_tsx-async.js.map