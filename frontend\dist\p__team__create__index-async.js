((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] || []).push([
        ['p__team__create__index'],
{ "src/pages/team/create/index.tsx": function (module, exports, __mako_require__){
/**
 * 创建团队页面
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _antdstyle = __mako_require__("node_modules/antd-style/es/index.js");
var _services = __mako_require__("src/services/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text } = _antd.Typography;
const { TextArea } = _antd.Input;
const useStyles = (0, _antdstyle.createStyles)(({ token })=>{
    return {
        container: {
            display: 'flex',
            flexDirection: 'column',
            height: '100vh',
            overflow: 'auto',
            backgroundColor: token.colorBgLayout
        },
        content: {
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '32px 16px'
        },
        header: {
            marginBottom: 40,
            textAlign: 'center'
        },
        formCard: {
            width: '100%',
            maxWidth: 500,
            marginBottom: 24
        },
        actions: {
            marginTop: 24,
            display: 'flex',
            gap: 16,
            justifyContent: 'center'
        },
        backButton: {
            marginBottom: 24
        }
    };
});
const CreateTeamPage = ()=>{
    _s();
    const [loading, setLoading] = (0, _react.useState)(false);
    const [form] = _antd.Form.useForm();
    const { styles } = useStyles();
    const { initialState, setInitialState } = (0, _max.useModel)('@@initialState');
    const handleSubmit = async (values)=>{
        setLoading(true);
        try {
            await _services.TeamService.createTeam(values);
            _antd.message.success('团队创建成功！请在团队列表中选择进入新创建的团队。');
            // 不自动进行团队登录，让用户在团队选择页面手动选择
            // 直接跳转回团队选择页面
            _max.history.push('/user/team-select');
        } catch (error) {
            console.error('创建团队失败:', error);
        } finally{
            setLoading(false);
        }
    };
    const handleBack = ()=>{
        // 检查是否有 Account Token
        if (_services.AuthService.isLoggedIn()) _max.history.push('/user/team-select');
        else _max.history.push('/user/login');
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        className: styles.container,
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
            className: styles.content,
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                    type: "text",
                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ArrowLeftOutlined, {}, void 0, false, {
                        fileName: "src/pages/team/create/index.tsx",
                        lineNumber: 90,
                        columnNumber: 17
                    }, void 0),
                    onClick: handleBack,
                    className: styles.backButton,
                    children: "返回"
                }, void 0, false, {
                    fileName: "src/pages/team/create/index.tsx",
                    lineNumber: 88,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    className: styles.header,
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        direction: "vertical",
                        align: "center",
                        size: "large",
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                style: {
                                    fontSize: 48,
                                    color: '#1890ff'
                                }
                            }, void 0, false, {
                                fileName: "src/pages/team/create/index.tsx",
                                lineNumber: 99,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                        level: 2,
                                        children: "创建团队"
                                    }, void 0, false, {
                                        fileName: "src/pages/team/create/index.tsx",
                                        lineNumber: 101,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        children: "创建一个新的团队工作空间"
                                    }, void 0, false, {
                                        fileName: "src/pages/team/create/index.tsx",
                                        lineNumber: 102,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/create/index.tsx",
                                lineNumber: 100,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/team/create/index.tsx",
                        lineNumber: 98,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team/create/index.tsx",
                    lineNumber: 97,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                    className: styles.formCard,
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                        form: form,
                        layout: "vertical",
                        onFinish: handleSubmit,
                        autoComplete: "off",
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                label: "团队名称",
                                name: "name",
                                rules: [
                                    {
                                        required: true,
                                        message: '请输入团队名称！'
                                    },
                                    {
                                        max: 100,
                                        message: '团队名称长度不能超过100字符！'
                                    },
                                    {
                                        min: 2,
                                        message: '团队名称至少需要2个字符！'
                                    }
                                ],
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                    placeholder: "请输入团队名称",
                                    size: "large"
                                }, void 0, false, {
                                    fileName: "src/pages/team/create/index.tsx",
                                    lineNumber: 123,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/team/create/index.tsx",
                                lineNumber: 114,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                label: "团队描述",
                                name: "description",
                                rules: [
                                    {
                                        max: 500,
                                        message: '团队描述长度不能超过500字符！'
                                    }
                                ],
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                    placeholder: "请输入团队描述（可选）",
                                    rows: 4,
                                    showCount: true,
                                    maxLength: 500
                                }, void 0, false, {
                                    fileName: "src/pages/team/create/index.tsx",
                                    lineNumber: 136,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/team/create/index.tsx",
                                lineNumber: 129,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    type: "primary",
                                    htmlType: "submit",
                                    loading: loading,
                                    size: "large",
                                    block: true,
                                    children: "创建团队"
                                }, void 0, false, {
                                    fileName: "src/pages/team/create/index.tsx",
                                    lineNumber: 145,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/team/create/index.tsx",
                                lineNumber: 144,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/team/create/index.tsx",
                        lineNumber: 108,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team/create/index.tsx",
                    lineNumber: 107,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    className: styles.actions,
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        direction: "vertical",
                        align: "center",
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                type: "secondary",
                                children: "创建团队后，您将成为团队的管理员"
                            }, void 0, false, {
                                fileName: "src/pages/team/create/index.tsx",
                                lineNumber: 160,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                type: "secondary",
                                children: "可以邀请其他成员加入您的团队"
                            }, void 0, false, {
                                fileName: "src/pages/team/create/index.tsx",
                                lineNumber: 163,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/team/create/index.tsx",
                        lineNumber: 159,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team/create/index.tsx",
                    lineNumber: 158,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/team/create/index.tsx",
            lineNumber: 87,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "src/pages/team/create/index.tsx",
        lineNumber: 86,
        columnNumber: 5
    }, this);
};
_s(CreateTeamPage, "oYglL6T4d7+glC/MA/QUImokXYk=", false, function() {
    return [
        _antd.Form.useForm,
        useStyles,
        _max.useModel
    ];
});
_c = CreateTeamPage;
var _default = CreateTeamPage;
var _c;
$RefreshReg$(_c, "CreateTeamPage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=p__team__create__index-async.js.map