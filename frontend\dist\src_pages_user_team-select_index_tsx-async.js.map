{"version": 3, "sources": ["src/pages/user/team-select/components/ActionButtons.tsx", "src/pages/user/team-select/components/TeamList.tsx", "src/pages/user/team-select/components/index.ts", "src/pages/user/team-select/index.tsx"], "sourcesContent": ["/**\n * 操作按钮组件\n * 用于团队选择页面的操作按钮，包括进入团队、创建团队、退出登录\n */\n\nimport React from 'react';\nimport { Button } from 'antd';\nimport { PlusOutlined } from '@ant-design/icons';\nimport { createStyles } from 'antd-style';\n\nconst useStyles = createStyles(() => {\n  return {\n    actions: {\n      marginTop: 24,\n      display: 'flex',\n      gap: 16,\n      justifyContent: 'center',\n      flexWrap: 'wrap',\n    },\n    createTeamButton: {\n      borderStyle: 'dashed',\n    },\n  };\n});\n\ninterface ActionButtonsProps {\n  hasTeams: boolean;\n  selectedTeamId: number | null;\n  loading: boolean;\n  onTeamLogin: () => void;\n  onCreateTeam: () => void;\n  onLogout: () => void;\n  showCreateButton?: boolean; // 控制是否显示创建团队按钮\n}\n\nconst ActionButtons: React.FC<ActionButtonsProps> = ({\n  hasTeams,\n  selectedTeamId,\n  loading,\n  onTeamLogin,\n  onCreateTeam,\n  onLogout,\n  showCreateButton = true, // 默认显示创建团队按钮\n}) => {\n  const { styles } = useStyles();\n\n  return (\n    <div className={styles.actions}>\n      {/* 只有当有团队时才显示进入团队按钮 */}\n      {hasTeams && (\n        <Button\n          type=\"primary\"\n          size=\"large\"\n          loading={loading}\n          disabled={!selectedTeamId}\n          onClick={onTeamLogin}\n        >\n          确认进入\n        </Button>\n      )}\n\n      {/* 根据 showCreateButton 属性控制创建团队按钮的显示 */}\n      {showCreateButton && (\n        <Button\n          size=\"large\"\n          icon={<PlusOutlined />}\n          className={styles.createTeamButton}\n          onClick={onCreateTeam}\n        >\n          创建新团队\n        </Button>\n      )}\n\n      <Button\n        size=\"large\"\n        onClick={onLogout}\n      >\n        退出登录\n      </Button>\n    </div>\n  );\n};\n\nexport default ActionButtons;\n", "/**\n * 团队列表组件\n * 用于显示用户的团队列表并支持选择\n */\n\nimport React from 'react';\nimport { List, Avatar, Typography, Space } from 'antd';\nimport { TeamOutlined, UserOutlined } from '@ant-design/icons';\nimport { createStyles } from 'antd-style';\nimport type { TeamInfo } from '@/types/api';\n\nconst { Text } = Typography;\n\nconst useStyles = createStyles(({ token }) => {\n  return {\n    teamList: {\n      width: '100%',\n    },\n    teamItem: {\n      padding: '16px 24px',\n      cursor: 'pointer',\n      borderRadius: token.borderRadius,\n      transition: 'all 0.3s',\n      '&:hover': {\n        backgroundColor: token.colorBgTextHover,\n      },\n    },\n    teamItemSelected: {\n      backgroundColor: token.colorPrimaryBg,\n      borderColor: token.colorPrimary,\n    },\n  };\n});\n\ninterface TeamListProps {\n  teams: TeamInfo[];\n  selectedTeamId: number | null;\n  onTeamSelect: (teamId: number) => void;\n}\n\nconst TeamList: React.FC<TeamListProps> = ({\n  teams,\n  selectedTeamId,\n  onTeamSelect,\n}) => {\n  const { styles } = useStyles();\n\n  if (teams.length === 0) {\n    return (\n      <div style={{ textAlign: 'center', padding: '40px 0' }}>\n        <TeamOutlined style={{ fontSize: 64, color: '#d9d9d9', marginBottom: 16 }} />\n        <Typography.Title level={4} type=\"secondary\">\n          您还没有加入任何团队\n        </Typography.Title>\n        <Text type=\"secondary\">\n          创建一个新团队或等待其他人邀请您加入\n        </Text>\n      </div>\n    );\n  }\n\n  return (\n    <List\n      className={styles.teamList}\n      dataSource={teams}\n      renderItem={(team) => (\n        <List.Item\n          className={`${styles.teamItem} ${\n            selectedTeamId === team.id ? styles.teamItemSelected : ''\n          }`}\n          onClick={() => onTeamSelect(team.id)}\n        >\n          <List.Item.Meta\n            avatar={\n              <Avatar size=\"large\" icon={<TeamOutlined />} />\n            }\n            title={\n              <Space>\n                {team.name}\n                {team.isCreator && (\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                    (创建者)\n                  </Text>\n                )}\n              </Space>\n            }\n            description={\n              <Space>\n                <UserOutlined />\n                <Text type=\"secondary\">{team.memberCount} 名成员</Text>\n                <Text type=\"secondary\">\n                  最后访问: {new Date(team.lastAccessTime).toLocaleDateString()}\n                </Text>\n              </Space>\n            }\n          />\n        </List.Item>\n      )}\n    />\n  );\n};\n\nexport default TeamList;\n", "/**\n * 团队选择页面组件导出\n */\n\nexport { default as TeamList } from './TeamList';\nexport { default as ActionButtons } from './ActionButtons';\n", "/**\n * 团队选择页面\n * 实现双阶段认证的第二阶段：团队登录\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { Card, Typography, Space, message, Spin } from 'antd';\nimport { TeamOutlined } from '@ant-design/icons';\nimport { history, useLocation, useModel } from '@umijs/max';\nimport { createStyles } from 'antd-style';\nimport { AuthService, TeamService } from '@/services';\nimport type { TeamInfo } from '@/types/api';\nimport { TeamList, ActionButtons } from './components';\n\nconst { Title, Text } = Typography;\n\nconst useStyles = createStyles(({ token }) => {\n  return {\n    container: {\n      display: 'flex',\n      flexDirection: 'column',\n      height: '100vh',\n      overflow: 'auto',\n      backgroundColor: token.colorBgLayout,\n    },\n    content: {\n      flex: 1,\n      display: 'flex',\n      flexDirection: 'column',\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: '32px 16px',\n    },\n    header: {\n      marginBottom: 40,\n      textAlign: 'center',\n    },\n    teamCard: {\n      width: '100%',\n      maxWidth: 600,\n      marginBottom: 24,\n    },\n  };\n});\n\nconst TeamSelectPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [selectedTeamId, setSelectedTeamId] = useState<number | null>(null);\n  const [teams, setTeams] = useState<TeamInfo[]>([]);\n  const { styles } = useStyles();\n  const location = useLocation();\n  const { initialState, setInitialState } = useModel('@@initialState');\n\n  useEffect(() => {\n    // 从路由状态获取团队列表\n    const teamsFromState = location.state?.teams;\n    if (teamsFromState) {\n      setTeams(teamsFromState);\n      // 不自动选中团队，需要用户手动选择\n    } else {\n      // 如果没有团队数据，重新获取\n      fetchTeams();\n    }\n  }, [location.state]);\n\n  const fetchTeams = async () => {\n    try {\n      setLoading(true);\n      const teamList = await TeamService.getUserTeams();\n      const teamInfos: TeamInfo[] = teamList.map(team => ({\n        id: team.id,\n        name: team.name,\n        isCreator: team.isCreator,\n        memberCount: team.memberCount,\n        lastAccessTime: team.updatedAt,\n      }));\n      setTeams(teamInfos);\n    } catch (error) {\n      console.error('获取团队列表失败:', error);\n      message.error('获取团队列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleTeamSelect = async () => {\n    if (!selectedTeamId) {\n      message.warning('请选择一个团队');\n      return;\n    }\n\n    setLoading(true);\n\n    try {\n      const response = await AuthService.selectTeam({ teamId: selectedTeamId });\n\n      // 检查后端返回的团队选择成功标识\n      if (response.teamSelectionSuccess && response.team && response.team.id === selectedTeamId) {\n        message.success('团队选择成功！');\n\n        // 同步更新 initialState，等待更新完成后再跳转\n        if (initialState?.fetchTeamInfo && initialState?.fetchUserInfo && setInitialState) {\n          try {\n            const [currentUser, currentTeam] = await Promise.all([\n              initialState.fetchUserInfo(),\n              initialState.fetchTeamInfo()\n            ]);\n\n            // 确保团队信息已正确获取\n            if (currentTeam && currentTeam.id === selectedTeamId) {\n              await setInitialState({\n                ...initialState,\n                currentUser,\n                currentTeam,\n              });\n\n              // 等待 initialState 更新完成后再跳转\n              setTimeout(() => {\n                history.push('/dashboard');\n              }, 100);\n            } else {\n              console.error('获取的团队信息与选择的团队不匹配');\n              message.error('团队选择失败，请重试');\n            }\n          } catch (error) {\n            console.error('更新 initialState 失败:', error);\n            message.error('团队选择失败，请重试');\n          }\n        } else {\n          // 如果没有 initialState 相关方法，直接跳转\n          history.push('/dashboard');\n        }\n      } else {\n        console.error('团队选择响应异常，未返回正确的团队信息');\n        message.error('团队选择失败，请重试');\n      }\n    } catch (error) {\n      console.error('团队选择失败:', error);\n      message.error('团队选择失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 保持兼容性的方法名\n  const handleTeamLogin = handleTeamSelect;\n\n  const handleCreateTeam = () => {\n    history.push('/team/create');\n  };\n\n  const handleLogout = () => {\n    AuthService.clearTokens();\n    history.push('/user/login');\n  };\n\n  if (loading && teams.length === 0) {\n    return (\n      <div className={styles.container}>\n        <div className={styles.content}>\n          <Spin size=\"large\" />\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={styles.container}>\n      <div className={styles.content}>\n        <div className={styles.header}>\n          <Space direction=\"vertical\" align=\"center\" size=\"large\">\n            <TeamOutlined style={{ fontSize: 48, color: '#1890ff' }} />\n            <div>\n              <Title level={2}>选择团队</Title>\n              <Text type=\"secondary\">请选择要进入的团队工作空间</Text>\n            </div>\n          </Space>\n        </div>\n\n        <Card className={styles.teamCard}>\n          <TeamList\n            teams={teams}\n            selectedTeamId={selectedTeamId}\n            onTeamSelect={setSelectedTeamId}\n          />\n        </Card>\n\n        <ActionButtons\n          hasTeams={teams.length > 0}\n          selectedTeamId={selectedTeamId}\n          loading={loading}\n          onTeamLogin={handleTeamLogin}\n          onCreateTeam={handleCreateTeam}\n          onLogout={handleLogout}\n          showCreateButton={true} // 始终显示创建团队按钮\n        />\n      </div>\n    </div>\n  );\n};\n\nexport default TeamSelectPage;\n"], "names": [], "mappings": ";;;AAAA;;;CAGC;;;;4BAgFD;;;eAAA;;;;;;;uEA9EkB;6BACK;8BACM;kCACA;;;;;;;;;;AAE7B,MAAM,YAAY,IAAA,uBAAY,EAAC;IAC7B,OAAO;QACL,SAAS;YACP,WAAW;YACX,SAAS;YACT,KAAK;YACL,gBAAgB;YAChB,UAAU;QACZ;QACA,kBAAkB;YAChB,aAAa;QACf;IACF;AACF;AAYA,MAAM,gBAA8C,CAAC,EACnD,QAAQ,EACR,cAAc,EACd,OAAO,EACP,WAAW,EACX,YAAY,EACZ,QAAQ,EACR,mBAAmB,IAAI,EACxB;;IACC,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,qBACE,2BAAC;QAAI,WAAW,OAAO,OAAO;;YAE3B,0BACC,2BAAC,YAAM;gBACL,MAAK;gBACL,MAAK;gBACL,SAAS;gBACT,UAAU,CAAC;gBACX,SAAS;0BACV;;;;;;YAMF,kCACC,2BAAC,YAAM;gBACL,MAAK;gBACL,oBAAM,2BAAC,mBAAY;;;;;gBACnB,WAAW,OAAO,gBAAgB;gBAClC,SAAS;0BACV;;;;;;0BAKH,2BAAC,YAAM;gBACL,MAAK;gBACL,SAAS;0BACV;;;;;;;;;;;;AAKP;GA9CM;;QASe;;;KATf;IAgDN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnFf;;;CAGC;;;;4BAmGD;;;eAAA;;;;;;;uEAjGkB;6BAC8B;8BACL;kCACd;;;;;;;;;;AAG7B,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;AAE3B,MAAM,YAAY,IAAA,uBAAY,EAAC,CAAC,EAAE,KAAK,EAAE;IACvC,OAAO;QACL,UAAU;YACR,OAAO;QACT;QACA,UAAU;YACR,SAAS;YACT,QAAQ;YACR,cAAc,MAAM,YAAY;YAChC,YAAY;YACZ,WAAW;gBACT,iBAAiB,MAAM,gBAAgB;YACzC;QACF;QACA,kBAAkB;YAChB,iBAAiB,MAAM,cAAc;YACrC,aAAa,MAAM,YAAY;QACjC;IACF;AACF;AAQA,MAAM,WAAoC,CAAC,EACzC,KAAK,EACL,cAAc,EACd,YAAY,EACb;;IACC,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,IAAI,MAAM,MAAM,KAAK,GACnB,qBACE,2BAAC;QAAI,OAAO;YAAE,WAAW;YAAU,SAAS;QAAS;;0BACnD,2BAAC,mBAAY;gBAAC,OAAO;oBAAE,UAAU;oBAAI,OAAO;oBAAW,cAAc;gBAAG;;;;;;0BACxE,2BAAC,gBAAU,CAAC,KAAK;gBAAC,OAAO;gBAAG,MAAK;0BAAY;;;;;;0BAG7C,2BAAC;gBAAK,MAAK;0BAAY;;;;;;;;;;;;IAO7B,qBACE,2BAAC,UAAI;QACH,WAAW,OAAO,QAAQ;QAC1B,YAAY;QACZ,YAAY,CAAC,qBACX,2BAAC,UAAI,CAAC,IAAI;gBACR,WAAW,CAAC,EAAE,OAAO,QAAQ,CAAC,CAAC,EAC7B,mBAAmB,KAAK,EAAE,GAAG,OAAO,gBAAgB,GAAG,GACxD,CAAC;gBACF,SAAS,IAAM,aAAa,KAAK,EAAE;0BAEnC,cAAA,2BAAC,UAAI,CAAC,IAAI,CAAC,IAAI;oBACb,sBACE,2BAAC,YAAM;wBAAC,MAAK;wBAAQ,oBAAM,2BAAC,mBAAY;;;;;;;;;;oBAE1C,qBACE,2BAAC,WAAK;;4BACH,KAAK,IAAI;4BACT,KAAK,SAAS,kBACb,2BAAC;gCAAK,MAAK;gCAAY,OAAO;oCAAE,UAAU;gCAAG;0CAAG;;;;;;;;;;;;oBAMtD,2BACE,2BAAC,WAAK;;0CACJ,2BAAC,mBAAY;;;;;0CACb,2BAAC;gCAAK,MAAK;;oCAAa,KAAK,WAAW;oCAAC;;;;;;;0CACzC,2BAAC;gCAAK,MAAK;;oCAAY;oCACd,IAAI,KAAK,KAAK,cAAc,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzE;GA5DM;;QAKe;;;KALf;IA8DN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtGf;;CAEC;;;;;;;;;;;IAGmB,aAAa;eAAb,sBAAa;;IADb,QAAQ;eAAR,iBAAQ;;;;;;0EAAQ;+EACK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACLzC;;;CAGC;;;;4BAsMD;;;eAAA;;;;;;wEApM2C;6BACY;8BAC1B;4BACkB;kCAClB;iCACY;mCAED;;;;;;;;;;AAExC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAElC,MAAM,YAAY,IAAA,uBAAY,EAAC,CAAC,EAAE,KAAK,EAAE;IACvC,OAAO;QACL,WAAW;YACT,SAAS;YACT,eAAe;YACf,QAAQ;YACR,UAAU;YACV,iBAAiB,MAAM,aAAa;QACtC;QACA,SAAS;YACP,MAAM;YACN,SAAS;YACT,eAAe;YACf,gBAAgB;YAChB,YAAY;YACZ,SAAS;QACX;QACA,QAAQ;YACN,cAAc;YACd,WAAW;QACb;QACA,UAAU;YACR,OAAO;YACP,UAAU;YACV,cAAc;QAChB;IACF;AACF;AAEA,MAAM,iBAA2B;;IAC/B,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,eAAQ,EAAgB;IACpE,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAa,EAAE;IACjD,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,MAAM,WAAW,IAAA,gBAAW;IAC5B,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;IAEnD,IAAA,gBAAS,EAAC;YAEe;QADvB,cAAc;QACd,MAAM,kBAAiB,kBAAA,SAAS,KAAK,cAAd,sCAAA,gBAAgB,KAAK;QAC5C,IAAI,gBACF,SAAS;aAGT,gBAAgB;QAChB;IAEJ,GAAG;QAAC,SAAS,KAAK;KAAC;IAEnB,MAAM,aAAa;QACjB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,qBAAW,CAAC,YAAY;YAC/C,MAAM,YAAwB,SAAS,GAAG,CAAC,CAAA,OAAS,CAAA;oBAClD,IAAI,KAAK,EAAE;oBACX,MAAM,KAAK,IAAI;oBACf,WAAW,KAAK,SAAS;oBACzB,aAAa,KAAK,WAAW;oBAC7B,gBAAgB,KAAK,SAAS;gBAChC,CAAA;YACA,SAAS;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,gBAAgB;YACnB,aAAO,CAAC,OAAO,CAAC;YAChB;QACF;QAEA,WAAW;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,qBAAW,CAAC,UAAU,CAAC;gBAAE,QAAQ;YAAe;YAEvE,kBAAkB;YAClB,IAAI,SAAS,oBAAoB,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,EAAE,KAAK,gBAAgB;gBACzF,aAAO,CAAC,OAAO,CAAC;gBAEhB,+BAA+B;gBAC/B,IAAI,CAAA,yBAAA,mCAAA,aAAc,aAAa,MAAI,yBAAA,mCAAA,aAAc,aAAa,KAAI,iBAChE,IAAI;oBACF,MAAM,CAAC,aAAa,YAAY,GAAG,MAAM,QAAQ,GAAG,CAAC;wBACnD,aAAa,aAAa;wBAC1B,aAAa,aAAa;qBAC3B;oBAED,cAAc;oBACd,IAAI,eAAe,YAAY,EAAE,KAAK,gBAAgB;wBACpD,MAAM,gBAAgB;4BACpB,GAAG,YAAY;4BACf;4BACA;wBACF;wBAEA,2BAA2B;wBAC3B,WAAW;4BACT,YAAO,CAAC,IAAI,CAAC;wBACf,GAAG;oBACL,OAAO;wBACL,QAAQ,KAAK,CAAC;wBACd,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,uBAAuB;oBACrC,aAAO,CAAC,KAAK,CAAC;gBAChB;qBAEA,8BAA8B;gBAC9B,YAAO,CAAC,IAAI,CAAC;YAEjB,OAAO;gBACL,QAAQ,KAAK,CAAC;gBACd,aAAO,CAAC,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,YAAY;IACZ,MAAM,kBAAkB;IAExB,MAAM,mBAAmB;QACvB,YAAO,CAAC,IAAI,CAAC;IACf;IAEA,MAAM,eAAe;QACnB,qBAAW,CAAC,WAAW;QACvB,YAAO,CAAC,IAAI,CAAC;IACf;IAEA,IAAI,WAAW,MAAM,MAAM,KAAK,GAC9B,qBACE,2BAAC;QAAI,WAAW,OAAO,SAAS;kBAC9B,cAAA,2BAAC;YAAI,WAAW,OAAO,OAAO;sBAC5B,cAAA,2BAAC,UAAI;gBAAC,MAAK;;;;;;;;;;;;;;;;IAMnB,qBACE,2BAAC;QAAI,WAAW,OAAO,SAAS;kBAC9B,cAAA,2BAAC;YAAI,WAAW,OAAO,OAAO;;8BAC5B,2BAAC;oBAAI,WAAW,OAAO,MAAM;8BAC3B,cAAA,2BAAC,WAAK;wBAAC,WAAU;wBAAW,OAAM;wBAAS,MAAK;;0CAC9C,2BAAC,mBAAY;gCAAC,OAAO;oCAAE,UAAU;oCAAI,OAAO;gCAAU;;;;;;0CACtD,2BAAC;;kDACC,2BAAC;wCAAM,OAAO;kDAAG;;;;;;kDACjB,2BAAC;wCAAK,MAAK;kDAAY;;;;;;;;;;;;;;;;;;;;;;;8BAK7B,2BAAC,UAAI;oBAAC,WAAW,OAAO,QAAQ;8BAC9B,cAAA,2BAAC,oBAAQ;wBACP,OAAO;wBACP,gBAAgB;wBAChB,cAAc;;;;;;;;;;;8BAIlB,2BAAC,yBAAa;oBACZ,UAAU,MAAM,MAAM,GAAG;oBACzB,gBAAgB;oBAChB,SAAS;oBACT,aAAa;oBACb,cAAc;oBACd,UAAU;oBACV,kBAAkB;;;;;;;;;;;;;;;;;AAK5B;GA1JM;;QAIe;QACF,gBAAW;QACc,aAAQ;;;KAN9C;IA4JN,WAAe"}