/**
 * 团队详情内容组件
 */

import React, { useState } from 'react';
import {
  Descriptions,
  Button,
  Space,
  Typography,
  message,
  Modal,
  Form,
  Input,
  Spin,
  Divider,
  Empty
} from 'antd';
import {
  TeamOutlined,
  EditOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { TeamService, AuthService } from '@/services';
import type { TeamDetailResponse, UpdateTeamRequest } from '@/types/api';
import { history, useModel } from '@umijs/max';
import TeamMemberList from './TeamMemberList';

const { Title, Text } = Typography;
const { TextArea } = Input;

interface TeamDetailContentProps {
  teamDetail: TeamDetailResponse | null;
  loading: boolean;
  onRefresh: () => void;
}

const TeamDetailContent: React.FC<TeamDetailContentProps> = ({
  teamDetail,
  loading,
  onRefresh
}) => {
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [form] = Form.useForm();
  const { setInitialState } = useModel('@@initialState');

  /**
   * 处理编辑团队信息操作
   *
   * 执行步骤：
   * 1. 验证团队详情数据存在
   * 2. 将当前团队信息填充到表单中
   * 3. 显示编辑模态框
   */
  const handleEdit = () => {
    if (!teamDetail) return;
    // 将当前团队信息填充到表单中
    form.setFieldsValue({
      name: teamDetail.name,
      description: teamDetail.description,
    });
    setEditModalVisible(true);
  };

  /**
   * 处理团队信息更新操作
   *
   * 执行流程：
   * 1. 验证团队详情数据存在
   * 2. 设置更新状态，显示加载动画
   * 3. 构造更新请求数据
   * 4. 调用API更新团队信息
   * 5. 关闭编辑模态框并显示成功消息
   * 6. 刷新团队详情数据
   * 7. 处理错误情况
   *
   * @param values 表单提交的值，包含团队名称和描述
   */
  const handleUpdate = async (values: any) => {
    if (!teamDetail) return;

    try {
      setUpdating(true);
      // 构造更新请求数据
      const updateData: UpdateTeamRequest = {
        name: values.name,
        description: values.description,
      };

      await TeamService.updateCurrentTeam(updateData);
      setEditModalVisible(false);
      message.success('团队信息更新成功');
      onRefresh(); // 刷新团队详情
    } catch (error) {
      console.error('更新团队信息失败:', error);
      message.error('更新团队信息失败，请稍后重试');
    } finally {
      setUpdating(false);
    }
  };

  /**
   * 处理删除团队操作
   *
   * 执行流程：
   * 1. 显示确认对话框，详细说明删除后果
   * 2. 用户确认后调用删除API
   * 3. 清除本地团队状态和Token
   * 4. 跳转到团队选择页面
   * 5. 处理错误情况
   *
   * 安全措施：
   * - 只有团队创建者可以看到删除按钮
   * - 二次确认防止误操作
   * - 详细说明删除后果
   */
  const handleDelete = () => {
    if (!teamDetail) return;

    Modal.confirm({
      title: '确认删除团队',
      icon: <ExclamationCircleOutlined />,
      content: (
        <div>
          <p>您确定要删除团队 <strong>"{teamDetail.name}"</strong> 吗？</p>
          <p style={{ color: '#ff4d4f', marginBottom: 0 }}>
            ⚠️ 此操作不可撤销，删除后将：
          </p>
          <ul style={{ color: '#ff4d4f', marginTop: 8, paddingLeft: 20 }}>
            <li>永久删除团队及所有相关数据</li>
            <li>移除所有团队成员</li>
            <li>无法恢复团队信息</li>
          </ul>
        </div>
      ),
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      confirmLoading: deleting,
      onOk: async () => {
        try {
          setDeleting(true);
          await TeamService.deleteCurrentTeam();
          message.success('团队删除成功');

          // 清除当前团队状态并跳转到团队选择页面
          await setInitialState((s) => ({ ...s, currentTeam: null }));
          AuthService.clearTeamToken();
          history.push('/user/team-select');
        } catch (error) {
          console.error('删除团队失败:', error);
          message.error('删除团队失败，请稍后重试');
        } finally {
          setDeleting(false);
        }
      },
    });
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px 0' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!teamDetail) {
    return (
      <Empty
        image={Empty.PRESENTED_IMAGE_SIMPLE}
        description="请先选择一个团队"
      />
    );
  }

  return (
    <div>
      {/* 团队基本信息 */}
      <div style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
          <Space>
            <TeamOutlined style={{ fontSize: 24, color: '#1890ff' }} />
            <Title level={3} style={{ margin: 0 }}>
              {teamDetail.name}
            </Title>
            {teamDetail.isCreator && (
              <Text type="secondary" style={{ fontSize: 14 }}>
                (管理员)
              </Text>
            )}
          </Space>
          {teamDetail.isCreator && (
            <Space>
              <Button
                icon={<EditOutlined />}
                onClick={handleEdit}
              >
                编辑团队
              </Button>
              <Button
                danger
                icon={<DeleteOutlined />}
                onClick={handleDelete}
                loading={deleting}
              >
                删除团队
              </Button>
            </Space>
          )}
        </div>

        <Descriptions column={2} bordered>
          <Descriptions.Item label="团队名称">
            {teamDetail.name}
          </Descriptions.Item>
          <Descriptions.Item label="成员数量">
            {teamDetail.memberCount} 人
          </Descriptions.Item>
          <Descriptions.Item label="创建时间">
            {new Date(teamDetail.createdAt).toLocaleString()}
          </Descriptions.Item>
          <Descriptions.Item label="更新时间">
            {new Date(teamDetail.updatedAt).toLocaleString()}
          </Descriptions.Item>
          <Descriptions.Item label="团队描述" span={2}>
            {teamDetail.description || '暂无描述'}
          </Descriptions.Item>
        </Descriptions>
      </div>

      <Divider />

      {/* 团队成员列表 */}
      <TeamMemberList teamId={teamDetail.id} isCreator={teamDetail.isCreator} />

      {/* 编辑团队模态框 */}
      <Modal
        title="编辑团队信息"
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleUpdate}
        >
          <Form.Item
            label="团队名称"
            name="name"
            rules={[
              { required: true, message: '请输入团队名称' },
              { min: 2, max: 50, message: '团队名称长度应在2-50个字符之间' }
            ]}
          >
            <Input placeholder="请输入团队名称" />
          </Form.Item>

          <Form.Item
            label="团队描述"
            name="description"
            rules={[
              { max: 200, message: '团队描述不能超过200个字符' }
            ]}
          >
            <TextArea
              rows={4}
              placeholder="请输入团队描述（可选）"
              showCount
              maxLength={200}
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setEditModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={updating}>
                保存
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>


    </div>
  );
};

export default TeamDetailContent;
