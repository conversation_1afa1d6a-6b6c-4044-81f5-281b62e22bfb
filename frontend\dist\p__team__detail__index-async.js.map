{"version": 3, "sources": ["src/pages/team/detail/components/EnhancedTeamDetail.tsx", "src/pages/team/detail/index.tsx"], "sourcesContent": ["/**\n * 增强版团队详情组件 - 全新UI设计\n */\n\nimport React, { useState } from 'react';\nimport { \n  Card, \n  Row, \n  Col, \n  Statistic, \n  Button, \n  Space, \n  Typography, \n  Avatar, \n  Tag, \n  Divider,\n  Progress,\n  Tooltip,\n  Badge,\n  Dropdown,\n  Menu,\n  Modal,\n  Form,\n  Input,\n  message\n} from 'antd';\nimport {\n  TeamOutlined,\n  UserOutlined,\n  CalendarOutlined,\n  EditOutlined,\n  UserAddOutlined,\n  SwapOutlined,\n  MoreOutlined,\n  CrownOutlined,\n  ClockCircleOutlined,\n  SettingOutlined,\n  ShareAltOutlined,\n  DeleteOutlined,\n  ExclamationCircleOutlined\n} from '@ant-design/icons';\nimport { TeamService } from '@/services';\nimport type { TeamDetailResponse, UpdateTeamRequest } from '@/types/api';\nimport { history, useModel } from '@umijs/max';\nimport TeamMemberList from './TeamMemberList';\nimport MemberAssignModal from './MemberAssignModal';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { TextArea } = Input;\n\ninterface EnhancedTeamDetailProps {\n  teamDetail: TeamDetailResponse;\n  loading: boolean;\n  onRefresh: () => void;\n}\n\nconst EnhancedTeamDetail: React.FC<EnhancedTeamDetailProps> = ({\n  teamDetail,\n  loading,\n  onRefresh\n}) => {\n  const [editModalVisible, setEditModalVisible] = useState(false);\n  const [assignModalVisible, setAssignModalVisible] = useState(false);\n  const [updating, setUpdating] = useState(false);\n  const [deleting, setDeleting] = useState(false);\n  const [form] = Form.useForm();\n  const { setInitialState } = useModel('@@initialState');\n\n  const handleEdit = () => {\n    form.setFieldsValue({\n      name: teamDetail.name,\n      description: teamDetail.description || '',\n    });\n    setEditModalVisible(true);\n  };\n\n  const handleUpdateTeam = async (values: UpdateTeamRequest) => {\n    try {\n      setUpdating(true);\n      await TeamService.updateTeam(teamDetail.id, values);\n      message.success('团队信息更新成功');\n      setEditModalVisible(false);\n      onRefresh();\n    } catch (error) {\n      console.error('更新团队失败:', error);\n      message.error('更新团队失败');\n    } finally {\n      setUpdating(false);\n    }\n  };\n\n  const handleDeleteTeam = () => {\n    Modal.confirm({\n      title: '确认删除团队',\n      content: '删除团队后，所有团队数据将无法恢复。确定要删除吗？',\n      icon: <ExclamationCircleOutlined />,\n      okText: '确认删除',\n      cancelText: '取消',\n      okType: 'danger',\n      onOk: async () => {\n        try {\n          setDeleting(true);\n          await TeamService.deleteTeam(teamDetail.id);\n          message.success('团队删除成功');\n          // 更新全局状态，清除当前团队\n          setInitialState((s) => ({ ...s, currentTeam: undefined }));\n          history.push('/user/team-select');\n        } catch (error) {\n          console.error('删除团队失败:', error);\n          message.error('删除团队失败');\n        } finally {\n          setDeleting(false);\n        }\n      },\n    });\n  };\n\n  const moreMenuItems = [\n    {\n      key: 'share',\n      icon: <ShareAltOutlined />,\n      label: '分享团队',\n      onClick: () => {\n        // TODO: 实现分享功能\n        message.info('分享功能开发中');\n      }\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: '团队设置',\n      onClick: () => {\n        // TODO: 实现团队设置\n        message.info('团队设置功能开发中');\n      }\n    },\n    {\n      type: 'divider'\n    },\n    {\n      key: 'delete',\n      icon: <DeleteOutlined />,\n      label: '删除团队',\n      danger: true,\n      onClick: handleDeleteTeam\n    }\n  ];\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('zh-CN', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  const getTeamStatusColor = () => {\n    const memberCount = teamDetail.memberCount;\n    if (memberCount >= 10) return '#52c41a'; // 绿色 - 活跃\n    if (memberCount >= 5) return '#faad14'; // 橙色 - 正常\n    return '#1890ff'; // 蓝色 - 小团队\n  };\n\n  const getTeamStatusText = () => {\n    const memberCount = teamDetail.memberCount;\n    if (memberCount >= 10) return '活跃团队';\n    if (memberCount >= 5) return '正常团队';\n    return '小型团队';\n  };\n\n  return (\n    <div style={{ padding: '0 24px' }}>\n      {/* 团队头部信息卡片 */}\n      <Card \n        style={{ \n          marginBottom: 24,\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          border: 'none',\n          borderRadius: 16\n        }}\n        bodyStyle={{ padding: '32px' }}\n      >\n        <Row align=\"middle\" justify=\"space-between\">\n          <Col>\n            <Space size=\"large\" align=\"center\">\n              <Avatar \n                size={80} \n                icon={<TeamOutlined />}\n                style={{ \n                  backgroundColor: 'rgba(255, 255, 255, 0.2)',\n                  color: 'white',\n                  fontSize: 32\n                }}\n              />\n              <div>\n                <Space align=\"center\" style={{ marginBottom: 8 }}>\n                  <Title level={2} style={{ color: 'white', margin: 0 }}>\n                    {teamDetail.name}\n                  </Title>\n                  {teamDetail.isCreator && (\n                    <Tag \n                      icon={<CrownOutlined />} \n                      color=\"gold\"\n                      style={{ fontSize: 12 }}\n                    >\n                      管理员\n                    </Tag>\n                  )}\n                  <Badge \n                    color={getTeamStatusColor()} \n                    text={\n                      <Text style={{ color: 'rgba(255, 255, 255, 0.8)' }}>\n                        {getTeamStatusText()}\n                      </Text>\n                    }\n                  />\n                </Space>\n                <Paragraph \n                  style={{ \n                    color: 'rgba(255, 255, 255, 0.8)', \n                    margin: 0,\n                    maxWidth: 400\n                  }}\n                  ellipsis={{ rows: 2 }}\n                >\n                  {teamDetail.description || '这个团队还没有描述'}\n                </Paragraph>\n              </div>\n            </Space>\n          </Col>\n          <Col>\n            <Space>\n              {teamDetail.isCreator && (\n                <Dropdown\n                  menu={{ items: moreMenuItems }}\n                  trigger={['click']}\n                >\n                  <Button\n                    size=\"large\"\n                    icon={<MoreOutlined />}\n                    style={{\n                      background: 'rgba(255, 255, 255, 0.1)',\n                      borderColor: 'rgba(255, 255, 255, 0.2)',\n                      color: 'white'\n                    }}\n                  >\n                    更多操作\n                  </Button>\n                </Dropdown>\n              )}\n            </Space>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 团队统计信息 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"团队成员\"\n              value={teamDetail.memberCount}\n              suffix=\"人\"\n              prefix={<UserOutlined style={{ color: '#1890ff' }} />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"创建时间\"\n              value={formatDate(teamDetail.createdAt)}\n              prefix={<CalendarOutlined style={{ color: '#52c41a' }} />}\n              valueStyle={{ color: '#52c41a', fontSize: 16 }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"最后活动\"\n              value={formatDate(teamDetail.updatedAt)}\n              prefix={<ClockCircleOutlined style={{ color: '#faad14' }} />}\n              valueStyle={{ color: '#faad14', fontSize: 16 }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <div style={{ textAlign: 'center' }}>\n              <Text type=\"secondary\" style={{ fontSize: 14 }}>团队活跃度</Text>\n              <div style={{ marginTop: 8 }}>\n                <Progress\n                  type=\"circle\"\n                  size={60}\n                  percent={Math.min(teamDetail.memberCount * 10, 100)}\n                  strokeColor={getTeamStatusColor()}\n                  format={() => (\n                    <Text style={{ fontSize: 12, color: getTeamStatusColor() }}>\n                      {teamDetail.memberCount >= 10 ? '高' : \n                       teamDetail.memberCount >= 5 ? '中' : '低'}\n                    </Text>\n                  )}\n                />\n              </div>\n            </div>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 团队成员列表 */}\n      <TeamMemberList \n        teamId={teamDetail.id}\n        isCreator={teamDetail.isCreator}\n        onMemberChange={onRefresh}\n      />\n\n      {/* 模态框组件 */}\n      <MemberAssignModal\n        visible={assignModalVisible}\n        onCancel={() => setAssignModalVisible(false)}\n        onSuccess={() => {\n          setAssignModalVisible(false);\n          onRefresh();\n        }}\n        currentTeamId={teamDetail.id}\n      />\n\n      {/* 编辑团队模态框 */}\n      <Modal\n        title=\"编辑团队信息\"\n        open={editModalVisible}\n        onCancel={() => setEditModalVisible(false)}\n        footer={null}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleUpdateTeam}\n        >\n          <Form.Item\n            label=\"团队名称\"\n            name=\"name\"\n            rules={[\n              { required: true, message: '请输入团队名称' },\n              { max: 50, message: '团队名称不能超过50个字符' }\n            ]}\n          >\n            <Input placeholder=\"请输入团队名称\" />\n          </Form.Item>\n          <Form.Item\n            label=\"团队描述\"\n            name=\"description\"\n            rules={[\n              { max: 200, message: '团队描述不能超过200个字符' }\n            ]}\n          >\n            <TextArea\n              rows={4}\n              placeholder=\"请输入团队描述（可选）\"\n              showCount\n              maxLength={200}\n            />\n          </Form.Item>\n          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>\n            <Space>\n              <Button onClick={() => setEditModalVisible(false)}>\n                取消\n              </Button>\n              <Button type=\"primary\" htmlType=\"submit\" loading={updating}>\n                保存\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default EnhancedTeamDetail;\n", "/**\n * 团队详情页面\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { \n  Card, \n  Descriptions, \n  Button, \n  Space, \n  Typography, \n  message, \n  Modal, \n  Form, \n  Input,\n  Spin,\n  Divider\n} from 'antd';\nimport {\n  TeamOutlined,\n  EditOutlined,\n  UserAddOutlined,\n  SettingOutlined,\n  SwapOutlined\n} from '@ant-design/icons';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { TeamService } from '@/services';\nimport type { TeamDetailResponse, UpdateTeamRequest } from '@/types/api';\nimport TeamMemberList from './components/TeamMemberList';\nimport MemberAssignModal from './components/MemberAssignModal';\nimport EnhancedTeamDetail from './components/EnhancedTeamDetail';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\n\nconst TeamDetailPage: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [teamDetail, setTeamDetail] = useState<TeamDetailResponse | null>(null);\n  const [useEnhancedUI, setUseEnhancedUI] = useState(true); // 控制是否使用新UI\n\n  useEffect(() => {\n    fetchTeamDetail();\n  }, []);\n\n  const fetchTeamDetail = async () => {\n    try {\n      setLoading(true);\n      const detail = await TeamService.getCurrentTeamDetail();\n      setTeamDetail(detail);\n    } catch (error) {\n      console.error('获取团队详情失败:', error);\n      message.error('获取团队详情失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 经典版UI的处理方法\n  const [editModalVisible, setEditModalVisible] = useState(false);\n  const [assignModalVisible, setAssignModalVisible] = useState(false);\n  const [updating, setUpdating] = useState(false);\n  const [form] = Form.useForm();\n\n  const handleEdit = () => {\n    if (teamDetail) {\n      form.setFieldsValue({\n        name: teamDetail.name,\n        description: teamDetail.description,\n      });\n      setEditModalVisible(true);\n    }\n  };\n\n  const handleUpdate = async (values: UpdateTeamRequest) => {\n    try {\n      setUpdating(true);\n      const updatedTeam = await TeamService.updateCurrentTeam(values);\n      setTeamDetail(updatedTeam);\n      setEditModalVisible(false);\n      message.success('团队信息更新成功');\n    } catch (error) {\n      console.error('更新团队信息失败:', error);\n    } finally {\n      setUpdating(false);\n    }\n  };\n\n  const handleAssignSuccess = () => {\n    setAssignModalVisible(false);\n    fetchTeamDetail(); // 刷新团队详情\n    message.success('成员分配成功');\n  };\n\n  if (loading) {\n    return (\n      <PageContainer>\n        <div style={{ textAlign: 'center', padding: '50px 0' }}>\n          <Spin size=\"large\" />\n        </div>\n      </PageContainer>\n    );\n  }\n\n  if (!teamDetail) {\n    return (\n      <PageContainer>\n        <div style={{ textAlign: 'center', padding: '50px 0' }}>\n          <Text type=\"secondary\">团队信息加载失败</Text>\n        </div>\n      </PageContainer>\n    );\n  }\n\n  // 使用新的增强版UI\n  if (useEnhancedUI) {\n    return (\n      <PageContainer\n        title={\n          <Space>\n            <Button\n              type=\"text\"\n              size=\"small\"\n              onClick={() => setUseEnhancedUI(false)}\n              style={{ color: '#666' }}\n            >\n              切换到经典版\n            </Button>\n          </Space>\n        }\n        style={{ background: '#f5f5f5' }}\n      >\n        <EnhancedTeamDetail\n          teamDetail={teamDetail}\n          loading={loading}\n          onRefresh={fetchTeamDetail}\n        />\n      </PageContainer>\n    );\n  }\n\n  // 原有的经典UI\n  return (\n    <PageContainer\n      title={\n        <Space>\n          <TeamOutlined />\n          {teamDetail.name}\n          {teamDetail.isCreator && (\n            <Text type=\"secondary\" style={{ fontSize: 14 }}>\n              (管理员)\n            </Text>\n          )}\n        </Space>\n      }\n      extra={\n        <Space>\n          <Button\n            type=\"text\"\n            size=\"small\"\n            onClick={() => setUseEnhancedUI(true)}\n            style={{ color: '#1890ff' }}\n          >\n            切换到增强版\n          </Button>\n          {teamDetail.isCreator && (\n            <>\n              <Button\n                icon={<SwapOutlined />}\n                onClick={() => setAssignModalVisible(true)}\n              >\n                分配成员\n              </Button>\n              <Button\n                icon={<EditOutlined />}\n                onClick={handleEdit}\n              >\n                编辑团队\n              </Button>\n            </>\n          )}\n        </Space>\n      }\n    >\n      <Card title=\"团队信息\" style={{ marginBottom: 24 }}>\n        <Descriptions column={2}>\n          <Descriptions.Item label=\"团队名称\">\n            {teamDetail.name}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"成员数量\">\n            {teamDetail.memberCount} 人\n          </Descriptions.Item>\n          <Descriptions.Item label=\"创建时间\">\n            {new Date(teamDetail.createdAt).toLocaleString()}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"最后更新\">\n            {new Date(teamDetail.updatedAt).toLocaleString()}\n          </Descriptions.Item>\n          {teamDetail.description && (\n            <Descriptions.Item label=\"团队描述\" span={2}>\n              {teamDetail.description}\n            </Descriptions.Item>\n          )}\n        </Descriptions>\n      </Card>\n\n      <TeamMemberList \n        teamId={teamDetail.id}\n        isCreator={teamDetail.isCreator}\n        onMemberChange={fetchTeamDetail}\n      />\n\n      {/* 编辑团队模态框 */}\n      <Modal\n        title=\"编辑团队信息\"\n        open={editModalVisible}\n        onCancel={() => setEditModalVisible(false)}\n        footer={null}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleUpdate}\n        >\n          <Form.Item\n            label=\"团队名称\"\n            name=\"name\"\n            rules={[\n              { required: true, message: '请输入团队名称！' },\n              { max: 100, message: '团队名称长度不能超过100字符！' },\n            ]}\n          >\n            <Input placeholder=\"请输入团队名称\" />\n          </Form.Item>\n\n          <Form.Item\n            label=\"团队描述\"\n            name=\"description\"\n            rules={[\n              { max: 500, message: '团队描述长度不能超过500字符！' },\n            ]}\n          >\n            <TextArea\n              placeholder=\"请输入团队描述（可选）\"\n              rows={4}\n              showCount\n              maxLength={500}\n            />\n          </Form.Item>\n\n          <Form.Item>\n            <Space>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                loading={updating}\n              >\n                保存\n              </Button>\n              <Button onClick={() => setEditModalVisible(false)}>\n                取消\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 成员分配模态框 */}\n      <MemberAssignModal\n        visible={assignModalVisible}\n        onCancel={() => setAssignModalVisible(false)}\n        onSuccess={handleAssignSuccess}\n        currentTeamId={teamDetail.id}\n      />\n    </PageContainer>\n  );\n};\n\nexport default TeamDetailPage;\n"], "names": [], "mappings": ";;;AAAA;;CAEC;;;;4BA4XD;;;eAAA;;;;;;;0DA1XgC;6BAqBzB;8BAeA;iCACqB;4BAEM;kEACP;qEACG;;;;;;;;;;AAE9B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,gBAAU;AAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAK;AAQ1B,MAAM,qBAAwD,CAAC,EAC7D,UAAU,EACV,OAAO,EACP,SAAS,EACV;;IACC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;IACzD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;IAC7D,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;IACzC,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;IAC3B,MAAM,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;IAUrC,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,YAAY;YACZ,MAAM,qBAAW,CAAC,UAAU,CAAC,WAAW,EAAE,EAAE;YAC5C,aAAO,CAAC,OAAO,CAAC;YAChB,oBAAoB;YACpB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,mBAAmB;QACvB,WAAK,CAAC,OAAO,CAAC;YACZ,OAAO;YACP,SAAS;YACT,MAAM,2BAAC,gCAAyB;;;;;YAChC,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,MAAM;gBACJ,IAAI;oBACF,YAAY;oBACZ,MAAM,qBAAW,CAAC,UAAU,CAAC,WAAW,EAAE;oBAC1C,aAAO,CAAC,OAAO,CAAC;oBAEhB,gBAAgB,CAAC,IAAO,CAAA;4BAAE,GAAG,CAAC;4BAAE,aAAa;wBAAU,CAAA;oBACvD,YAAO,CAAC,IAAI,CAAC;gBACf,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,WAAW;oBACzB,aAAO,CAAC,KAAK,CAAC;gBAChB,SAAU;oBACR,YAAY;gBACd;YACF;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB;YACE,KAAK;YACL,MAAM,2BAAC,uBAAgB;;;;;YACvB,OAAO;YACP,SAAS;gBAEP,aAAO,CAAC,IAAI,CAAC;YACf;QACF;QACA;YACE,KAAK;YACL,MAAM,2BAAC,sBAAe;;;;;YACtB,OAAO;YACP,SAAS;gBAEP,aAAO,CAAC,IAAI,CAAC;YACf;QACF;QACA;YACE,MAAM;QACR;QACA;YACE,KAAK;YACL,MAAM,2BAAC,qBAAc;;;;;YACrB,OAAO;YACP,QAAQ;YACR,SAAS;QACX;KACD;IAED,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,qBAAqB;QACzB,MAAM,cAAc,WAAW,WAAW;QAC1C,IAAI,eAAe,IAAI,OAAO;QAC9B,IAAI,eAAe,GAAG,OAAO;QAC7B,OAAO;IACT;IAEA,MAAM,oBAAoB;QACxB,MAAM,cAAc,WAAW,WAAW;QAC1C,IAAI,eAAe,IAAI,OAAO;QAC9B,IAAI,eAAe,GAAG,OAAO;QAC7B,OAAO;IACT;IAEA,OACE,2BAAC;QAAI,OAAO;YAAE,SAAS;QAAS;;YAE9B,2BAAC,UAAI;gBACH,OAAO;oBACL,cAAc;oBACd,YAAY;oBACZ,QAAQ;oBACR,cAAc;gBAChB;gBACA,WAAW;oBAAE,SAAS;gBAAO;0BAE7B,2BAAC,SAAG;oBAAC,OAAM;oBAAS,SAAQ;;wBAC1B,2BAAC,SAAG;sCACF,2BAAC,WAAK;gCAAC,MAAK;gCAAQ,OAAM;;oCACxB,2BAAC,YAAM;wCACL,MAAM;wCACN,MAAM,2BAAC,mBAAY;;;;;wCACnB,OAAO;4CACL,iBAAiB;4CACjB,OAAO;4CACP,UAAU;wCACZ;;;;;;oCAEF,2BAAC;;4CACC,2BAAC,WAAK;gDAAC,OAAM;gDAAS,OAAO;oDAAE,cAAc;gDAAE;;oDAC7C,2BAAC;wDAAM,OAAO;wDAAG,OAAO;4DAAE,OAAO;4DAAS,QAAQ;wDAAE;kEACjD,WAAW,IAAI;;;;;;oDAEjB,WAAW,SAAS,IACnB,2BAAC,SAAG;wDACF,MAAM,2BAAC,oBAAa;;;;;wDACpB,OAAM;wDACN,OAAO;4DAAE,UAAU;wDAAG;kEACvB;;;;;;oDAIH,2BAAC,WAAK;wDACJ,OAAO;wDACP,MACE,2BAAC;4DAAK,OAAO;gEAAE,OAAO;4DAA2B;sEAC9C;;;;;;;;;;;;;;;;;4CAKT,2BAAC;gDACC,OAAO;oDACL,OAAO;oDACP,QAAQ;oDACR,UAAU;gDACZ;gDACA,UAAU;oDAAE,MAAM;gDAAE;0DAEnB,WAAW,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;;wBAKnC,2BAAC,SAAG;sCACF,2BAAC,WAAK;0CACH,WAAW,SAAS,IACnB,2BAAC,cAAQ;oCACP,MAAM;wCAAE,OAAO;oCAAc;oCAC7B,SAAS;wCAAC;qCAAQ;8CAElB,2BAAC,YAAM;wCACL,MAAK;wCACL,MAAM,2BAAC,mBAAY;;;;;wCACnB,OAAO;4CACL,YAAY;4CACZ,aAAa;4CACb,OAAO;wCACT;kDACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAWb,2BAAC,SAAG;gBAAC,QAAQ;oBAAC;oBAAI;iBAAG;gBAAE,OAAO;oBAAE,cAAc;gBAAG;;oBAC/C,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAI,IAAI;kCACvB,2BAAC,UAAI;sCACH,2BAAC,eAAS;gCACR,OAAM;gCACN,OAAO,WAAW,WAAW;gCAC7B,QAAO;gCACP,QAAQ,2BAAC,mBAAY;oCAAC,OAAO;wCAAE,OAAO;oCAAU;;;;;;gCAChD,YAAY;oCAAE,OAAO;gCAAU;;;;;;;;;;;;;;;;oBAIrC,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAI,IAAI;kCACvB,2BAAC,UAAI;sCACH,2BAAC,eAAS;gCACR,OAAM;gCACN,OAAO,WAAW,WAAW,SAAS;gCACtC,QAAQ,2BAAC,uBAAgB;oCAAC,OAAO;wCAAE,OAAO;oCAAU;;;;;;gCACpD,YAAY;oCAAE,OAAO;oCAAW,UAAU;gCAAG;;;;;;;;;;;;;;;;oBAInD,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAI,IAAI;kCACvB,2BAAC,UAAI;sCACH,2BAAC,eAAS;gCACR,OAAM;gCACN,OAAO,WAAW,WAAW,SAAS;gCACtC,QAAQ,2BAAC,0BAAmB;oCAAC,OAAO;wCAAE,OAAO;oCAAU;;;;;;gCACvD,YAAY;oCAAE,OAAO;oCAAW,UAAU;gCAAG;;;;;;;;;;;;;;;;oBAInD,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAI,IAAI;kCACvB,2BAAC,UAAI;sCACH,2BAAC;gCAAI,OAAO;oCAAE,WAAW;gCAAS;;oCAChC,2BAAC;wCAAK,MAAK;wCAAY,OAAO;4CAAE,UAAU;wCAAG;kDAAG;;;;;;oCAChD,2BAAC;wCAAI,OAAO;4CAAE,WAAW;wCAAE;kDACzB,2BAAC,cAAQ;4CACP,MAAK;4CACL,MAAM;4CACN,SAAS,KAAK,GAAG,CAAC,WAAW,WAAW,GAAG,IAAI;4CAC/C,aAAa;4CACb,QAAQ,IACN,2BAAC;oDAAK,OAAO;wDAAE,UAAU;wDAAI,OAAO;oDAAqB;8DACtD,WAAW,WAAW,IAAI,KAAK,MAC/B,WAAW,WAAW,IAAI,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAWrD,2BAAC,uBAAc;gBACb,QAAQ,WAAW,EAAE;gBACrB,WAAW,WAAW,SAAS;gBAC/B,gBAAgB;;;;;;YAIlB,2BAAC,0BAAiB;gBAChB,SAAS;gBACT,UAAU,IAAM,sBAAsB;gBACtC,WAAW;oBACT,sBAAsB;oBACtB;gBACF;gBACA,eAAe,WAAW,EAAE;;;;;;YAI9B,2BAAC,WAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU,IAAM,oBAAoB;gBACpC,QAAQ;gBACR,OAAO;0BAEP,2BAAC,UAAI;oBACH,MAAM;oBACN,QAAO;oBACP,UAAU;;wBAEV,2BAAC,UAAI,CAAC,IAAI;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO;gCACL;oCAAE,UAAU;oCAAM,SAAS;gCAAU;gCACrC;oCAAE,KAAK;oCAAI,SAAS;gCAAgB;6BACrC;sCAED,2BAAC,WAAK;gCAAC,aAAY;;;;;;;;;;;wBAErB,2BAAC,UAAI,CAAC,IAAI;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO;gCACL;oCAAE,KAAK;oCAAK,SAAS;gCAAiB;6BACvC;sCAED,2BAAC;gCACC,MAAM;gCACN,aAAY;gCACZ,SAAS;gCACT,WAAW;;;;;;;;;;;wBAGf,2BAAC,UAAI,CAAC,IAAI;4BAAC,OAAO;gCAAE,cAAc;gCAAG,WAAW;4BAAQ;sCACtD,2BAAC,WAAK;;oCACJ,2BAAC,YAAM;wCAAC,SAAS,IAAM,oBAAoB;kDAAQ;;;;;;oCAGnD,2BAAC,YAAM;wCAAC,MAAK;wCAAU,UAAS;wCAAS,SAAS;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1E;GApUM;;QASW,UAAI,CAAC;QACQ,aAAQ;;;KAVhC;IAsUN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9Xf;;CAEC;;;;4BAmRD;;;eAAA;;;;;;;0DAjR2C;6BAapC;8BAOA;sCACuB;iCACF;kEAED;qEACG;sEACC;;;;;;;;;;AAE/B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAClC,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAK;AAE1B,MAAM,iBAA2B;;IAC/B,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAA4B;IACxE,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAC;IAEnD,IAAA,gBAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,WAAW;YACX,MAAM,SAAS,MAAM,qBAAW,CAAC,oBAAoB;YACrD,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAGA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;IACzD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;IAC7D,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;IACzC,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;IAE3B,MAAM,aAAa;QACjB,IAAI,YAAY;YACd,KAAK,cAAc,CAAC;gBAClB,MAAM,WAAW,IAAI;gBACrB,aAAa,WAAW,WAAW;YACrC;YACA,oBAAoB;QACtB;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,YAAY;YACZ,MAAM,cAAc,MAAM,qBAAW,CAAC,iBAAiB,CAAC;YACxD,cAAc;YACd,oBAAoB;YACpB,aAAO,CAAC,OAAO,CAAC;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,sBAAsB;QAC1B,sBAAsB;QACtB;QACA,aAAO,CAAC,OAAO,CAAC;IAClB;IAEA,IAAI,SACF,OACE,2BAAC,4BAAa;kBACZ,2BAAC;YAAI,OAAO;gBAAE,WAAW;gBAAU,SAAS;YAAS;sBACnD,2BAAC,UAAI;gBAAC,MAAK;;;;;;;;;;;;;;;;IAMnB,IAAI,CAAC,YACH,OACE,2BAAC,4BAAa;kBACZ,2BAAC;YAAI,OAAO;gBAAE,WAAW;gBAAU,SAAS;YAAS;sBACnD,2BAAC;gBAAK,MAAK;0BAAY;;;;;;;;;;;;;;;;IAO/B,IAAI,eACF,OACE,2BAAC,4BAAa;QACZ,OACE,2BAAC,WAAK;sBACJ,2BAAC,YAAM;gBACL,MAAK;gBACL,MAAK;gBACL,SAAS,IAAM,iBAAiB;gBAChC,OAAO;oBAAE,OAAO;gBAAO;0BACxB;;;;;;;;;;;QAKL,OAAO;YAAE,YAAY;QAAU;kBAE/B,2BAAC,2BAAkB;YACjB,YAAY;YACZ,SAAS;YACT,WAAW;;;;;;;;;;;IAOnB,OACE,2BAAC,4BAAa;QACZ,OACE,2BAAC,WAAK;;gBACJ,2BAAC,mBAAY;;;;;gBACZ,WAAW,IAAI;gBACf,WAAW,SAAS,IACnB,2BAAC;oBAAK,MAAK;oBAAY,OAAO;wBAAE,UAAU;oBAAG;8BAAG;;;;;;;;;;;;QAMtD,OACE,2BAAC,WAAK;;gBACJ,2BAAC,YAAM;oBACL,MAAK;oBACL,MAAK;oBACL,SAAS,IAAM,iBAAiB;oBAChC,OAAO;wBAAE,OAAO;oBAAU;8BAC3B;;;;;;gBAGA,WAAW,SAAS,IACnB;;wBACE,2BAAC,YAAM;4BACL,MAAM,2BAAC,mBAAY;;;;;4BACnB,SAAS,IAAM,sBAAsB;sCACtC;;;;;;wBAGD,2BAAC,YAAM;4BACL,MAAM,2BAAC,mBAAY;;;;;4BACnB,SAAS;sCACV;;;;;;;;;;;;;;;YAQT,2BAAC,UAAI;gBAAC,OAAM;gBAAO,OAAO;oBAAE,cAAc;gBAAG;0BAC3C,2BAAC,kBAAY;oBAAC,QAAQ;;wBACpB,2BAAC,kBAAY,CAAC,IAAI;4BAAC,OAAM;sCACtB,WAAW,IAAI;;;;;;wBAElB,2BAAC,kBAAY,CAAC,IAAI;4BAAC,OAAM;;gCACtB,WAAW,WAAW;gCAAC;;;;;;;wBAE1B,2BAAC,kBAAY,CAAC,IAAI;4BAAC,OAAM;sCACtB,IAAI,KAAK,WAAW,SAAS,EAAE,cAAc;;;;;;wBAEhD,2BAAC,kBAAY,CAAC,IAAI;4BAAC,OAAM;sCACtB,IAAI,KAAK,WAAW,SAAS,EAAE,cAAc;;;;;;wBAE/C,WAAW,WAAW,IACrB,2BAAC,kBAAY,CAAC,IAAI;4BAAC,OAAM;4BAAO,MAAM;sCACnC,WAAW,WAAW;;;;;;;;;;;;;;;;;YAM/B,2BAAC,uBAAc;gBACb,QAAQ,WAAW,EAAE;gBACrB,WAAW,WAAW,SAAS;gBAC/B,gBAAgB;;;;;;YAIlB,2BAAC,WAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU,IAAM,oBAAoB;gBACpC,QAAQ;0BAER,2BAAC,UAAI;oBACH,MAAM;oBACN,QAAO;oBACP,UAAU;;wBAEV,2BAAC,UAAI,CAAC,IAAI;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO;gCACL;oCAAE,UAAU;oCAAM,SAAS;gCAAW;gCACtC;oCAAE,KAAK;oCAAK,SAAS;gCAAmB;6BACzC;sCAED,2BAAC,WAAK;gCAAC,aAAY;;;;;;;;;;;wBAGrB,2BAAC,UAAI,CAAC,IAAI;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO;gCACL;oCAAE,KAAK;oCAAK,SAAS;gCAAmB;6BACzC;sCAED,2BAAC;gCACC,aAAY;gCACZ,MAAM;gCACN,SAAS;gCACT,WAAW;;;;;;;;;;;wBAIf,2BAAC,UAAI,CAAC,IAAI;sCACR,2BAAC,WAAK;;oCACJ,2BAAC,YAAM;wCACL,MAAK;wCACL,UAAS;wCACT,SAAS;kDACV;;;;;;oCAGD,2BAAC,YAAM;wCAAC,SAAS,IAAM,oBAAoB;kDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAS3D,2BAAC,0BAAiB;gBAChB,SAAS;gBACT,UAAU,IAAM,sBAAsB;gBACtC,WAAW;gBACX,eAAe,WAAW,EAAE;;;;;;;;;;;;AAIpC;GAhPM;;QA0BW,UAAI,CAAC;;;KA1BhB;IAkPN,WAAe"}