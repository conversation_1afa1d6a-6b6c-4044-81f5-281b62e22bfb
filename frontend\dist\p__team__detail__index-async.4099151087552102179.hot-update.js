globalThis.makoModuleHotUpdate('p__team__detail__index', {
    modules: {
        "src/pages/team/detail/components/EnhancedTeamDetail.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _services = __mako_require__("src/services/index.ts");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _TeamMemberList = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team/detail/components/TeamMemberList.tsx"));
            var _MemberAssignModal = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team/detail/components/MemberAssignModal.tsx"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Title, Text, Paragraph } = _antd.Typography;
            const { TextArea } = _antd.Input;
            const EnhancedTeamDetail = ({ teamDetail, loading, onRefresh })=>{
                _s();
                const [editModalVisible, setEditModalVisible] = (0, _react.useState)(false);
                const [assignModalVisible, setAssignModalVisible] = (0, _react.useState)(false);
                const [updating, setUpdating] = (0, _react.useState)(false);
                const [deleting, setDeleting] = (0, _react.useState)(false);
                const [form] = _antd.Form.useForm();
                const { setInitialState } = (0, _max.useModel)('@@initialState');
                const handleUpdateTeam = async (values)=>{
                    try {
                        setUpdating(true);
                        await _services.TeamService.updateTeam(teamDetail.id, values);
                        _antd.message.success('团队信息更新成功');
                        setEditModalVisible(false);
                        onRefresh();
                    } catch (error) {
                        console.error('更新团队失败:', error);
                        _antd.message.error('更新团队失败');
                    } finally{
                        setUpdating(false);
                    }
                };
                const handleDeleteTeam = ()=>{
                    _antd.Modal.confirm({
                        title: '确认删除团队',
                        content: '删除团队后，所有团队数据将无法恢复。确定要删除吗？',
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {}, void 0, false, {
                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                            lineNumber: 96,
                            columnNumber: 13
                        }, this),
                        okText: '确认删除',
                        cancelText: '取消',
                        okType: 'danger',
                        onOk: async ()=>{
                            try {
                                setDeleting(true);
                                await _services.TeamService.deleteTeam(teamDetail.id);
                                _antd.message.success('团队删除成功');
                                // 更新全局状态，清除当前团队
                                setInitialState((s)=>({
                                        ...s,
                                        currentTeam: undefined
                                    }));
                                _max.history.push('/user/team-select');
                            } catch (error) {
                                console.error('删除团队失败:', error);
                                _antd.message.error('删除团队失败');
                            } finally{
                                setDeleting(false);
                            }
                        }
                    });
                };
                const moreMenuItems = [
                    {
                        key: 'share',
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ShareAltOutlined, {}, void 0, false, {
                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                            lineNumber: 121,
                            columnNumber: 13
                        }, this),
                        label: '分享团队',
                        onClick: ()=>{
                            // TODO: 实现分享功能
                            _antd.message.info('分享功能开发中');
                        }
                    },
                    {
                        key: 'settings',
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {}, void 0, false, {
                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                            lineNumber: 130,
                            columnNumber: 13
                        }, this),
                        label: '团队设置',
                        onClick: ()=>{
                            // TODO: 实现团队设置
                            _antd.message.info('团队设置功能开发中');
                        }
                    },
                    {
                        type: 'divider'
                    },
                    {
                        key: 'delete',
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                            lineNumber: 142,
                            columnNumber: 13
                        }, this),
                        label: '删除团队',
                        danger: true,
                        onClick: handleDeleteTeam
                    }
                ];
                const formatDate = (dateString)=>{
                    return new Date(dateString).toLocaleDateString('zh-CN', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                    });
                };
                const getTeamStatusColor = ()=>{
                    const memberCount = teamDetail.memberCount;
                    if (memberCount >= 10) return '#52c41a'; // 绿色 - 活跃
                    if (memberCount >= 5) return '#faad14'; // 橙色 - 正常
                    return '#1890ff'; // 蓝色 - 小团队
                };
                const getTeamStatusText = ()=>{
                    const memberCount = teamDetail.memberCount;
                    if (memberCount >= 10) return '活跃团队';
                    if (memberCount >= 5) return '正常团队';
                    return '小型团队';
                };
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        padding: '0 24px'
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            style: {
                                marginBottom: 24,
                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                                border: 'none',
                                borderRadius: 16
                            },
                            bodyStyle: {
                                padding: '32px'
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                align: "middle",
                                justify: "space-between",
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            size: "large",
                                            align: "center",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                                    size: 80,
                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                        lineNumber: 188,
                                                        columnNumber: 23
                                                    }, void 0),
                                                    style: {
                                                        backgroundColor: 'rgba(255, 255, 255, 0.2)',
                                                        color: 'white',
                                                        fontSize: 32
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                    lineNumber: 186,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                            align: "center",
                                                            style: {
                                                                marginBottom: 8
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                                    level: 2,
                                                                    style: {
                                                                        color: 'white',
                                                                        margin: 0
                                                                    },
                                                                    children: teamDetail.name
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                                    lineNumber: 197,
                                                                    columnNumber: 19
                                                                }, this),
                                                                teamDetail.isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                                                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                                        lineNumber: 202,
                                                                        columnNumber: 29
                                                                    }, void 0),
                                                                    color: "gold",
                                                                    style: {
                                                                        fontSize: 12
                                                                    },
                                                                    children: "管理员"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                                    lineNumber: 201,
                                                                    columnNumber: 21
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Badge, {
                                                                    color: getTeamStatusColor(),
                                                                    text: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        style: {
                                                                            color: 'rgba(255, 255, 255, 0.8)'
                                                                        },
                                                                        children: getTeamStatusText()
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                                        lineNumber: 212,
                                                                        columnNumber: 23
                                                                    }, void 0)
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                                    lineNumber: 209,
                                                                    columnNumber: 19
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                            lineNumber: 196,
                                                            columnNumber: 17
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                                            style: {
                                                                color: 'rgba(255, 255, 255, 0.8)',
                                                                margin: 0,
                                                                maxWidth: 400
                                                            },
                                                            ellipsis: {
                                                                rows: 2
                                                            },
                                                            children: teamDetail.description || '这个团队还没有描述'
                                                        }, void 0, false, {
                                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                            lineNumber: 218,
                                                            columnNumber: 17
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                    lineNumber: 195,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 185,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 184,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: teamDetail.isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Dropdown, {
                                                menu: {
                                                    items: moreMenuItems
                                                },
                                                trigger: [
                                                    'click'
                                                ],
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    size: "large",
                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MoreOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                        lineNumber: 240,
                                                        columnNumber: 27
                                                    }, void 0),
                                                    style: {
                                                        background: 'rgba(255, 255, 255, 0.1)',
                                                        borderColor: 'rgba(255, 255, 255, 0.2)',
                                                        color: 'white'
                                                    },
                                                    children: "更多操作"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                    lineNumber: 238,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                lineNumber: 234,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 232,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 231,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                lineNumber: 183,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                            lineNumber: 174,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                            gutter: [
                                16,
                                16
                            ],
                            style: {
                                marginBottom: 24
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 24,
                                    sm: 12,
                                    md: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                            title: "团队成员",
                                            value: teamDetail.memberCount,
                                            suffix: "人",
                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {
                                                style: {
                                                    color: '#1890ff'
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                lineNumber: 264,
                                                columnNumber: 23
                                            }, void 0),
                                            valueStyle: {
                                                color: '#1890ff'
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 260,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 259,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                    lineNumber: 258,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 24,
                                    sm: 12,
                                    md: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                            title: "创建时间",
                                            value: formatDate(teamDetail.createdAt),
                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CalendarOutlined, {
                                                style: {
                                                    color: '#52c41a'
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                lineNumber: 274,
                                                columnNumber: 23
                                            }, void 0),
                                            valueStyle: {
                                                color: '#52c41a',
                                                fontSize: 16
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 271,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 270,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                    lineNumber: 269,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 24,
                                    sm: 12,
                                    md: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                            title: "最后活动",
                                            value: formatDate(teamDetail.updatedAt),
                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ClockCircleOutlined, {
                                                style: {
                                                    color: '#faad14'
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                lineNumber: 284,
                                                columnNumber: 23
                                            }, void 0),
                                            valueStyle: {
                                                color: '#faad14',
                                                fontSize: 16
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 281,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 280,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                    lineNumber: 279,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 24,
                                    sm: 12,
                                    md: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                textAlign: 'center'
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    type: "secondary",
                                                    style: {
                                                        fontSize: 14
                                                    },
                                                    children: "团队活跃度"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                    lineNumber: 292,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        marginTop: 8
                                                    },
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Progress, {
                                                        type: "circle",
                                                        size: 60,
                                                        percent: Math.min(teamDetail.memberCount * 10, 100),
                                                        strokeColor: getTeamStatusColor(),
                                                        format: ()=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                style: {
                                                                    fontSize: 12,
                                                                    color: getTeamStatusColor()
                                                                },
                                                                children: teamDetail.memberCount >= 10 ? '高' : teamDetail.memberCount >= 5 ? '中' : '低'
                                                            }, void 0, false, {
                                                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                                lineNumber: 300,
                                                                columnNumber: 21
                                                            }, void 0)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                        lineNumber: 294,
                                                        columnNumber: 17
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                    lineNumber: 293,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 291,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 290,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                    lineNumber: 289,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                            lineNumber: 257,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamMemberList.default, {
                            teamId: teamDetail.id,
                            isCreator: teamDetail.isCreator,
                            onMemberChange: onRefresh
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                            lineNumber: 313,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_MemberAssignModal.default, {
                            visible: assignModalVisible,
                            onCancel: ()=>setAssignModalVisible(false),
                            onSuccess: ()=>{
                                setAssignModalVisible(false);
                                onRefresh();
                            },
                            currentTeamId: teamDetail.id
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                            lineNumber: 320,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                            title: "编辑团队信息",
                            open: editModalVisible,
                            onCancel: ()=>setEditModalVisible(false),
                            footer: null,
                            width: 600,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                                form: form,
                                layout: "vertical",
                                onFinish: handleUpdateTeam,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        label: "团队名称",
                                        name: "name",
                                        rules: [
                                            {
                                                required: true,
                                                message: '请输入团队名称'
                                            },
                                            {
                                                max: 50,
                                                message: '团队名称不能超过50个字符'
                                            }
                                        ],
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                            placeholder: "请输入团队名称"
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 351,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 343,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        label: "团队描述",
                                        name: "description",
                                        rules: [
                                            {
                                                max: 200,
                                                message: '团队描述不能超过200个字符'
                                            }
                                        ],
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                            rows: 4,
                                            placeholder: "请输入团队描述（可选）",
                                            showCount: true,
                                            maxLength: 200
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 360,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 353,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        style: {
                                            marginBottom: 0,
                                            textAlign: 'right'
                                        },
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    onClick: ()=>setEditModalVisible(false),
                                                    children: "取消"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                    lineNumber: 369,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    type: "primary",
                                                    htmlType: "submit",
                                                    loading: updating,
                                                    children: "保存"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                    lineNumber: 372,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 368,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 367,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                lineNumber: 338,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                            lineNumber: 331,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                    lineNumber: 172,
                    columnNumber: 5
                }, this);
            };
            _s(EnhancedTeamDetail, "6t3QdOj9q1Ia72V6K4OupTX5j68=", false, function() {
                return [
                    _antd.Form.useForm,
                    _max.useModel
                ];
            });
            _c = EnhancedTeamDetail;
            var _default = EnhancedTeamDetail;
            var _c;
            $RefreshReg$(_c, "EnhancedTeamDetail");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/pages/team/detail/components/MemberAssignModal.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _services = __mako_require__("src/services/index.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Text } = _antd.Typography;
            const { Option } = _antd.Select;
            const MemberAssignModal = ({ visible, onCancel, onSuccess, currentTeamId })=>{
                _s();
                const [loading, setLoading] = (0, _react.useState)(false);
                const [members, setMembers] = (0, _react.useState)([]);
                const [availableTargets, setAvailableTargets] = (0, _react.useState)([]);
                const [selectedMembers, setSelectedMembers] = (0, _react.useState)([]);
                const [selectedTarget, setSelectedTarget] = (0, _react.useState)();
                const [form] = _antd.Form.useForm();
                (0, _react.useEffect)(()=>{
                    if (visible) {
                        fetchMembers();
                        fetchAvailableTargets();
                    }
                }, [
                    visible
                ]);
                const fetchMembers = async ()=>{
                    try {
                        const response = await _services.TeamService.getTeamMembers({
                            current: 1,
                            pageSize: 1000
                        });
                        // 过滤掉创建者，因为创建者不能被分配
                        const assignableMembers = response.list.filter((member)=>!member.isCreator);
                        setMembers(assignableMembers);
                    } catch (error) {
                        console.error('获取团队成员失败:', error);
                        _antd.message.error('获取团队成员失败');
                    }
                };
                const fetchAvailableTargets = async ()=>{
                    try {
                        // 获取用户的其他团队作为分配目标
                        const teams = await _services.TeamService.getUserTeams();
                        const otherTeams = teams.filter((team)=>team.id !== currentTeamId).map((team)=>({
                                id: team.id,
                                name: team.name,
                                type: 'team',
                                memberCount: team.memberCount
                            }));
                        setAvailableTargets(otherTeams);
                    } catch (error) {
                        console.error('获取可分配目标失败:', error);
                        _antd.message.error('获取可分配目标失败');
                    }
                };
                const handleAssign = async ()=>{
                    if (selectedMembers.length === 0) {
                        _antd.message.warning('请选择要分配的成员');
                        return;
                    }
                    if (!selectedTarget) {
                        _antd.message.warning('请选择分配目标');
                        return;
                    }
                    try {
                        setLoading(true);
                        // 这里应该调用后端API进行成员分配
                        // 由于后端可能没有这个接口，我们模拟一个成功的响应
                        await new Promise((resolve)=>setTimeout(resolve, 1000));
                        _antd.message.success(`成功将 ${selectedMembers.length} 名成员分配到目标团队`);
                        onSuccess();
                        handleCancel();
                    } catch (error) {
                        console.error('分配成员失败:', error);
                        _antd.message.error('分配成员失败');
                    } finally{
                        setLoading(false);
                    }
                };
                const handleCancel = ()=>{
                    setSelectedMembers([]);
                    setSelectedTarget(undefined);
                    form.resetFields();
                    onCancel();
                };
                const selectedMemberDetails = members.filter((member)=>selectedMembers.includes(member.id));
                const selectedTargetDetails = availableTargets.find((target)=>target.id === selectedTarget);
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                    title: "分配团队成员",
                    open: visible,
                    onCancel: handleCancel,
                    footer: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            onClick: handleCancel,
                            children: "取消"
                        }, "cancel", false, {
                            fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                            lineNumber: 150,
                            columnNumber: 9
                        }, void 0),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "primary",
                            loading: loading,
                            onClick: handleAssign,
                            disabled: selectedMembers.length === 0 || !selectedTarget,
                            children: "确认分配"
                        }, "submit", false, {
                            fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                            lineNumber: 153,
                            columnNumber: 9
                        }, void 0)
                    ],
                    width: 800,
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                            message: "成员分配说明",
                            description: "将选中的成员从当前团队分配到其他团队。分配后，成员将离开当前团队并加入目标团队。",
                            type: "info",
                            showIcon: true,
                            style: {
                                marginBottom: 16
                            }
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                            lineNumber: 165,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                            form: form,
                            layout: "vertical",
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    label: "选择要分配的成员",
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        style: {
                                            maxHeight: 200,
                                            overflowY: 'auto',
                                            border: '1px solid #d9d9d9',
                                            borderRadius: 6,
                                            padding: 8
                                        },
                                        children: members.length === 0 ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            type: "secondary",
                                            children: "暂无可分配的成员"
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                            lineNumber: 177,
                                            columnNumber: 15
                                        }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                                            size: "small",
                                            dataSource: members,
                                            renderItem: (member)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                                                    style: {
                                                        cursor: 'pointer',
                                                        backgroundColor: selectedMembers.includes(member.id) ? '#e6f7ff' : 'transparent',
                                                        padding: '8px 12px',
                                                        borderRadius: 4,
                                                        margin: '2px 0'
                                                    },
                                                    onClick: ()=>{
                                                        if (selectedMembers.includes(member.id)) setSelectedMembers((prev)=>prev.filter((id)=>id !== member.id));
                                                        else setSelectedMembers((prev)=>[
                                                                ...prev,
                                                                member.id
                                                            ]);
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item.Meta, {
                                                            avatar: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                                                size: "small",
                                                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                                    fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                                    lineNumber: 200,
                                                                    columnNumber: 58
                                                                }, void 0)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                                lineNumber: 200,
                                                                columnNumber: 31
                                                            }, void 0),
                                                            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                children: [
                                                                    member.name,
                                                                    selectedMembers.includes(member.id) && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckCircleOutlined, {
                                                                        style: {
                                                                            color: '#1890ff'
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                                        lineNumber: 205,
                                                                        columnNumber: 29
                                                                    }, void 0)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                                lineNumber: 202,
                                                                columnNumber: 25
                                                            }, void 0),
                                                            description: member.email
                                                        }, void 0, false, {
                                                            fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                            lineNumber: 199,
                                                            columnNumber: 21
                                                        }, void 0),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                            color: member.isActive ? 'green' : 'red',
                                                            children: member.isActive ? '活跃' : '停用'
                                                        }, void 0, false, {
                                                            fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                            lineNumber: 211,
                                                            columnNumber: 21
                                                        }, void 0)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                    lineNumber: 183,
                                                    columnNumber: 19
                                                }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                            lineNumber: 179,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                        lineNumber: 175,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                    lineNumber: 174,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    label: "选择分配目标",
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                                        placeholder: "请选择目标团队",
                                        value: selectedTarget,
                                        onChange: setSelectedTarget,
                                        style: {
                                            width: '100%'
                                        },
                                        children: availableTargets.map((target)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                                                value: target.id,
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                                            fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                            lineNumber: 231,
                                                            columnNumber: 19
                                                        }, this),
                                                        target.name,
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            type: "secondary",
                                                            children: [
                                                                "(",
                                                                target.memberCount,
                                                                " 名成员)"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                            lineNumber: 233,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                    lineNumber: 230,
                                                    columnNumber: 17
                                                }, this)
                                            }, target.id, false, {
                                                fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                lineNumber: 229,
                                                columnNumber: 15
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                        lineNumber: 222,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                    lineNumber: 221,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                            lineNumber: 173,
                            columnNumber: 7
                        }, this),
                        selectedMembers.length > 0 && selectedTargetDetails && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {}, void 0, false, {
                                    fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                    lineNumber: 243,
                                    columnNumber: 11
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: "分配预览："
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                            lineNumber: 245,
                                            columnNumber: 13
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                marginTop: 8,
                                                padding: 12,
                                                background: '#f5f5f5',
                                                borderRadius: 6
                                            },
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                direction: "vertical",
                                                style: {
                                                    width: '100%'
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                children: [
                                                                    "将以下 ",
                                                                    selectedMemberDetails.length,
                                                                    " 名成员："
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                                lineNumber: 249,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                style: {
                                                                    marginTop: 4
                                                                },
                                                                children: selectedMemberDetails.map((member)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                                        color: "blue",
                                                                        style: {
                                                                            margin: '2px'
                                                                        },
                                                                        children: member.name
                                                                    }, member.id, false, {
                                                                        fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                                        lineNumber: 252,
                                                                        columnNumber: 23
                                                                    }, this))
                                                            }, void 0, false, {
                                                                fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                                lineNumber: 250,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                        lineNumber: 248,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            textAlign: 'center'
                                                        },
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SwapOutlined, {
                                                            style: {
                                                                fontSize: 16,
                                                                color: '#1890ff'
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                            lineNumber: 259,
                                                            columnNumber: 19
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                        lineNumber: 258,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                children: "分配到团队："
                                                            }, void 0, false, {
                                                                fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                                lineNumber: 262,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                                color: "green",
                                                                style: {
                                                                    marginLeft: 8
                                                                },
                                                                children: selectedTargetDetails.name
                                                            }, void 0, false, {
                                                                fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                                lineNumber: 263,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                        lineNumber: 261,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                                lineNumber: 247,
                                                columnNumber: 15
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                            lineNumber: 246,
                                            columnNumber: 13
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                                    lineNumber: 244,
                                    columnNumber: 11
                                }, this)
                            ]
                        }, void 0, true)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/detail/components/MemberAssignModal.tsx",
                    lineNumber: 145,
                    columnNumber: 5
                }, this);
            };
            _s(MemberAssignModal, "7bJL8QxrUB07zAQJIhuLmnGERu4=", false, function() {
                return [
                    _antd.Form.useForm
                ];
            });
            _c = MemberAssignModal;
            var _default = MemberAssignModal;
            var _c;
            $RefreshReg$(_c, "MemberAssignModal");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '10529360131703232622';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/.umi/plugin-openapi/openapi.tsx": [
            "vendors",
            "src/.umi/plugin-openapi/openapi.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/friend/index.tsx": [
            "p__friend__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "common",
            "p__subscription__index"
        ],
        "src/pages/team/create/index.tsx": [
            "p__team__create__index"
        ],
        "src/pages/team/detail/index.tsx": [
            "common",
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "common",
            "p__user__index"
        ],
        "src/pages/user/login/index.tsx": [
            "p__user__login__index"
        ],
        "src/pages/user/team-select/index.tsx": [
            "src/pages/user/team-select/index.tsx"
        ]
    });
    ;
});

//# sourceMappingURL=p__team__detail__index-async.4099151087552102179.hot-update.js.map