# TeamAuth 前端项目

基于 Ant Design Pro 构建的现代化团队管理系统前端应用。

## 项目概述

TeamAuth 是一个功能完整的团队管理系统，支持双阶段认证、团队协作、用户管理和订阅管理等功能。

### 主要特性

- 🔐 **双阶段认证** - 账号登录 + 团队选择的安全认证机制
- 👥 **团队管理** - 创建团队、邀请成员、权限管理
- 👤 **用户管理** - 个人资料、偏好设置、密码管理
- 💎 **订阅管理** - 多种订阅套餐、使用量监控、续费升级
- 🎨 **现代化UI** - 基于 Ant Design Pro 的响应式设计
- 🌐 **国际化** - 支持中英文切换
- 🛡️ **错误处理** - 统一的错误处理和用户友好的错误提示
- ⚡ **性能优化** - 代码分割、懒加载、缓存策略

## 技术栈

- **框架**: React 18 + TypeScript
- **UI库**: Ant Design + Ant Design Pro Components
- **路由**: UmiJS 4
- **状态管理**: UmiJS 内置状态管理
- **网络请求**: umi-request (基于 axios)
- **构建工具**: UmiJS + Webpack
- **代码规范**: ESLint + Prettier

## 核心功能

### 1. 双阶段认证系统

- **第一阶段**: 用户账号登录，获取 Account Token
- **第二阶段**: 选择团队，获取 Team Token
- **Token 管理**: 自动刷新、安全存储、权限验证

### 2. 团队管理

- **团队创建**: 支持团队名称、描述设置
- **成员管理**: 邀请成员、移除成员、权限控制
- **团队切换**: 支持多团队切换，保持上下文

### 3. 用户管理

- **个人资料**: 用户名、邮箱、头像管理
- **安全设置**: 密码修改、登录历史
- **偏好设置**: 语言、时区、主题、通知设置

### 4. 订阅管理

- **套餐展示**: 多种订阅套餐对比
- **使用监控**: 存储使用量、剩余天数
- **订阅操作**: 购买、续费、升级、取消

## 开发指南

### 环境要求

- Node.js >= 16
- npm >= 8 或 yarn >= 1.22

### 安装依赖

```bash
npm install
# 或
yarn install
```

### 开发运行

```bash
npm start
# 或
yarn start
```

### 构建部署

```bash
npm run build
# 或
yarn build
```

### 代码检查

```bash
npm run lint
# 或
yarn lint
```

## API 集成

### 请求配置

- **Base URL**: `/api/v1`
- **认证方式**: Bearer Token
- **错误处理**: 统一错误拦截和用户提示
- **加载状态**: 全局加载状态管理

### 服务层设计

每个功能模块都有对应的服务类：

- `AuthService`: 认证相关操作
- `TeamService`: 团队管理操作
- `UserService`: 用户管理操作
- `SubscriptionService`: 订阅管理操作

## 浏览器支持

- Chrome >= 80
- Firefox >= 78
- Safari >= 13
- Edge >= 80

## 许可证

MIT License
