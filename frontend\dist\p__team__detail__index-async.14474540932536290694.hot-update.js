globalThis.makoModuleHotUpdate('p__team__detail__index', {
    modules: {
        "src/pages/team/detail/index.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
            var _services = __mako_require__("src/services/index.ts");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _TeamMemberList = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team/detail/components/TeamMemberList.tsx"));
            var _EnhancedTeamDetail = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team/detail/components/EnhancedTeamDetail.tsx"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Title, Text } = _antd.Typography;
            const { TextArea } = _antd.Input;
            const TeamDetailPage = ()=>{
                _s();
                const [loading, setLoading] = (0, _react.useState)(true);
                const [teamDetail, setTeamDetail] = (0, _react.useState)(null);
                const [useEnhancedUI, setUseEnhancedUI] = (0, _react.useState)(true); // 控制是否使用新UI
                const { setInitialState } = (0, _max.useModel)('@@initialState');
                (0, _react.useEffect)(()=>{
                    fetchTeamDetail();
                }, []);
                const fetchTeamDetail = async ()=>{
                    try {
                        setLoading(true);
                        const detail = await _services.TeamService.getCurrentTeamDetail();
                        setTeamDetail(detail);
                    } catch (error) {
                        console.error('获取团队详情失败:', error);
                        _antd.message.error('获取团队详情失败');
                    } finally{
                        setLoading(false);
                    }
                };
                // 经典版UI的处理方法
                const [editModalVisible, setEditModalVisible] = (0, _react.useState)(false);
                const [updating, setUpdating] = (0, _react.useState)(false);
                const [deleting, setDeleting] = (0, _react.useState)(false);
                const [form] = _antd.Form.useForm();
                const handleEdit = ()=>{
                    if (teamDetail) {
                        form.setFieldsValue({
                            name: teamDetail.name,
                            description: teamDetail.description
                        });
                        setEditModalVisible(true);
                    }
                };
                const handleUpdate = async (values)=>{
                    try {
                        setUpdating(true);
                        const updatedTeam = await _services.TeamService.updateCurrentTeam(values);
                        setTeamDetail(updatedTeam);
                        setEditModalVisible(false);
                        _antd.message.success('团队信息更新成功');
                    } catch (error) {
                        console.error('更新团队信息失败:', error);
                    } finally{
                        setUpdating(false);
                    }
                };
                const handleDeleteTeam = ()=>{
                    _antd.Modal.confirm({
                        title: '确认删除团队',
                        content: '删除团队后，所有团队数据将无法恢复。确定要删除吗？',
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {}, void 0, false, {
                            fileName: "src/pages/team/detail/index.tsx",
                            lineNumber: 93,
                            columnNumber: 13
                        }, this),
                        okText: '确认删除',
                        cancelText: '取消',
                        okType: 'danger',
                        onOk: async ()=>{
                            try {
                                setDeleting(true);
                                await _services.TeamService.deleteTeam(teamDetail.id);
                                _antd.message.success('团队删除成功');
                                // 更新全局状态，清除当前团队
                                setInitialState((s)=>({
                                        ...s,
                                        currentTeam: undefined
                                    }));
                                _max.history.push('/user/team-select');
                            } catch (error) {
                                console.error('删除团队失败:', error);
                                _antd.message.error('删除团队失败');
                            } finally{
                                setDeleting(false);
                            }
                        }
                    });
                };
                const handleGoBack = ()=>{
                    _max.history.push('/user/team-select');
                };
                if (loading) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            textAlign: 'center',
                            padding: '50px 0'
                        },
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                            size: "large"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/index.tsx",
                            lineNumber: 123,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/index.tsx",
                        lineNumber: 122,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team/detail/index.tsx",
                    lineNumber: 121,
                    columnNumber: 7
                }, this);
                if (!teamDetail) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            textAlign: 'center',
                            padding: '50px 0'
                        },
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            type: "secondary",
                            children: "团队信息加载失败"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/index.tsx",
                            lineNumber: 133,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/index.tsx",
                        lineNumber: 132,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team/detail/index.tsx",
                    lineNumber: 131,
                    columnNumber: 7
                }, this);
                // 使用新的增强版UI
                if (useEnhancedUI) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
                    title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        align: "center",
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "text",
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ArrowLeftOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team/detail/index.tsx",
                                    lineNumber: 147,
                                    columnNumber: 21
                                }, void 0),
                                onClick: handleGoBack,
                                style: {
                                    color: '#666'
                                },
                                children: "返回团队选择"
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/index.tsx",
                                lineNumber: 145,
                                columnNumber: 13
                            }, void 0),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {
                                type: "vertical"
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/index.tsx",
                                lineNumber: 153,
                                columnNumber: 13
                            }, void 0),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                style: {
                                    color: '#1890ff'
                                }
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/index.tsx",
                                lineNumber: 154,
                                columnNumber: 13
                            }, void 0),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Title, {
                                level: 4,
                                style: {
                                    margin: 0,
                                    color: '#333'
                                },
                                children: (teamDetail === null || teamDetail === void 0 ? void 0 : teamDetail.name) || '团队详情'
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/index.tsx",
                                lineNumber: 155,
                                columnNumber: 13
                            }, void 0),
                            (teamDetail === null || teamDetail === void 0 ? void 0 : teamDetail.isCreator) && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                type: "secondary",
                                style: {
                                    fontSize: 14
                                },
                                children: "(管理员)"
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/index.tsx",
                                lineNumber: 159,
                                columnNumber: 15
                            }, void 0)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/team/detail/index.tsx",
                        lineNumber: 144,
                        columnNumber: 11
                    }, void 0),
                    extra: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "text",
                            size: "small",
                            onClick: ()=>setUseEnhancedUI(false),
                            style: {
                                color: '#666'
                            },
                            children: "切换到经典版"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/index.tsx",
                            lineNumber: 167,
                            columnNumber: 13
                        }, void 0)
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/index.tsx",
                        lineNumber: 166,
                        columnNumber: 11
                    }, void 0),
                    style: {
                        background: '#f5f5f5'
                    },
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_EnhancedTeamDetail.default, {
                        teamDetail: teamDetail,
                        onRefresh: fetchTeamDetail
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/index.tsx",
                        lineNumber: 179,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team/detail/index.tsx",
                    lineNumber: 142,
                    columnNumber: 7
                }, this);
                // 原有的经典UI
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
                    title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        align: "center",
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "text",
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ArrowLeftOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team/detail/index.tsx",
                                    lineNumber: 194,
                                    columnNumber: 19
                                }, void 0),
                                onClick: handleGoBack,
                                style: {
                                    color: '#666'
                                },
                                children: "返回"
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/index.tsx",
                                lineNumber: 192,
                                columnNumber: 11
                            }, void 0),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {
                                type: "vertical"
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/index.tsx",
                                lineNumber: 200,
                                columnNumber: 11
                            }, void 0),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                style: {
                                    color: '#1890ff'
                                }
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/index.tsx",
                                lineNumber: 201,
                                columnNumber: 11
                            }, void 0),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Title, {
                                level: 4,
                                style: {
                                    margin: 0,
                                    color: '#333'
                                },
                                children: teamDetail.name
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/index.tsx",
                                lineNumber: 202,
                                columnNumber: 11
                            }, void 0),
                            teamDetail.isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                type: "secondary",
                                style: {
                                    fontSize: 14
                                },
                                children: "(管理员)"
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/index.tsx",
                                lineNumber: 206,
                                columnNumber: 13
                            }, void 0)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/team/detail/index.tsx",
                        lineNumber: 191,
                        columnNumber: 9
                    }, void 0),
                    extra: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "text",
                                size: "small",
                                onClick: ()=>setUseEnhancedUI(true),
                                style: {
                                    color: '#1890ff'
                                },
                                children: "切换到增强版"
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/index.tsx",
                                lineNumber: 214,
                                columnNumber: 11
                            }, void 0),
                            teamDetail.isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                            fileName: "src/pages/team/detail/index.tsx",
                                            lineNumber: 225,
                                            columnNumber: 23
                                        }, void 0),
                                        onClick: handleEdit,
                                        type: "primary",
                                        children: "编辑团队"
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/index.tsx",
                                        lineNumber: 224,
                                        columnNumber: 15
                                    }, void 0),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                            fileName: "src/pages/team/detail/index.tsx",
                                            lineNumber: 232,
                                            columnNumber: 23
                                        }, void 0),
                                        onClick: handleDeleteTeam,
                                        danger: true,
                                        loading: deleting,
                                        children: "删除团队"
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/index.tsx",
                                        lineNumber: 231,
                                        columnNumber: 15
                                    }, void 0)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/detail/index.tsx",
                                lineNumber: 223,
                                columnNumber: 13
                            }, void 0)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/team/detail/index.tsx",
                        lineNumber: 213,
                        columnNumber: 9
                    }, void 0),
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            title: "团队信息",
                            style: {
                                marginBottom: 24
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions, {
                                column: 2,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                        label: "团队名称",
                                        children: teamDetail.name
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/index.tsx",
                                        lineNumber: 246,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                        label: "成员数量",
                                        children: [
                                            teamDetail.memberCount,
                                            " 人"
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/team/detail/index.tsx",
                                        lineNumber: 249,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                        label: "创建时间",
                                        children: new Date(teamDetail.createdAt).toLocaleString()
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/index.tsx",
                                        lineNumber: 252,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                        label: "最后更新",
                                        children: new Date(teamDetail.updatedAt).toLocaleString()
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/index.tsx",
                                        lineNumber: 255,
                                        columnNumber: 11
                                    }, this),
                                    teamDetail.description && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                        label: "团队描述",
                                        span: 2,
                                        children: teamDetail.description
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/index.tsx",
                                        lineNumber: 259,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/detail/index.tsx",
                                lineNumber: 245,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/index.tsx",
                            lineNumber: 244,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamMemberList.default, {
                            teamId: teamDetail.id,
                            isCreator: teamDetail.isCreator,
                            onMemberChange: fetchTeamDetail
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/index.tsx",
                            lineNumber: 266,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                            title: "编辑团队信息",
                            open: editModalVisible,
                            onCancel: ()=>setEditModalVisible(false),
                            footer: null,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                                form: form,
                                layout: "vertical",
                                onFinish: handleUpdate,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        label: "团队名称",
                                        name: "name",
                                        rules: [
                                            {
                                                required: true,
                                                message: '请输入团队名称！'
                                            },
                                            {
                                                max: 100,
                                                message: '团队名称长度不能超过100字符！'
                                            }
                                        ],
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                            placeholder: "请输入团队名称"
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/index.tsx",
                                            lineNumber: 292,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/index.tsx",
                                        lineNumber: 284,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        label: "团队描述",
                                        name: "description",
                                        rules: [
                                            {
                                                max: 500,
                                                message: '团队描述长度不能超过500字符！'
                                            }
                                        ],
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                            placeholder: "请输入团队描述（可选）",
                                            rows: 4,
                                            showCount: true,
                                            maxLength: 500
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/index.tsx",
                                            lineNumber: 302,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/index.tsx",
                                        lineNumber: 295,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    type: "primary",
                                                    htmlType: "submit",
                                                    loading: updating,
                                                    children: "保存"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/index.tsx",
                                                    lineNumber: 312,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    onClick: ()=>setEditModalVisible(false),
                                                    children: "取消"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/index.tsx",
                                                    lineNumber: 319,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team/detail/index.tsx",
                                            lineNumber: 311,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/index.tsx",
                                        lineNumber: 310,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/detail/index.tsx",
                                lineNumber: 279,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/index.tsx",
                            lineNumber: 273,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/detail/index.tsx",
                    lineNumber: 189,
                    columnNumber: 5
                }, this);
            };
            _s(TeamDetailPage, "rxmLrNpsCCk14PzVItCdmNci628=", false, function() {
                return [
                    _max.useModel,
                    _antd.Form.useForm
                ];
            });
            _c = TeamDetailPage;
            var _default = TeamDetailPage;
            var _c;
            $RefreshReg$(_c, "TeamDetailPage");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '4099151087552102179';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/.umi/plugin-openapi/openapi.tsx": [
            "vendors",
            "src/.umi/plugin-openapi/openapi.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/friend/index.tsx": [
            "p__friend__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "common",
            "p__subscription__index"
        ],
        "src/pages/team/create/index.tsx": [
            "p__team__create__index"
        ],
        "src/pages/team/detail/index.tsx": [
            "common",
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "common",
            "p__user__index"
        ],
        "src/pages/user/login/index.tsx": [
            "p__user__login__index"
        ],
        "src/pages/user/team-select/index.tsx": [
            "src/pages/user/team-select/index.tsx"
        ]
    });
    ;
});

//# sourceMappingURL=p__team__detail__index-async.14474540932536290694.hot-update.js.map