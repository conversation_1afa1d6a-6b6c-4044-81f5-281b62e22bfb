/**
 * 用户资料页面
 */

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  Button, 
  Space, 
  Typography, 
  message, 
  Divider,
  Avatar,
  Upload,
  Modal
} from 'antd';
import { 
  UserOutlined, 
  EditOutlined, 
  LockOutlined,
  MailOutlined,
  SaveOutlined,
  UploadOutlined
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { UserService } from '@/services';
import type { UserProfileResponse, UpdateUserProfileRequest } from '@/types/api';

const { Title, Text } = Typography;

const UserProfilePage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [userProfile, setUserProfile] = useState<UserProfileResponse | null>(null);
  const [editMode, setEditMode] = useState(false);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [passwordForm] = Form.useForm();

  useEffect(() => {
    fetchUserProfile();
  }, []);

  const fetchUserProfile = async () => {
    try {
      setLoading(true);
      const profile = await UserService.getUserProfile();
      setUserProfile(profile);
      form.setFieldsValue({
        name: profile.name,
        email: profile.email,
      });
    } catch (error) {
      console.error('获取用户资料失败:', error);
      message.error('获取用户资料失败');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateProfile = async (values: { name: string }) => {
    try {
      setUpdating(true);
      const request: UpdateUserProfileRequest = {
        name: values.name,
      };
      
      const updatedProfile = await UserService.updateUserProfile(request);
      setUserProfile(updatedProfile);
      setEditMode(false);
      message.success('用户资料更新成功');
    } catch (error) {
      console.error('更新用户资料失败:', error);
    } finally {
      setUpdating(false);
    }
  };

  const handleChangePassword = async (values: { currentPassword: string; newPassword: string }) => {
    try {
      setUpdating(true);
      await UserService.changePassword(values.currentPassword, values.newPassword);
      setPasswordModalVisible(false);
      passwordForm.resetFields();
      message.success('密码修改成功');
    } catch (error) {
      console.error('修改密码失败:', error);
    } finally {
      setUpdating(false);
    }
  };

  const handleCancelEdit = () => {
    setEditMode(false);
    if (userProfile) {
      form.setFieldsValue({
        name: userProfile.name,
        email: userProfile.email,
      });
    }
  };

  if (loading || !userProfile) {
    return (
      <PageContainer>
        <Card loading={loading} />
      </PageContainer>
    );
  }

  return (
    <PageContainer title="个人资料">
      <Card>
        <div style={{ display: 'flex', alignItems: 'flex-start', gap: 24 }}>
          {/* 头像部分 */}
          <div style={{ textAlign: 'center' }}>
            <Avatar size={120} icon={<UserOutlined />} />
            <div style={{ marginTop: 16 }}>
              <Upload
                showUploadList={false}
                beforeUpload={() => {
                  message.info('头像上传功能暂未实现');
                  return false;
                }}
              >
                <Button icon={<UploadOutlined />} size="small">
                  更换头像
                </Button>
              </Upload>
            </div>
          </div>

          {/* 资料表单 */}
          <div style={{ flex: 1 }}>
            <Form
              form={form}
              layout="vertical"
              onFinish={handleUpdateProfile}
              disabled={!editMode}
            >
              <Form.Item
                label="用户名"
                name="name"
                rules={[
                  { required: true, message: '请输入用户名！' },
                  { max: 100, message: '用户名长度不能超过100字符！' },
                ]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="请输入用户名"
                  disabled={!editMode}
                />
              </Form.Item>

              <Form.Item
                label="邮箱"
                name="email"
              >
                <Input
                  prefix={<MailOutlined />}
                  disabled
                  placeholder="邮箱地址"
                />
              </Form.Item>

              <Form.Item label="注册时间">
                <Input
                  value={new Date(userProfile.createdAt).toLocaleString()}
                  disabled
                />
              </Form.Item>

              <Form.Item label="最后更新">
                <Input
                  value={new Date(userProfile.updatedAt).toLocaleString()}
                  disabled
                />
              </Form.Item>

              <Form.Item>
                <Space>
                  {editMode ? (
                    <>
                      <Button
                        type="primary"
                        htmlType="submit"
                        loading={updating}
                        icon={<SaveOutlined />}
                      >
                        保存
                      </Button>
                      <Button onClick={handleCancelEdit}>
                        取消
                      </Button>
                    </>
                  ) : (
                    <Button
                      type="primary"
                      icon={<EditOutlined />}
                      onClick={() => setEditMode(true)}
                    >
                      编辑资料
                    </Button>
                  )}
                </Space>
              </Form.Item>
            </Form>

            <Divider />

            {/* 安全设置 */}
            <div>
              <Title level={4}>安全设置</Title>
              <Space direction="vertical" style={{ width: '100%' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <div>
                    <Text strong>登录密码</Text>
                    <br />
                    <Text type="secondary">定期更换密码可以提高账户安全性</Text>
                  </div>
                  <Button
                    icon={<LockOutlined />}
                    onClick={() => setPasswordModalVisible(true)}
                  >
                    修改密码
                  </Button>
                </div>
              </Space>
            </div>
          </div>
        </div>
      </Card>

      {/* 修改密码模态框 */}
      <Modal
        title="修改密码"
        open={passwordModalVisible}
        onCancel={() => {
          setPasswordModalVisible(false);
          passwordForm.resetFields();
        }}
        footer={null}
      >
        <Form
          form={passwordForm}
          layout="vertical"
          onFinish={handleChangePassword}
        >
          <Form.Item
            label="当前密码"
            name="currentPassword"
            rules={[
              { required: true, message: '请输入当前密码！' },
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请输入当前密码"
            />
          </Form.Item>

          <Form.Item
            label="新密码"
            name="newPassword"
            rules={[
              { required: true, message: '请输入新密码！' },
              { min: 8, message: '密码长度至少8位！' },
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请输入新密码（至少8位）"
            />
          </Form.Item>

          <Form.Item
            label="确认新密码"
            name="confirmPassword"
            dependencies={['newPassword']}
            rules={[
              { required: true, message: '请确认新密码！' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('newPassword') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致！'));
                },
              }),
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请再次输入新密码"
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={updating}
              >
                确认修改
              </Button>
              <Button
                onClick={() => {
                  setPasswordModalVisible(false);
                  passwordForm.resetFields();
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </PageContainer>
  );
};

export default UserProfilePage;
