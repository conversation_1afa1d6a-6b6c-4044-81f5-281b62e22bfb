{"version": 3, "sources": ["src/pages/team/create/index.tsx"], "sourcesContent": ["/**\n * 创建团队页面\n */\n\nimport React, { useState } from 'react';\nimport { Card, Form, Input, Button, Typography, Space, message } from 'antd';\nimport { TeamOutlined, ArrowLeftOutlined } from '@ant-design/icons';\nimport { history, useModel } from '@umijs/max';\nimport { createStyles } from 'antd-style';\nimport { TeamService, AuthService } from '@/services';\nimport type { CreateTeamRequest } from '@/types/api';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\n\nconst useStyles = createStyles(({ token }) => {\n  return {\n    container: {\n      display: 'flex',\n      flexDirection: 'column',\n      height: '100vh',\n      overflow: 'auto',\n      backgroundColor: token.colorBgLayout,\n    },\n    content: {\n      flex: 1,\n      display: 'flex',\n      flexDirection: 'column',\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: '32px 16px',\n    },\n    header: {\n      marginBottom: 40,\n      textAlign: 'center',\n    },\n    formCard: {\n      width: '100%',\n      maxWidth: 500,\n      marginBottom: 24,\n    },\n    actions: {\n      marginTop: 24,\n      display: 'flex',\n      gap: 16,\n      justifyContent: 'center',\n    },\n    backButton: {\n      marginBottom: 24,\n    },\n  };\n});\n\nconst CreateTeamPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [form] = Form.useForm();\n  const { styles } = useStyles();\n  const { initialState, setInitialState } = useModel('@@initialState');\n\n  const handleSubmit = async (values: CreateTeamRequest) => {\n    setLoading(true);\n    try {\n      const team = await TeamService.createTeam(values);\n      message.success('团队创建成功！请在团队列表中选择进入新创建的团队。');\n\n      // 不自动进行团队登录，让用户在团队选择页面手动选择\n      // 直接跳转回团队选择页面\n      history.push('/user/team-select');\n    } catch (error) {\n      console.error('创建团队失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleBack = () => {\n    // 检查是否有 Account Token\n    if (AuthService.isLoggedIn()) {\n      history.push('/user/team-select');\n    } else {\n      history.push('/user/login');\n    }\n  };\n\n  return (\n    <div className={styles.container}>\n      <div className={styles.content}>\n        <Button\n          type=\"text\"\n          icon={<ArrowLeftOutlined />}\n          onClick={handleBack}\n          className={styles.backButton}\n        >\n          返回\n        </Button>\n\n        <div className={styles.header}>\n          <Space direction=\"vertical\" align=\"center\" size=\"large\">\n            <TeamOutlined style={{ fontSize: 48, color: '#1890ff' }} />\n            <div>\n              <Title level={2}>创建团队</Title>\n              <Text type=\"secondary\">创建一个新的团队工作空间</Text>\n            </div>\n          </Space>\n        </div>\n\n        <Card className={styles.formCard}>\n          <Form\n            form={form}\n            layout=\"vertical\"\n            onFinish={handleSubmit}\n            autoComplete=\"off\"\n          >\n            <Form.Item\n              label=\"团队名称\"\n              name=\"name\"\n              rules={[\n                { required: true, message: '请输入团队名称！' },\n                { max: 100, message: '团队名称长度不能超过100字符！' },\n                { min: 2, message: '团队名称至少需要2个字符！' },\n              ]}\n            >\n              <Input\n                placeholder=\"请输入团队名称\"\n                size=\"large\"\n              />\n            </Form.Item>\n\n            <Form.Item\n              label=\"团队描述\"\n              name=\"description\"\n              rules={[\n                { max: 500, message: '团队描述长度不能超过500字符！' },\n              ]}\n            >\n              <TextArea\n                placeholder=\"请输入团队描述（可选）\"\n                rows={4}\n                showCount\n                maxLength={500}\n              />\n            </Form.Item>\n\n            <Form.Item>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                loading={loading}\n                size=\"large\"\n                block\n              >\n                创建团队\n              </Button>\n            </Form.Item>\n          </Form>\n        </Card>\n\n        <div className={styles.actions}>\n          <Space direction=\"vertical\" align=\"center\">\n            <Text type=\"secondary\">\n              创建团队后，您将成为团队的管理员\n            </Text>\n            <Text type=\"secondary\">\n              可以邀请其他成员加入您的团队\n            </Text>\n          </Space>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CreateTeamPage;\n"], "names": [], "mappings": ";;;AAAA;;CAEC;;;;4BA0KD;;;eAAA;;;;;;wEAxKgC;6BACsC;8BACtB;4BACd;kCACL;iCACY;;;;;;;;;;AAGzC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAClC,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAK;AAE1B,MAAM,YAAY,IAAA,uBAAY,EAAC,CAAC,EAAE,KAAK,EAAE;IACvC,OAAO;QACL,WAAW;YACT,SAAS;YACT,eAAe;YACf,QAAQ;YACR,UAAU;YACV,iBAAiB,MAAM,aAAa;QACtC;QACA,SAAS;YACP,MAAM;YACN,SAAS;YACT,eAAe;YACf,gBAAgB;YAChB,YAAY;YACZ,SAAS;QACX;QACA,QAAQ;YACN,cAAc;YACd,WAAW;QACb;QACA,UAAU;YACR,OAAO;YACP,UAAU;YACV,cAAc;QAChB;QACA,SAAS;YACP,WAAW;YACX,SAAS;YACT,KAAK;YACL,gBAAgB;QAClB;QACA,YAAY;YACV,cAAc;QAChB;IACF;AACF;AAEA,MAAM,iBAA2B;;IAC/B,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;IAC3B,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;IAEnD,MAAM,eAAe,OAAO;QAC1B,WAAW;QACX,IAAI;YACW,MAAM,qBAAW,CAAC,UAAU,CAAC;YAC1C,aAAO,CAAC,OAAO,CAAC;YAEhB,2BAA2B;YAC3B,cAAc;YACd,YAAO,CAAC,IAAI,CAAC;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa;QACjB,sBAAsB;QACtB,IAAI,qBAAW,CAAC,UAAU,IACxB,YAAO,CAAC,IAAI,CAAC;aAEb,YAAO,CAAC,IAAI,CAAC;IAEjB;IAEA,qBACE,2BAAC;QAAI,WAAW,OAAO,SAAS;kBAC9B,cAAA,2BAAC;YAAI,WAAW,OAAO,OAAO;;8BAC5B,2BAAC,YAAM;oBACL,MAAK;oBACL,oBAAM,2BAAC,wBAAiB;;;;;oBACxB,SAAS;oBACT,WAAW,OAAO,UAAU;8BAC7B;;;;;;8BAID,2BAAC;oBAAI,WAAW,OAAO,MAAM;8BAC3B,cAAA,2BAAC,WAAK;wBAAC,WAAU;wBAAW,OAAM;wBAAS,MAAK;;0CAC9C,2BAAC,mBAAY;gCAAC,OAAO;oCAAE,UAAU;oCAAI,OAAO;gCAAU;;;;;;0CACtD,2BAAC;;kDACC,2BAAC;wCAAM,OAAO;kDAAG;;;;;;kDACjB,2BAAC;wCAAK,MAAK;kDAAY;;;;;;;;;;;;;;;;;;;;;;;8BAK7B,2BAAC,UAAI;oBAAC,WAAW,OAAO,QAAQ;8BAC9B,cAAA,2BAAC,UAAI;wBACH,MAAM;wBACN,QAAO;wBACP,UAAU;wBACV,cAAa;;0CAEb,2BAAC,UAAI,CAAC,IAAI;gCACR,OAAM;gCACN,MAAK;gCACL,OAAO;oCACL;wCAAE,UAAU;wCAAM,SAAS;oCAAW;oCACtC;wCAAE,KAAK;wCAAK,SAAS;oCAAmB;oCACxC;wCAAE,KAAK;wCAAG,SAAS;oCAAgB;iCACpC;0CAED,cAAA,2BAAC,WAAK;oCACJ,aAAY;oCACZ,MAAK;;;;;;;;;;;0CAIT,2BAAC,UAAI,CAAC,IAAI;gCACR,OAAM;gCACN,MAAK;gCACL,OAAO;oCACL;wCAAE,KAAK;wCAAK,SAAS;oCAAmB;iCACzC;0CAED,cAAA,2BAAC;oCACC,aAAY;oCACZ,MAAM;oCACN,SAAS;oCACT,WAAW;;;;;;;;;;;0CAIf,2BAAC,UAAI,CAAC,IAAI;0CACR,cAAA,2BAAC,YAAM;oCACL,MAAK;oCACL,UAAS;oCACT,SAAS;oCACT,MAAK;oCACL,KAAK;8CACN;;;;;;;;;;;;;;;;;;;;;;8BAOP,2BAAC;oBAAI,WAAW,OAAO,OAAO;8BAC5B,cAAA,2BAAC,WAAK;wBAAC,WAAU;wBAAW,OAAM;;0CAChC,2BAAC;gCAAK,MAAK;0CAAY;;;;;;0CAGvB,2BAAC;gCAAK,MAAK;0CAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnC;GArHM;;QAEW,UAAI,CAAC;QACD;QACuB,aAAQ;;;KAJ9C;IAuHN,WAAe"}