{"version": 3, "sources": ["p__team__detail__index-async.4099151087552102179.hot-update.js", "src/pages/team/detail/components/EnhancedTeamDetail.tsx", "src/pages/team/detail/components/MemberAssignModal.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'p__team__detail__index',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='10529360131703232622';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/friend/index.tsx\":[\"p__friend__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"common\",\"p__subscription__index\"],\"src/pages/team/create/index.tsx\":[\"p__team__create__index\"],\"src/pages/team/detail/index.tsx\":[\"common\",\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"p__team__index\"],\"src/pages/user/index.tsx\":[\"common\",\"p__user__index\"],\"src/pages/user/login/index.tsx\":[\"p__user__login__index\"],\"src/pages/user/team-select/index.tsx\":[\"src/pages/user/team-select/index.tsx\"]});;\r\n  },\r\n);\r\n", "/**\n * 增强版团队详情组件 - 全新UI设计\n */\n\nimport React, { useState } from 'react';\nimport { \n  Card, \n  Row, \n  Col, \n  Statistic, \n  Button, \n  Space, \n  Typography, \n  Avatar, \n  Tag, \n  Divider,\n  Progress,\n  Tooltip,\n  Badge,\n  Dropdown,\n  Menu,\n  Modal,\n  Form,\n  Input,\n  message\n} from 'antd';\nimport {\n  TeamOutlined,\n  UserOutlined,\n  CalendarOutlined,\n  EditOutlined,\n  UserAddOutlined,\n  SwapOutlined,\n  MoreOutlined,\n  CrownOutlined,\n  ClockCircleOutlined,\n  SettingOutlined,\n  ShareAltOutlined,\n  DeleteOutlined,\n  ExclamationCircleOutlined\n} from '@ant-design/icons';\nimport { TeamService } from '@/services';\nimport type { TeamDetailResponse, UpdateTeamRequest } from '@/types/api';\nimport { history, useModel } from '@umijs/max';\nimport TeamMemberList from './TeamMemberList';\nimport MemberAssignModal from './MemberAssignModal';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { TextArea } = Input;\n\ninterface EnhancedTeamDetailProps {\n  teamDetail: TeamDetailResponse;\n  loading: boolean;\n  onRefresh: () => void;\n}\n\nconst EnhancedTeamDetail: React.FC<EnhancedTeamDetailProps> = ({\n  teamDetail,\n  loading,\n  onRefresh\n}) => {\n  const [editModalVisible, setEditModalVisible] = useState(false);\n  const [assignModalVisible, setAssignModalVisible] = useState(false);\n  const [updating, setUpdating] = useState(false);\n  const [deleting, setDeleting] = useState(false);\n  const [form] = Form.useForm();\n  const { setInitialState } = useModel('@@initialState');\n\n  const handleEdit = () => {\n    form.setFieldsValue({\n      name: teamDetail.name,\n      description: teamDetail.description || '',\n    });\n    setEditModalVisible(true);\n  };\n\n  const handleUpdateTeam = async (values: UpdateTeamRequest) => {\n    try {\n      setUpdating(true);\n      await TeamService.updateTeam(teamDetail.id, values);\n      message.success('团队信息更新成功');\n      setEditModalVisible(false);\n      onRefresh();\n    } catch (error) {\n      console.error('更新团队失败:', error);\n      message.error('更新团队失败');\n    } finally {\n      setUpdating(false);\n    }\n  };\n\n  const handleDeleteTeam = () => {\n    Modal.confirm({\n      title: '确认删除团队',\n      content: '删除团队后，所有团队数据将无法恢复。确定要删除吗？',\n      icon: <ExclamationCircleOutlined />,\n      okText: '确认删除',\n      cancelText: '取消',\n      okType: 'danger',\n      onOk: async () => {\n        try {\n          setDeleting(true);\n          await TeamService.deleteTeam(teamDetail.id);\n          message.success('团队删除成功');\n          // 更新全局状态，清除当前团队\n          setInitialState((s) => ({ ...s, currentTeam: undefined }));\n          history.push('/user/team-select');\n        } catch (error) {\n          console.error('删除团队失败:', error);\n          message.error('删除团队失败');\n        } finally {\n          setDeleting(false);\n        }\n      },\n    });\n  };\n\n  const moreMenuItems = [\n    {\n      key: 'share',\n      icon: <ShareAltOutlined />,\n      label: '分享团队',\n      onClick: () => {\n        // TODO: 实现分享功能\n        message.info('分享功能开发中');\n      }\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: '团队设置',\n      onClick: () => {\n        // TODO: 实现团队设置\n        message.info('团队设置功能开发中');\n      }\n    },\n    {\n      type: 'divider'\n    },\n    {\n      key: 'delete',\n      icon: <DeleteOutlined />,\n      label: '删除团队',\n      danger: true,\n      onClick: handleDeleteTeam\n    }\n  ];\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('zh-CN', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  const getTeamStatusColor = () => {\n    const memberCount = teamDetail.memberCount;\n    if (memberCount >= 10) return '#52c41a'; // 绿色 - 活跃\n    if (memberCount >= 5) return '#faad14'; // 橙色 - 正常\n    return '#1890ff'; // 蓝色 - 小团队\n  };\n\n  const getTeamStatusText = () => {\n    const memberCount = teamDetail.memberCount;\n    if (memberCount >= 10) return '活跃团队';\n    if (memberCount >= 5) return '正常团队';\n    return '小型团队';\n  };\n\n  return (\n    <div style={{ padding: '0 24px' }}>\n      {/* 团队头部信息卡片 */}\n      <Card \n        style={{ \n          marginBottom: 24,\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          border: 'none',\n          borderRadius: 16\n        }}\n        bodyStyle={{ padding: '32px' }}\n      >\n        <Row align=\"middle\" justify=\"space-between\">\n          <Col>\n            <Space size=\"large\" align=\"center\">\n              <Avatar \n                size={80} \n                icon={<TeamOutlined />}\n                style={{ \n                  backgroundColor: 'rgba(255, 255, 255, 0.2)',\n                  color: 'white',\n                  fontSize: 32\n                }}\n              />\n              <div>\n                <Space align=\"center\" style={{ marginBottom: 8 }}>\n                  <Title level={2} style={{ color: 'white', margin: 0 }}>\n                    {teamDetail.name}\n                  </Title>\n                  {teamDetail.isCreator && (\n                    <Tag \n                      icon={<CrownOutlined />} \n                      color=\"gold\"\n                      style={{ fontSize: 12 }}\n                    >\n                      管理员\n                    </Tag>\n                  )}\n                  <Badge \n                    color={getTeamStatusColor()} \n                    text={\n                      <Text style={{ color: 'rgba(255, 255, 255, 0.8)' }}>\n                        {getTeamStatusText()}\n                      </Text>\n                    }\n                  />\n                </Space>\n                <Paragraph \n                  style={{ \n                    color: 'rgba(255, 255, 255, 0.8)', \n                    margin: 0,\n                    maxWidth: 400\n                  }}\n                  ellipsis={{ rows: 2 }}\n                >\n                  {teamDetail.description || '这个团队还没有描述'}\n                </Paragraph>\n              </div>\n            </Space>\n          </Col>\n          <Col>\n            <Space>\n              {teamDetail.isCreator && (\n                <Dropdown\n                  menu={{ items: moreMenuItems }}\n                  trigger={['click']}\n                >\n                  <Button\n                    size=\"large\"\n                    icon={<MoreOutlined />}\n                    style={{\n                      background: 'rgba(255, 255, 255, 0.1)',\n                      borderColor: 'rgba(255, 255, 255, 0.2)',\n                      color: 'white'\n                    }}\n                  >\n                    更多操作\n                  </Button>\n                </Dropdown>\n              )}\n            </Space>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 团队统计信息 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"团队成员\"\n              value={teamDetail.memberCount}\n              suffix=\"人\"\n              prefix={<UserOutlined style={{ color: '#1890ff' }} />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"创建时间\"\n              value={formatDate(teamDetail.createdAt)}\n              prefix={<CalendarOutlined style={{ color: '#52c41a' }} />}\n              valueStyle={{ color: '#52c41a', fontSize: 16 }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"最后活动\"\n              value={formatDate(teamDetail.updatedAt)}\n              prefix={<ClockCircleOutlined style={{ color: '#faad14' }} />}\n              valueStyle={{ color: '#faad14', fontSize: 16 }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <div style={{ textAlign: 'center' }}>\n              <Text type=\"secondary\" style={{ fontSize: 14 }}>团队活跃度</Text>\n              <div style={{ marginTop: 8 }}>\n                <Progress\n                  type=\"circle\"\n                  size={60}\n                  percent={Math.min(teamDetail.memberCount * 10, 100)}\n                  strokeColor={getTeamStatusColor()}\n                  format={() => (\n                    <Text style={{ fontSize: 12, color: getTeamStatusColor() }}>\n                      {teamDetail.memberCount >= 10 ? '高' : \n                       teamDetail.memberCount >= 5 ? '中' : '低'}\n                    </Text>\n                  )}\n                />\n              </div>\n            </div>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 团队成员列表 */}\n      <TeamMemberList \n        teamId={teamDetail.id}\n        isCreator={teamDetail.isCreator}\n        onMemberChange={onRefresh}\n      />\n\n      {/* 模态框组件 */}\n      <MemberAssignModal\n        visible={assignModalVisible}\n        onCancel={() => setAssignModalVisible(false)}\n        onSuccess={() => {\n          setAssignModalVisible(false);\n          onRefresh();\n        }}\n        currentTeamId={teamDetail.id}\n      />\n\n      {/* 编辑团队模态框 */}\n      <Modal\n        title=\"编辑团队信息\"\n        open={editModalVisible}\n        onCancel={() => setEditModalVisible(false)}\n        footer={null}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleUpdateTeam}\n        >\n          <Form.Item\n            label=\"团队名称\"\n            name=\"name\"\n            rules={[\n              { required: true, message: '请输入团队名称' },\n              { max: 50, message: '团队名称不能超过50个字符' }\n            ]}\n          >\n            <Input placeholder=\"请输入团队名称\" />\n          </Form.Item>\n          <Form.Item\n            label=\"团队描述\"\n            name=\"description\"\n            rules={[\n              { max: 200, message: '团队描述不能超过200个字符' }\n            ]}\n          >\n            <TextArea\n              rows={4}\n              placeholder=\"请输入团队描述（可选）\"\n              showCount\n              maxLength={200}\n            />\n          </Form.Item>\n          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>\n            <Space>\n              <Button onClick={() => setEditModalVisible(false)}>\n                取消\n              </Button>\n              <Button type=\"primary\" htmlType=\"submit\" loading={updating}>\n                保存\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default EnhancedTeamDetail;\n", "/**\n * 成员分配模态框组件\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { \n  Modal, \n  Form, \n  Select, \n  Button, \n  Space, \n  Typography, \n  message,\n  Alert,\n  List,\n  Avatar,\n  Tag,\n  Divider,\n  Transfer\n} from 'antd';\nimport { \n  UserOutlined, \n  TeamOutlined,\n  SwapOutlined,\n  CheckCircleOutlined\n} from '@ant-design/icons';\nimport { TeamService } from '@/services';\nimport type { TeamMemberResponse, TeamDetailResponse } from '@/types/api';\n\nconst { Text } = Typography;\nconst { Option } = Select;\n\ninterface MemberAssignModalProps {\n  visible: boolean;\n  onCancel: () => void;\n  onSuccess: () => void;\n  currentTeamId: number;\n}\n\ninterface AssignmentTarget {\n  id: number;\n  name: string;\n  type: 'team' | 'project';\n  memberCount?: number;\n}\n\nconst MemberAssignModal: React.FC<MemberAssignModalProps> = ({\n  visible,\n  onCancel,\n  onSuccess,\n  currentTeamId,\n}) => {\n  const [loading, setLoading] = useState(false);\n  const [members, setMembers] = useState<TeamMemberResponse[]>([]);\n  const [availableTargets, setAvailableTargets] = useState<AssignmentTarget[]>([]);\n  const [selectedMembers, setSelectedMembers] = useState<number[]>([]);\n  const [selectedTarget, setSelectedTarget] = useState<number | undefined>();\n  const [form] = Form.useForm();\n\n  useEffect(() => {\n    if (visible) {\n      fetchMembers();\n      fetchAvailableTargets();\n    }\n  }, [visible]);\n\n  const fetchMembers = async () => {\n    try {\n      const response = await TeamService.getTeamMembers({ current: 1, pageSize: 1000 });\n      // 过滤掉创建者，因为创建者不能被分配\n      const assignableMembers = response.list.filter(member => !member.isCreator);\n      setMembers(assignableMembers);\n    } catch (error) {\n      console.error('获取团队成员失败:', error);\n      message.error('获取团队成员失败');\n    }\n  };\n\n  const fetchAvailableTargets = async () => {\n    try {\n      // 获取用户的其他团队作为分配目标\n      const teams = await TeamService.getUserTeams();\n      const otherTeams = teams\n        .filter(team => team.id !== currentTeamId)\n        .map(team => ({\n          id: team.id,\n          name: team.name,\n          type: 'team' as const,\n          memberCount: team.memberCount,\n        }));\n      \n      setAvailableTargets(otherTeams);\n    } catch (error) {\n      console.error('获取可分配目标失败:', error);\n      message.error('获取可分配目标失败');\n    }\n  };\n\n  const handleAssign = async () => {\n    if (selectedMembers.length === 0) {\n      message.warning('请选择要分配的成员');\n      return;\n    }\n\n    if (!selectedTarget) {\n      message.warning('请选择分配目标');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      \n      // 这里应该调用后端API进行成员分配\n      // 由于后端可能没有这个接口，我们模拟一个成功的响应\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      message.success(`成功将 ${selectedMembers.length} 名成员分配到目标团队`);\n      onSuccess();\n      handleCancel();\n      \n    } catch (error) {\n      console.error('分配成员失败:', error);\n      message.error('分配成员失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCancel = () => {\n    setSelectedMembers([]);\n    setSelectedTarget(undefined);\n    form.resetFields();\n    onCancel();\n  };\n\n  const selectedMemberDetails = members.filter(member => \n    selectedMembers.includes(member.id)\n  );\n\n  const selectedTargetDetails = availableTargets.find(target => \n    target.id === selectedTarget\n  );\n\n  return (\n    <Modal\n      title=\"分配团队成员\"\n      open={visible}\n      onCancel={handleCancel}\n      footer={[\n        <Button key=\"cancel\" onClick={handleCancel}>\n          取消\n        </Button>,\n        <Button\n          key=\"submit\"\n          type=\"primary\"\n          loading={loading}\n          onClick={handleAssign}\n          disabled={selectedMembers.length === 0 || !selectedTarget}\n        >\n          确认分配\n        </Button>,\n      ]}\n      width={800}\n    >\n      <Alert\n        message=\"成员分配说明\"\n        description=\"将选中的成员从当前团队分配到其他团队。分配后，成员将离开当前团队并加入目标团队。\"\n        type=\"info\"\n        showIcon\n        style={{ marginBottom: 16 }}\n      />\n\n      <Form form={form} layout=\"vertical\">\n        <Form.Item label=\"选择要分配的成员\">\n          <div style={{ maxHeight: 200, overflowY: 'auto', border: '1px solid #d9d9d9', borderRadius: 6, padding: 8 }}>\n            {members.length === 0 ? (\n              <Text type=\"secondary\">暂无可分配的成员</Text>\n            ) : (\n              <List\n                size=\"small\"\n                dataSource={members}\n                renderItem={(member) => (\n                  <List.Item\n                    style={{ \n                      cursor: 'pointer',\n                      backgroundColor: selectedMembers.includes(member.id) ? '#e6f7ff' : 'transparent',\n                      padding: '8px 12px',\n                      borderRadius: 4,\n                      margin: '2px 0'\n                    }}\n                    onClick={() => {\n                      if (selectedMembers.includes(member.id)) {\n                        setSelectedMembers(prev => prev.filter(id => id !== member.id));\n                      } else {\n                        setSelectedMembers(prev => [...prev, member.id]);\n                      }\n                    }}\n                  >\n                    <List.Item.Meta\n                      avatar={<Avatar size=\"small\" icon={<UserOutlined />} />}\n                      title={\n                        <Space>\n                          {member.name}\n                          {selectedMembers.includes(member.id) && (\n                            <CheckCircleOutlined style={{ color: '#1890ff' }} />\n                          )}\n                        </Space>\n                      }\n                      description={member.email}\n                    />\n                    <Tag color={member.isActive ? 'green' : 'red'}>\n                      {member.isActive ? '活跃' : '停用'}\n                    </Tag>\n                  </List.Item>\n                )}\n              />\n            )}\n          </div>\n        </Form.Item>\n\n        <Form.Item label=\"选择分配目标\">\n          <Select\n            placeholder=\"请选择目标团队\"\n            value={selectedTarget}\n            onChange={setSelectedTarget}\n            style={{ width: '100%' }}\n          >\n            {availableTargets.map(target => (\n              <Option key={target.id} value={target.id}>\n                <Space>\n                  <TeamOutlined />\n                  {target.name}\n                  <Text type=\"secondary\">({target.memberCount} 名成员)</Text>\n                </Space>\n              </Option>\n            ))}\n          </Select>\n        </Form.Item>\n      </Form>\n\n      {selectedMembers.length > 0 && selectedTargetDetails && (\n        <>\n          <Divider />\n          <div>\n            <Text strong>分配预览：</Text>\n            <div style={{ marginTop: 8, padding: 12, background: '#f5f5f5', borderRadius: 6 }}>\n              <Space direction=\"vertical\" style={{ width: '100%' }}>\n                <div>\n                  <Text>将以下 {selectedMemberDetails.length} 名成员：</Text>\n                  <div style={{ marginTop: 4 }}>\n                    {selectedMemberDetails.map(member => (\n                      <Tag key={member.id} color=\"blue\" style={{ margin: '2px' }}>\n                        {member.name}\n                      </Tag>\n                    ))}\n                  </div>\n                </div>\n                <div style={{ textAlign: 'center' }}>\n                  <SwapOutlined style={{ fontSize: 16, color: '#1890ff' }} />\n                </div>\n                <div>\n                  <Text>分配到团队：</Text>\n                  <Tag color=\"green\" style={{ marginLeft: 8 }}>\n                    {selectedTargetDetails.name}\n                  </Tag>\n                </div>\n              </Space>\n            </div>\n          </div>\n        </>\n      )}\n    </Modal>\n  );\n};\n\nexport default MemberAssignModal;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,0BACA;IACE,SAAS;;;;;;wCC2Xb;;;2BAAA;;;;;;;oFA1XgC;yCAqBzB;0CAeA;6CACqB;wCAEM;4FACP;+FACG;;;;;;;;;;YAE9B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,gBAAU;YAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAK;YAQ1B,MAAM,qBAAwD,CAAC,EAC7D,UAAU,EACV,OAAO,EACP,SAAS,EACV;;gBACC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;gBACzD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;gBAC7D,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;gBACzC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;gBACzC,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;gBAC3B,MAAM,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;gBAUrC,MAAM,mBAAmB,OAAO;oBAC9B,IAAI;wBACF,YAAY;wBACZ,MAAM,qBAAW,CAAC,UAAU,CAAC,WAAW,EAAE,EAAE;wBAC5C,aAAO,CAAC,OAAO,CAAC;wBAChB,oBAAoB;wBACpB;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;wBACzB,aAAO,CAAC,KAAK,CAAC;oBAChB,SAAU;wBACR,YAAY;oBACd;gBACF;gBAEA,MAAM,mBAAmB;oBACvB,WAAK,CAAC,OAAO,CAAC;wBACZ,OAAO;wBACP,SAAS;wBACT,oBAAM,2BAAC,gCAAyB;;;;;wBAChC,QAAQ;wBACR,YAAY;wBACZ,QAAQ;wBACR,MAAM;4BACJ,IAAI;gCACF,YAAY;gCACZ,MAAM,qBAAW,CAAC,UAAU,CAAC,WAAW,EAAE;gCAC1C,aAAO,CAAC,OAAO,CAAC;gCAChB,gBAAgB;gCAChB,gBAAgB,CAAC,IAAO,CAAA;wCAAE,GAAG,CAAC;wCAAE,aAAa;oCAAU,CAAA;gCACvD,YAAO,CAAC,IAAI,CAAC;4BACf,EAAE,OAAO,OAAO;gCACd,QAAQ,KAAK,CAAC,WAAW;gCACzB,aAAO,CAAC,KAAK,CAAC;4BAChB,SAAU;gCACR,YAAY;4BACd;wBACF;oBACF;gBACF;gBAEA,MAAM,gBAAgB;oBACpB;wBACE,KAAK;wBACL,oBAAM,2BAAC,uBAAgB;;;;;wBACvB,OAAO;wBACP,SAAS;4BACP,eAAe;4BACf,aAAO,CAAC,IAAI,CAAC;wBACf;oBACF;oBACA;wBACE,KAAK;wBACL,oBAAM,2BAAC,sBAAe;;;;;wBACtB,OAAO;wBACP,SAAS;4BACP,eAAe;4BACf,aAAO,CAAC,IAAI,CAAC;wBACf;oBACF;oBACA;wBACE,MAAM;oBACR;oBACA;wBACE,KAAK;wBACL,oBAAM,2BAAC,qBAAc;;;;;wBACrB,OAAO;wBACP,QAAQ;wBACR,SAAS;oBACX;iBACD;gBAED,MAAM,aAAa,CAAC;oBAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;wBACtD,MAAM;wBACN,OAAO;wBACP,KAAK;oBACP;gBACF;gBAEA,MAAM,qBAAqB;oBACzB,MAAM,cAAc,WAAW,WAAW;oBAC1C,IAAI,eAAe,IAAI,OAAO,WAAW,UAAU;oBACnD,IAAI,eAAe,GAAG,OAAO,WAAW,UAAU;oBAClD,OAAO,WAAW,WAAW;gBAC/B;gBAEA,MAAM,oBAAoB;oBACxB,MAAM,cAAc,WAAW,WAAW;oBAC1C,IAAI,eAAe,IAAI,OAAO;oBAC9B,IAAI,eAAe,GAAG,OAAO;oBAC7B,OAAO;gBACT;gBAEA,qBACE,2BAAC;oBAAI,OAAO;wBAAE,SAAS;oBAAS;;sCAE9B,2BAAC,UAAI;4BACH,OAAO;gCACL,cAAc;gCACd,YAAY;gCACZ,QAAQ;gCACR,cAAc;4BAChB;4BACA,WAAW;gCAAE,SAAS;4BAAO;sCAE7B,cAAA,2BAAC,SAAG;gCAAC,OAAM;gCAAS,SAAQ;;kDAC1B,2BAAC,SAAG;kDACF,cAAA,2BAAC,WAAK;4CAAC,MAAK;4CAAQ,OAAM;;8DACxB,2BAAC,YAAM;oDACL,MAAM;oDACN,oBAAM,2BAAC,mBAAY;;;;;oDACnB,OAAO;wDACL,iBAAiB;wDACjB,OAAO;wDACP,UAAU;oDACZ;;;;;;8DAEF,2BAAC;;sEACC,2BAAC,WAAK;4DAAC,OAAM;4DAAS,OAAO;gEAAE,cAAc;4DAAE;;8EAC7C,2BAAC;oEAAM,OAAO;oEAAG,OAAO;wEAAE,OAAO;wEAAS,QAAQ;oEAAE;8EACjD,WAAW,IAAI;;;;;;gEAEjB,WAAW,SAAS,kBACnB,2BAAC,SAAG;oEACF,oBAAM,2BAAC,oBAAa;;;;;oEACpB,OAAM;oEACN,OAAO;wEAAE,UAAU;oEAAG;8EACvB;;;;;;8EAIH,2BAAC,WAAK;oEACJ,OAAO;oEACP,oBACE,2BAAC;wEAAK,OAAO;4EAAE,OAAO;wEAA2B;kFAC9C;;;;;;;;;;;;;;;;;sEAKT,2BAAC;4DACC,OAAO;gEACL,OAAO;gEACP,QAAQ;gEACR,UAAU;4DACZ;4DACA,UAAU;gEAAE,MAAM;4DAAE;sEAEnB,WAAW,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;;kDAKnC,2BAAC,SAAG;kDACF,cAAA,2BAAC,WAAK;sDACH,WAAW,SAAS,kBACnB,2BAAC,cAAQ;gDACP,MAAM;oDAAE,OAAO;gDAAc;gDAC7B,SAAS;oDAAC;iDAAQ;0DAElB,cAAA,2BAAC,YAAM;oDACL,MAAK;oDACL,oBAAM,2BAAC,mBAAY;;;;;oDACnB,OAAO;wDACL,YAAY;wDACZ,aAAa;wDACb,OAAO;oDACT;8DACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAWb,2BAAC,SAAG;4BAAC,QAAQ;gCAAC;gCAAI;6BAAG;4BAAE,OAAO;gCAAE,cAAc;4BAAG;;8CAC/C,2BAAC,SAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAI,IAAI;8CACvB,cAAA,2BAAC,UAAI;kDACH,cAAA,2BAAC,eAAS;4CACR,OAAM;4CACN,OAAO,WAAW,WAAW;4CAC7B,QAAO;4CACP,sBAAQ,2BAAC,mBAAY;gDAAC,OAAO;oDAAE,OAAO;gDAAU;;;;;;4CAChD,YAAY;gDAAE,OAAO;4CAAU;;;;;;;;;;;;;;;;8CAIrC,2BAAC,SAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAI,IAAI;8CACvB,cAAA,2BAAC,UAAI;kDACH,cAAA,2BAAC,eAAS;4CACR,OAAM;4CACN,OAAO,WAAW,WAAW,SAAS;4CACtC,sBAAQ,2BAAC,uBAAgB;gDAAC,OAAO;oDAAE,OAAO;gDAAU;;;;;;4CACpD,YAAY;gDAAE,OAAO;gDAAW,UAAU;4CAAG;;;;;;;;;;;;;;;;8CAInD,2BAAC,SAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAI,IAAI;8CACvB,cAAA,2BAAC,UAAI;kDACH,cAAA,2BAAC,eAAS;4CACR,OAAM;4CACN,OAAO,WAAW,WAAW,SAAS;4CACtC,sBAAQ,2BAAC,0BAAmB;gDAAC,OAAO;oDAAE,OAAO;gDAAU;;;;;;4CACvD,YAAY;gDAAE,OAAO;gDAAW,UAAU;4CAAG;;;;;;;;;;;;;;;;8CAInD,2BAAC,SAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAI,IAAI;8CACvB,cAAA,2BAAC,UAAI;kDACH,cAAA,2BAAC;4CAAI,OAAO;gDAAE,WAAW;4CAAS;;8DAChC,2BAAC;oDAAK,MAAK;oDAAY,OAAO;wDAAE,UAAU;oDAAG;8DAAG;;;;;;8DAChD,2BAAC;oDAAI,OAAO;wDAAE,WAAW;oDAAE;8DACzB,cAAA,2BAAC,cAAQ;wDACP,MAAK;wDACL,MAAM;wDACN,SAAS,KAAK,GAAG,CAAC,WAAW,WAAW,GAAG,IAAI;wDAC/C,aAAa;wDACb,QAAQ,kBACN,2BAAC;gEAAK,OAAO;oEAAE,UAAU;oEAAI,OAAO;gEAAqB;0EACtD,WAAW,WAAW,IAAI,KAAK,MAC/B,WAAW,WAAW,IAAI,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAWrD,2BAAC,uBAAc;4BACb,QAAQ,WAAW,EAAE;4BACrB,WAAW,WAAW,SAAS;4BAC/B,gBAAgB;;;;;;sCAIlB,2BAAC,0BAAiB;4BAChB,SAAS;4BACT,UAAU,IAAM,sBAAsB;4BACtC,WAAW;gCACT,sBAAsB;gCACtB;4BACF;4BACA,eAAe,WAAW,EAAE;;;;;;sCAI9B,2BAAC,WAAK;4BACJ,OAAM;4BACN,MAAM;4BACN,UAAU,IAAM,oBAAoB;4BACpC,QAAQ;4BACR,OAAO;sCAEP,cAAA,2BAAC,UAAI;gCACH,MAAM;gCACN,QAAO;gCACP,UAAU;;kDAEV,2BAAC,UAAI,CAAC,IAAI;wCACR,OAAM;wCACN,MAAK;wCACL,OAAO;4CACL;gDAAE,UAAU;gDAAM,SAAS;4CAAU;4CACrC;gDAAE,KAAK;gDAAI,SAAS;4CAAgB;yCACrC;kDAED,cAAA,2BAAC,WAAK;4CAAC,aAAY;;;;;;;;;;;kDAErB,2BAAC,UAAI,CAAC,IAAI;wCACR,OAAM;wCACN,MAAK;wCACL,OAAO;4CACL;gDAAE,KAAK;gDAAK,SAAS;4CAAiB;yCACvC;kDAED,cAAA,2BAAC;4CACC,MAAM;4CACN,aAAY;4CACZ,SAAS;4CACT,WAAW;;;;;;;;;;;kDAGf,2BAAC,UAAI,CAAC,IAAI;wCAAC,OAAO;4CAAE,cAAc;4CAAG,WAAW;wCAAQ;kDACtD,cAAA,2BAAC,WAAK;;8DACJ,2BAAC,YAAM;oDAAC,SAAS,IAAM,oBAAoB;8DAAQ;;;;;;8DAGnD,2BAAC,YAAM;oDAAC,MAAK;oDAAU,UAAS;oDAAS,SAAS;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAS1E;eApUM;;oBASW,UAAI,CAAC;oBACQ,aAAQ;;;iBAVhC;gBAsUN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCC3Gf;;;2BAAA;;;;;;oFA/Q2C;yCAepC;0CAMA;6CACqB;;;;;;;;;;YAG5B,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;YAC3B,MAAM,EAAE,MAAM,EAAE,GAAG,YAAM;YAgBzB,MAAM,oBAAsD,CAAC,EAC3D,OAAO,EACP,QAAQ,EACR,SAAS,EACT,aAAa,EACd;;gBACC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;gBACvC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAuB,EAAE;gBAC/D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAqB,EAAE;gBAC/E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAW,EAAE;gBACnE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,eAAQ;gBACpD,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;gBAE3B,IAAA,gBAAS,EAAC;oBACR,IAAI,SAAS;wBACX;wBACA;oBACF;gBACF,GAAG;oBAAC;iBAAQ;gBAEZ,MAAM,eAAe;oBACnB,IAAI;wBACF,MAAM,WAAW,MAAM,qBAAW,CAAC,cAAc,CAAC;4BAAE,SAAS;4BAAG,UAAU;wBAAK;wBAC/E,oBAAoB;wBACpB,MAAM,oBAAoB,SAAS,IAAI,CAAC,MAAM,CAAC,CAAA,SAAU,CAAC,OAAO,SAAS;wBAC1E,WAAW;oBACb,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAEA,MAAM,wBAAwB;oBAC5B,IAAI;wBACF,kBAAkB;wBAClB,MAAM,QAAQ,MAAM,qBAAW,CAAC,YAAY;wBAC5C,MAAM,aAAa,MAChB,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,eAC3B,GAAG,CAAC,CAAA,OAAS,CAAA;gCACZ,IAAI,KAAK,EAAE;gCACX,MAAM,KAAK,IAAI;gCACf,MAAM;gCACN,aAAa,KAAK,WAAW;4BAC/B,CAAA;wBAEF,oBAAoB;oBACtB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,cAAc;wBAC5B,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAEA,MAAM,eAAe;oBACnB,IAAI,gBAAgB,MAAM,KAAK,GAAG;wBAChC,aAAO,CAAC,OAAO,CAAC;wBAChB;oBACF;oBAEA,IAAI,CAAC,gBAAgB;wBACnB,aAAO,CAAC,OAAO,CAAC;wBAChB;oBACF;oBAEA,IAAI;wBACF,WAAW;wBAEX,oBAAoB;wBACpB,2BAA2B;wBAC3B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;wBAEjD,aAAO,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,gBAAgB,MAAM,CAAC,WAAW,CAAC;wBAC1D;wBACA;oBAEF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;wBACzB,aAAO,CAAC,KAAK,CAAC;oBAChB,SAAU;wBACR,WAAW;oBACb;gBACF;gBAEA,MAAM,eAAe;oBACnB,mBAAmB,EAAE;oBACrB,kBAAkB;oBAClB,KAAK,WAAW;oBAChB;gBACF;gBAEA,MAAM,wBAAwB,QAAQ,MAAM,CAAC,CAAA,SAC3C,gBAAgB,QAAQ,CAAC,OAAO,EAAE;gBAGpC,MAAM,wBAAwB,iBAAiB,IAAI,CAAC,CAAA,SAClD,OAAO,EAAE,KAAK;gBAGhB,qBACE,2BAAC,WAAK;oBACJ,OAAM;oBACN,MAAM;oBACN,UAAU;oBACV,QAAQ;sCACN,2BAAC,YAAM;4BAAc,SAAS;sCAAc;2BAAhC;;;;;sCAGZ,2BAAC,YAAM;4BAEL,MAAK;4BACL,SAAS;4BACT,SAAS;4BACT,UAAU,gBAAgB,MAAM,KAAK,KAAK,CAAC;sCAC5C;2BALK;;;;;qBAQP;oBACD,OAAO;;sCAEP,2BAAC,WAAK;4BACJ,SAAQ;4BACR,aAAY;4BACZ,MAAK;4BACL,QAAQ;4BACR,OAAO;gCAAE,cAAc;4BAAG;;;;;;sCAG5B,2BAAC,UAAI;4BAAC,MAAM;4BAAM,QAAO;;8CACvB,2BAAC,UAAI,CAAC,IAAI;oCAAC,OAAM;8CACf,cAAA,2BAAC;wCAAI,OAAO;4CAAE,WAAW;4CAAK,WAAW;4CAAQ,QAAQ;4CAAqB,cAAc;4CAAG,SAAS;wCAAE;kDACvG,QAAQ,MAAM,KAAK,kBAClB,2BAAC;4CAAK,MAAK;sDAAY;;;;;iEAEvB,2BAAC,UAAI;4CACH,MAAK;4CACL,YAAY;4CACZ,YAAY,CAAC,uBACX,2BAAC,UAAI,CAAC,IAAI;oDACR,OAAO;wDACL,QAAQ;wDACR,iBAAiB,gBAAgB,QAAQ,CAAC,OAAO,EAAE,IAAI,YAAY;wDACnE,SAAS;wDACT,cAAc;wDACd,QAAQ;oDACV;oDACA,SAAS;wDACP,IAAI,gBAAgB,QAAQ,CAAC,OAAO,EAAE,GACpC,mBAAmB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,OAAO,EAAE;6DAE7D,mBAAmB,CAAA,OAAQ;mEAAI;gEAAM,OAAO,EAAE;6DAAC;oDAEnD;;sEAEA,2BAAC,UAAI,CAAC,IAAI,CAAC,IAAI;4DACb,sBAAQ,2BAAC,YAAM;gEAAC,MAAK;gEAAQ,oBAAM,2BAAC,mBAAY;;;;;;;;;;4DAChD,qBACE,2BAAC,WAAK;;oEACH,OAAO,IAAI;oEACX,gBAAgB,QAAQ,CAAC,OAAO,EAAE,mBACjC,2BAAC,0BAAmB;wEAAC,OAAO;4EAAE,OAAO;wEAAU;;;;;;;;;;;;4DAIrD,aAAa,OAAO,KAAK;;;;;;sEAE3B,2BAAC,SAAG;4DAAC,OAAO,OAAO,QAAQ,GAAG,UAAU;sEACrC,OAAO,QAAQ,GAAG,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;8CASxC,2BAAC,UAAI,CAAC,IAAI;oCAAC,OAAM;8CACf,cAAA,2BAAC,YAAM;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU;wCACV,OAAO;4CAAE,OAAO;wCAAO;kDAEtB,iBAAiB,GAAG,CAAC,CAAA,uBACpB,2BAAC;gDAAuB,OAAO,OAAO,EAAE;0DACtC,cAAA,2BAAC,WAAK;;sEACJ,2BAAC,mBAAY;;;;;wDACZ,OAAO,IAAI;sEACZ,2BAAC;4DAAK,MAAK;;gEAAY;gEAAE,OAAO,WAAW;gEAAC;;;;;;;;;;;;;+CAJnC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;wBAY7B,gBAAgB,MAAM,GAAG,KAAK,uCAC7B;;8CACE,2BAAC,aAAO;;;;;8CACR,2BAAC;;sDACC,2BAAC;4CAAK,MAAM;sDAAC;;;;;;sDACb,2BAAC;4CAAI,OAAO;gDAAE,WAAW;gDAAG,SAAS;gDAAI,YAAY;gDAAW,cAAc;4CAAE;sDAC9E,cAAA,2BAAC,WAAK;gDAAC,WAAU;gDAAW,OAAO;oDAAE,OAAO;gDAAO;;kEACjD,2BAAC;;0EACC,2BAAC;;oEAAK;oEAAK,sBAAsB,MAAM;oEAAC;;;;;;;0EACxC,2BAAC;gEAAI,OAAO;oEAAE,WAAW;gEAAE;0EACxB,sBAAsB,GAAG,CAAC,CAAA,uBACzB,2BAAC,SAAG;wEAAiB,OAAM;wEAAO,OAAO;4EAAE,QAAQ;wEAAM;kFACtD,OAAO,IAAI;uEADJ,OAAO,EAAE;;;;;;;;;;;;;;;;kEAMzB,2BAAC;wDAAI,OAAO;4DAAE,WAAW;wDAAS;kEAChC,cAAA,2BAAC,mBAAY;4DAAC,OAAO;gEAAE,UAAU;gEAAI,OAAO;4DAAU;;;;;;;;;;;kEAExD,2BAAC;;0EACC,2BAAC;0EAAK;;;;;;0EACN,2BAAC,SAAG;gEAAC,OAAM;gEAAQ,OAAO;oEAAE,YAAY;gEAAE;0EACvC,sBAAsB,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAU/C;eAnOM;;oBAWW,UAAI,CAAC;;;iBAXhB;gBAqON,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IFhRD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,8BAA6B;YAAC;SAAmB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;YAAS;SAAyB;QAAC,mCAAkC;YAAC;SAAyB;QAAC,mCAAkC;YAAC;YAAS;SAAyB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,kCAAiC;YAAC;SAAwB;QAAC,wCAAuC;YAAC;SAAuC;IAAA;;AAC38B"}