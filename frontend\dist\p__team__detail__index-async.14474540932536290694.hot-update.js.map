{"version": 3, "sources": ["p__team__detail__index-async.14474540932536290694.hot-update.js", "src/pages/team/detail/index.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'p__team__detail__index',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='4099151087552102179';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/friend/index.tsx\":[\"p__friend__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"common\",\"p__subscription__index\"],\"src/pages/team/create/index.tsx\":[\"p__team__create__index\"],\"src/pages/team/detail/index.tsx\":[\"common\",\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"p__team__index\"],\"src/pages/user/index.tsx\":[\"common\",\"p__user__index\"],\"src/pages/user/login/index.tsx\":[\"p__user__login__index\"],\"src/pages/user/team-select/index.tsx\":[\"src/pages/user/team-select/index.tsx\"]});;\r\n  },\r\n);\r\n", "/**\n * 团队详情页面\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Descriptions,\n  Button,\n  Space,\n  Typography,\n  message,\n  Modal,\n  Form,\n  Input,\n  Spin,\n  Divider\n} from 'antd';\nimport {\n  TeamOutlined,\n  EditOutlined,\n  ArrowLeftOutlined,\n  DeleteOutlined,\n  ExclamationCircleOutlined\n} from '@ant-design/icons';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { TeamService } from '@/services';\nimport type { TeamDetailResponse, UpdateTeamRequest } from '@/types/api';\nimport { history, useModel } from '@umijs/max';\nimport TeamMemberList from './components/TeamMemberList';\nimport EnhancedTeamDetail from './components/EnhancedTeamDetail';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\n\nconst TeamDetailPage: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [teamDetail, setTeamDetail] = useState<TeamDetailResponse | null>(null);\n  const [useEnhancedUI, setUseEnhancedUI] = useState(true); // 控制是否使用新UI\n  const { setInitialState } = useModel('@@initialState');\n\n  useEffect(() => {\n    fetchTeamDetail();\n  }, []);\n\n  const fetchTeamDetail = async () => {\n    try {\n      setLoading(true);\n      const detail = await TeamService.getCurrentTeamDetail();\n      setTeamDetail(detail);\n    } catch (error) {\n      console.error('获取团队详情失败:', error);\n      message.error('获取团队详情失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 经典版UI的处理方法\n  const [editModalVisible, setEditModalVisible] = useState(false);\n  const [updating, setUpdating] = useState(false);\n  const [deleting, setDeleting] = useState(false);\n  const [form] = Form.useForm();\n\n  const handleEdit = () => {\n    if (teamDetail) {\n      form.setFieldsValue({\n        name: teamDetail.name,\n        description: teamDetail.description,\n      });\n      setEditModalVisible(true);\n    }\n  };\n\n  const handleUpdate = async (values: UpdateTeamRequest) => {\n    try {\n      setUpdating(true);\n      const updatedTeam = await TeamService.updateCurrentTeam(values);\n      setTeamDetail(updatedTeam);\n      setEditModalVisible(false);\n      message.success('团队信息更新成功');\n    } catch (error) {\n      console.error('更新团队信息失败:', error);\n    } finally {\n      setUpdating(false);\n    }\n  };\n\n  const handleDeleteTeam = () => {\n    Modal.confirm({\n      title: '确认删除团队',\n      content: '删除团队后，所有团队数据将无法恢复。确定要删除吗？',\n      icon: <ExclamationCircleOutlined />,\n      okText: '确认删除',\n      cancelText: '取消',\n      okType: 'danger',\n      onOk: async () => {\n        try {\n          setDeleting(true);\n          await TeamService.deleteTeam(teamDetail!.id);\n          message.success('团队删除成功');\n          // 更新全局状态，清除当前团队\n          setInitialState((s) => ({ ...s, currentTeam: undefined }));\n          history.push('/user/team-select');\n        } catch (error) {\n          console.error('删除团队失败:', error);\n          message.error('删除团队失败');\n        } finally {\n          setDeleting(false);\n        }\n      },\n    });\n  };\n\n  const handleGoBack = () => {\n    history.push('/user/team-select');\n  };\n\n  if (loading) {\n    return (\n      <PageContainer>\n        <div style={{ textAlign: 'center', padding: '50px 0' }}>\n          <Spin size=\"large\" />\n        </div>\n      </PageContainer>\n    );\n  }\n\n  if (!teamDetail) {\n    return (\n      <PageContainer>\n        <div style={{ textAlign: 'center', padding: '50px 0' }}>\n          <Text type=\"secondary\">团队信息加载失败</Text>\n        </div>\n      </PageContainer>\n    );\n  }\n\n  // 使用新的增强版UI\n  if (useEnhancedUI) {\n    return (\n      <PageContainer\n        title={\n          <Space align=\"center\">\n            <Button\n              type=\"text\"\n              icon={<ArrowLeftOutlined />}\n              onClick={handleGoBack}\n              style={{ color: '#666' }}\n            >\n              返回团队选择\n            </Button>\n            <Divider type=\"vertical\" />\n            <TeamOutlined style={{ color: '#1890ff' }} />\n            <Typography.Title level={4} style={{ margin: 0, color: '#333' }}>\n              {teamDetail?.name || '团队详情'}\n            </Typography.Title>\n            {teamDetail?.isCreator && (\n              <Typography.Text type=\"secondary\" style={{ fontSize: 14 }}>\n                (管理员)\n              </Typography.Text>\n            )}\n          </Space>\n        }\n        extra={\n          <Space>\n            <Button\n              type=\"text\"\n              size=\"small\"\n              onClick={() => setUseEnhancedUI(false)}\n              style={{ color: '#666' }}\n            >\n              切换到经典版\n            </Button>\n          </Space>\n        }\n        style={{ background: '#f5f5f5' }}\n      >\n        <EnhancedTeamDetail\n          teamDetail={teamDetail}\n          onRefresh={fetchTeamDetail}\n        />\n      </PageContainer>\n    );\n  }\n\n  // 原有的经典UI\n  return (\n    <PageContainer\n      title={\n        <Space align=\"center\">\n          <Button\n            type=\"text\"\n            icon={<ArrowLeftOutlined />}\n            onClick={handleGoBack}\n            style={{ color: '#666' }}\n          >\n            返回\n          </Button>\n          <Divider type=\"vertical\" />\n          <TeamOutlined style={{ color: '#1890ff' }} />\n          <Typography.Title level={4} style={{ margin: 0, color: '#333' }}>\n            {teamDetail.name}\n          </Typography.Title>\n          {teamDetail.isCreator && (\n            <Typography.Text type=\"secondary\" style={{ fontSize: 14 }}>\n              (管理员)\n            </Typography.Text>\n          )}\n        </Space>\n      }\n      extra={\n        <Space>\n          <Button\n            type=\"text\"\n            size=\"small\"\n            onClick={() => setUseEnhancedUI(true)}\n            style={{ color: '#1890ff' }}\n          >\n            切换到增强版\n          </Button>\n          {teamDetail.isCreator && (\n            <Space>\n              <Button\n                icon={<EditOutlined />}\n                onClick={handleEdit}\n                type=\"primary\"\n              >\n                编辑团队\n              </Button>\n              <Button\n                icon={<DeleteOutlined />}\n                onClick={handleDeleteTeam}\n                danger\n                loading={deleting}\n              >\n                删除团队\n              </Button>\n            </Space>\n          )}\n        </Space>\n      }\n    >\n      <Card title=\"团队信息\" style={{ marginBottom: 24 }}>\n        <Descriptions column={2}>\n          <Descriptions.Item label=\"团队名称\">\n            {teamDetail.name}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"成员数量\">\n            {teamDetail.memberCount} 人\n          </Descriptions.Item>\n          <Descriptions.Item label=\"创建时间\">\n            {new Date(teamDetail.createdAt).toLocaleString()}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"最后更新\">\n            {new Date(teamDetail.updatedAt).toLocaleString()}\n          </Descriptions.Item>\n          {teamDetail.description && (\n            <Descriptions.Item label=\"团队描述\" span={2}>\n              {teamDetail.description}\n            </Descriptions.Item>\n          )}\n        </Descriptions>\n      </Card>\n\n      <TeamMemberList \n        teamId={teamDetail.id}\n        isCreator={teamDetail.isCreator}\n        onMemberChange={fetchTeamDetail}\n      />\n\n      {/* 编辑团队模态框 */}\n      <Modal\n        title=\"编辑团队信息\"\n        open={editModalVisible}\n        onCancel={() => setEditModalVisible(false)}\n        footer={null}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleUpdate}\n        >\n          <Form.Item\n            label=\"团队名称\"\n            name=\"name\"\n            rules={[\n              { required: true, message: '请输入团队名称！' },\n              { max: 100, message: '团队名称长度不能超过100字符！' },\n            ]}\n          >\n            <Input placeholder=\"请输入团队名称\" />\n          </Form.Item>\n\n          <Form.Item\n            label=\"团队描述\"\n            name=\"description\"\n            rules={[\n              { max: 500, message: '团队描述长度不能超过500字符！' },\n            ]}\n          >\n            <TextArea\n              placeholder=\"请输入团队描述（可选）\"\n              rows={4}\n              showCount\n              maxLength={500}\n            />\n          </Form.Item>\n\n          <Form.Item>\n            <Space>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                loading={updating}\n              >\n                保存\n              </Button>\n              <Button onClick={() => setEditModalVisible(false)}>\n                取消\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n    </PageContainer>\n  );\n};\n\nexport default TeamDetailPage;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,0BACA;IACE,SAAS;;;;;;wCCuUb;;;2BAAA;;;;;;;oFAtU2C;yCAapC;0CAOA;kDACuB;6CACF;wCAEM;4FACP;gGACI;;;;;;;;;;YAE/B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;YAClC,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAK;YAE1B,MAAM,iBAA2B;;gBAC/B,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;gBACvC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAA4B;gBACxE,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAC,OAAO,YAAY;gBACtE,MAAM,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;gBAErC,IAAA,gBAAS,EAAC;oBACR;gBACF,GAAG,EAAE;gBAEL,MAAM,kBAAkB;oBACtB,IAAI;wBACF,WAAW;wBACX,MAAM,SAAS,MAAM,qBAAW,CAAC,oBAAoB;wBACrD,cAAc;oBAChB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,aAAO,CAAC,KAAK,CAAC;oBAChB,SAAU;wBACR,WAAW;oBACb;gBACF;gBAEA,aAAa;gBACb,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;gBACzD,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;gBACzC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;gBACzC,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;gBAE3B,MAAM,aAAa;oBACjB,IAAI,YAAY;wBACd,KAAK,cAAc,CAAC;4BAClB,MAAM,WAAW,IAAI;4BACrB,aAAa,WAAW,WAAW;wBACrC;wBACA,oBAAoB;oBACtB;gBACF;gBAEA,MAAM,eAAe,OAAO;oBAC1B,IAAI;wBACF,YAAY;wBACZ,MAAM,cAAc,MAAM,qBAAW,CAAC,iBAAiB,CAAC;wBACxD,cAAc;wBACd,oBAAoB;wBACpB,aAAO,CAAC,OAAO,CAAC;oBAClB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;oBAC7B,SAAU;wBACR,YAAY;oBACd;gBACF;gBAEA,MAAM,mBAAmB;oBACvB,WAAK,CAAC,OAAO,CAAC;wBACZ,OAAO;wBACP,SAAS;wBACT,oBAAM,2BAAC,gCAAyB;;;;;wBAChC,QAAQ;wBACR,YAAY;wBACZ,QAAQ;wBACR,MAAM;4BACJ,IAAI;gCACF,YAAY;gCACZ,MAAM,qBAAW,CAAC,UAAU,CAAC,WAAY,EAAE;gCAC3C,aAAO,CAAC,OAAO,CAAC;gCAChB,gBAAgB;gCAChB,gBAAgB,CAAC,IAAO,CAAA;wCAAE,GAAG,CAAC;wCAAE,aAAa;oCAAU,CAAA;gCACvD,YAAO,CAAC,IAAI,CAAC;4BACf,EAAE,OAAO,OAAO;gCACd,QAAQ,KAAK,CAAC,WAAW;gCACzB,aAAO,CAAC,KAAK,CAAC;4BAChB,SAAU;gCACR,YAAY;4BACd;wBACF;oBACF;gBACF;gBAEA,MAAM,eAAe;oBACnB,YAAO,CAAC,IAAI,CAAC;gBACf;gBAEA,IAAI,SACF,qBACE,2BAAC,4BAAa;8BACZ,cAAA,2BAAC;wBAAI,OAAO;4BAAE,WAAW;4BAAU,SAAS;wBAAS;kCACnD,cAAA,2BAAC,UAAI;4BAAC,MAAK;;;;;;;;;;;;;;;;gBAMnB,IAAI,CAAC,YACH,qBACE,2BAAC,4BAAa;8BACZ,cAAA,2BAAC;wBAAI,OAAO;4BAAE,WAAW;4BAAU,SAAS;wBAAS;kCACnD,cAAA,2BAAC;4BAAK,MAAK;sCAAY;;;;;;;;;;;;;;;;gBAM/B,YAAY;gBACZ,IAAI,eACF,qBACE,2BAAC,4BAAa;oBACZ,qBACE,2BAAC,WAAK;wBAAC,OAAM;;0CACX,2BAAC,YAAM;gCACL,MAAK;gCACL,oBAAM,2BAAC,wBAAiB;;;;;gCACxB,SAAS;gCACT,OAAO;oCAAE,OAAO;gCAAO;0CACxB;;;;;;0CAGD,2BAAC,aAAO;gCAAC,MAAK;;;;;;0CACd,2BAAC,mBAAY;gCAAC,OAAO;oCAAE,OAAO;gCAAU;;;;;;0CACxC,2BAAC,gBAAU,CAAC,KAAK;gCAAC,OAAO;gCAAG,OAAO;oCAAE,QAAQ;oCAAG,OAAO;gCAAO;0CAC3D,CAAA,uBAAA,iCAAA,WAAY,IAAI,KAAI;;;;;;4BAEtB,CAAA,uBAAA,iCAAA,WAAY,SAAS,mBACpB,2BAAC,gBAAU,CAAC,IAAI;gCAAC,MAAK;gCAAY,OAAO;oCAAE,UAAU;gCAAG;0CAAG;;;;;;;;;;;;oBAMjE,qBACE,2BAAC,WAAK;kCACJ,cAAA,2BAAC,YAAM;4BACL,MAAK;4BACL,MAAK;4BACL,SAAS,IAAM,iBAAiB;4BAChC,OAAO;gCAAE,OAAO;4BAAO;sCACxB;;;;;;;;;;;oBAKL,OAAO;wBAAE,YAAY;oBAAU;8BAE/B,cAAA,2BAAC,2BAAkB;wBACjB,YAAY;wBACZ,WAAW;;;;;;;;;;;gBAMnB,UAAU;gBACV,qBACE,2BAAC,4BAAa;oBACZ,qBACE,2BAAC,WAAK;wBAAC,OAAM;;0CACX,2BAAC,YAAM;gCACL,MAAK;gCACL,oBAAM,2BAAC,wBAAiB;;;;;gCACxB,SAAS;gCACT,OAAO;oCAAE,OAAO;gCAAO;0CACxB;;;;;;0CAGD,2BAAC,aAAO;gCAAC,MAAK;;;;;;0CACd,2BAAC,mBAAY;gCAAC,OAAO;oCAAE,OAAO;gCAAU;;;;;;0CACxC,2BAAC,gBAAU,CAAC,KAAK;gCAAC,OAAO;gCAAG,OAAO;oCAAE,QAAQ;oCAAG,OAAO;gCAAO;0CAC3D,WAAW,IAAI;;;;;;4BAEjB,WAAW,SAAS,kBACnB,2BAAC,gBAAU,CAAC,IAAI;gCAAC,MAAK;gCAAY,OAAO;oCAAE,UAAU;gCAAG;0CAAG;;;;;;;;;;;;oBAMjE,qBACE,2BAAC,WAAK;;0CACJ,2BAAC,YAAM;gCACL,MAAK;gCACL,MAAK;gCACL,SAAS,IAAM,iBAAiB;gCAChC,OAAO;oCAAE,OAAO;gCAAU;0CAC3B;;;;;;4BAGA,WAAW,SAAS,kBACnB,2BAAC,WAAK;;kDACJ,2BAAC,YAAM;wCACL,oBAAM,2BAAC,mBAAY;;;;;wCACnB,SAAS;wCACT,MAAK;kDACN;;;;;;kDAGD,2BAAC,YAAM;wCACL,oBAAM,2BAAC,qBAAc;;;;;wCACrB,SAAS;wCACT,MAAM;wCACN,SAAS;kDACV;;;;;;;;;;;;;;;;;;;sCAQT,2BAAC,UAAI;4BAAC,OAAM;4BAAO,OAAO;gCAAE,cAAc;4BAAG;sCAC3C,cAAA,2BAAC,kBAAY;gCAAC,QAAQ;;kDACpB,2BAAC,kBAAY,CAAC,IAAI;wCAAC,OAAM;kDACtB,WAAW,IAAI;;;;;;kDAElB,2BAAC,kBAAY,CAAC,IAAI;wCAAC,OAAM;;4CACtB,WAAW,WAAW;4CAAC;;;;;;;kDAE1B,2BAAC,kBAAY,CAAC,IAAI;wCAAC,OAAM;kDACtB,IAAI,KAAK,WAAW,SAAS,EAAE,cAAc;;;;;;kDAEhD,2BAAC,kBAAY,CAAC,IAAI;wCAAC,OAAM;kDACtB,IAAI,KAAK,WAAW,SAAS,EAAE,cAAc;;;;;;oCAE/C,WAAW,WAAW,kBACrB,2BAAC,kBAAY,CAAC,IAAI;wCAAC,OAAM;wCAAO,MAAM;kDACnC,WAAW,WAAW;;;;;;;;;;;;;;;;;sCAM/B,2BAAC,uBAAc;4BACb,QAAQ,WAAW,EAAE;4BACrB,WAAW,WAAW,SAAS;4BAC/B,gBAAgB;;;;;;sCAIlB,2BAAC,WAAK;4BACJ,OAAM;4BACN,MAAM;4BACN,UAAU,IAAM,oBAAoB;4BACpC,QAAQ;sCAER,cAAA,2BAAC,UAAI;gCACH,MAAM;gCACN,QAAO;gCACP,UAAU;;kDAEV,2BAAC,UAAI,CAAC,IAAI;wCACR,OAAM;wCACN,MAAK;wCACL,OAAO;4CACL;gDAAE,UAAU;gDAAM,SAAS;4CAAW;4CACtC;gDAAE,KAAK;gDAAK,SAAS;4CAAmB;yCACzC;kDAED,cAAA,2BAAC,WAAK;4CAAC,aAAY;;;;;;;;;;;kDAGrB,2BAAC,UAAI,CAAC,IAAI;wCACR,OAAM;wCACN,MAAK;wCACL,OAAO;4CACL;gDAAE,KAAK;gDAAK,SAAS;4CAAmB;yCACzC;kDAED,cAAA,2BAAC;4CACC,aAAY;4CACZ,MAAM;4CACN,SAAS;4CACT,WAAW;;;;;;;;;;;kDAIf,2BAAC,UAAI,CAAC,IAAI;kDACR,cAAA,2BAAC,WAAK;;8DACJ,2BAAC,YAAM;oDACL,MAAK;oDACL,UAAS;oDACT,SAAS;8DACV;;;;;;8DAGD,2BAAC,YAAM;oDAAC,SAAS,IAAM,oBAAoB;8DAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUjE;eArSM;;oBAIwB,aAAQ;oBAuBrB,UAAI,CAAC;;;iBA3BhB;gBAuSN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDvUD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,8BAA6B;YAAC;SAAmB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;YAAS;SAAyB;QAAC,mCAAkC;YAAC;SAAyB;QAAC,mCAAkC;YAAC;YAAS;SAAyB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,kCAAiC;YAAC;SAAwB;QAAC,wCAAuC;YAAC;SAAuC;IAAA;;AAC38B"}