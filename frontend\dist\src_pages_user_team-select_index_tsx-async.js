((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] || []).push([
        ['src/pages/user/team-select/index.tsx'],
{ "src/pages/user/team-select/components/ActionButtons.tsx": function (module, exports, __mako_require__){
/**
 * 操作按钮组件
 * 用于团队选择页面的操作按钮，包括进入团队、创建团队、退出登录
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _antdstyle = __mako_require__("node_modules/antd-style/es/index.js");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const useStyles = (0, _antdstyle.createStyles)(()=>{
    return {
        actions: {
            marginTop: 24,
            display: 'flex',
            gap: 16,
            justifyContent: 'center',
            flexWrap: 'wrap'
        },
        createTeamButton: {
            borderStyle: 'dashed'
        }
    };
});
const ActionButtons = ({ hasTeams, selectedTeamId, loading, onTeamLogin, onCreateTeam, onLogout, showCreateButton = true })=>{
    _s();
    const { styles } = useStyles();
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        className: styles.actions,
        children: [
            hasTeams && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                type: "primary",
                size: "large",
                loading: loading,
                disabled: !selectedTeamId,
                onClick: onTeamLogin,
                children: "确认进入"
            }, void 0, false, {
                fileName: "src/pages/user/team-select/components/ActionButtons.tsx",
                lineNumber: 51,
                columnNumber: 9
            }, this),
            showCreateButton && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                size: "large",
                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                    fileName: "src/pages/user/team-select/components/ActionButtons.tsx",
                    lineNumber: 66,
                    columnNumber: 17
                }, void 0),
                className: styles.createTeamButton,
                onClick: onCreateTeam,
                children: "创建新团队"
            }, void 0, false, {
                fileName: "src/pages/user/team-select/components/ActionButtons.tsx",
                lineNumber: 64,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                size: "large",
                onClick: onLogout,
                children: "退出登录"
            }, void 0, false, {
                fileName: "src/pages/user/team-select/components/ActionButtons.tsx",
                lineNumber: 74,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/user/team-select/components/ActionButtons.tsx",
        lineNumber: 48,
        columnNumber: 5
    }, this);
};
_s(ActionButtons, "1BGFRu6BGAbhzJ8kKgs1GUjvI6w=", false, function() {
    return [
        useStyles
    ];
});
_c = ActionButtons;
var _default = ActionButtons;
var _c;
$RefreshReg$(_c, "ActionButtons");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/user/team-select/components/TeamList.tsx": function (module, exports, __mako_require__){
/**
 * 团队列表组件
 * 用于显示用户的团队列表并支持选择
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _antdstyle = __mako_require__("node_modules/antd-style/es/index.js");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Text } = _antd.Typography;
const useStyles = (0, _antdstyle.createStyles)(({ token })=>{
    return {
        teamList: {
            width: '100%'
        },
        teamItem: {
            padding: '16px 24px',
            cursor: 'pointer',
            borderRadius: token.borderRadius,
            transition: 'all 0.3s',
            '&:hover': {
                backgroundColor: token.colorBgTextHover
            }
        },
        teamItemSelected: {
            backgroundColor: token.colorPrimaryBg,
            borderColor: token.colorPrimary
        }
    };
});
const TeamList = ({ teams, selectedTeamId, onTeamSelect })=>{
    _s();
    const { styles } = useStyles();
    if (teams.length === 0) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        style: {
            textAlign: 'center',
            padding: '40px 0'
        },
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                style: {
                    fontSize: 64,
                    color: '#d9d9d9',
                    marginBottom: 16
                }
            }, void 0, false, {
                fileName: "src/pages/user/team-select/components/TeamList.tsx",
                lineNumber: 51,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Title, {
                level: 4,
                type: "secondary",
                children: "您还没有加入任何团队"
            }, void 0, false, {
                fileName: "src/pages/user/team-select/components/TeamList.tsx",
                lineNumber: 52,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                type: "secondary",
                children: "创建一个新团队或等待其他人邀请您加入"
            }, void 0, false, {
                fileName: "src/pages/user/team-select/components/TeamList.tsx",
                lineNumber: 55,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/user/team-select/components/TeamList.tsx",
        lineNumber: 50,
        columnNumber: 7
    }, this);
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
        className: styles.teamList,
        dataSource: teams,
        renderItem: (team)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                className: `${styles.teamItem} ${selectedTeamId === team.id ? styles.teamItemSelected : ''}`,
                onClick: ()=>onTeamSelect(team.id),
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item.Meta, {
                    avatar: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                        size: "large",
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                            fileName: "src/pages/user/team-select/components/TeamList.tsx",
                            lineNumber: 75,
                            columnNumber: 42
                        }, void 0)
                    }, void 0, false, {
                        fileName: "src/pages/user/team-select/components/TeamList.tsx",
                        lineNumber: 75,
                        columnNumber: 15
                    }, void 0),
                    title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        children: [
                            team.name,
                            team.isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                type: "secondary",
                                style: {
                                    fontSize: 12
                                },
                                children: "(创建者)"
                            }, void 0, false, {
                                fileName: "src/pages/user/team-select/components/TeamList.tsx",
                                lineNumber: 81,
                                columnNumber: 19
                            }, void 0)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/user/team-select/components/TeamList.tsx",
                        lineNumber: 78,
                        columnNumber: 15
                    }, void 0),
                    description: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                fileName: "src/pages/user/team-select/components/TeamList.tsx",
                                lineNumber: 89,
                                columnNumber: 17
                            }, void 0),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                type: "secondary",
                                children: [
                                    team.memberCount,
                                    " 名成员"
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/user/team-select/components/TeamList.tsx",
                                lineNumber: 90,
                                columnNumber: 17
                            }, void 0),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                type: "secondary",
                                children: [
                                    "最后访问: ",
                                    new Date(team.lastAccessTime).toLocaleDateString()
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/user/team-select/components/TeamList.tsx",
                                lineNumber: 91,
                                columnNumber: 17
                            }, void 0)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/user/team-select/components/TeamList.tsx",
                        lineNumber: 88,
                        columnNumber: 15
                    }, void 0)
                }, void 0, false, {
                    fileName: "src/pages/user/team-select/components/TeamList.tsx",
                    lineNumber: 73,
                    columnNumber: 11
                }, void 0)
            }, void 0, false, {
                fileName: "src/pages/user/team-select/components/TeamList.tsx",
                lineNumber: 67,
                columnNumber: 9
            }, void 0)
    }, void 0, false, {
        fileName: "src/pages/user/team-select/components/TeamList.tsx",
        lineNumber: 63,
        columnNumber: 5
    }, this);
};
_s(TeamList, "1BGFRu6BGAbhzJ8kKgs1GUjvI6w=", false, function() {
    return [
        useStyles
    ];
});
_c = TeamList;
var _default = TeamList;
var _c;
$RefreshReg$(_c, "TeamList");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/user/team-select/components/index.ts": function (module, exports, __mako_require__){
/**
 * 团队选择页面组件导出
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
__mako_require__.e(exports, {
    ActionButtons: function() {
        return _ActionButtons.default;
    },
    TeamList: function() {
        return _TeamList.default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _TeamList = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/user/team-select/components/TeamList.tsx"));
var _ActionButtons = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/user/team-select/components/ActionButtons.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/user/team-select/index.tsx": function (module, exports, __mako_require__){
/**
 * 团队选择页面
 * 实现双阶段认证的第二阶段：团队登录
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _antdstyle = __mako_require__("node_modules/antd-style/es/index.js");
var _services = __mako_require__("src/services/index.ts");
var _components = __mako_require__("src/pages/user/team-select/components/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text } = _antd.Typography;
const useStyles = (0, _antdstyle.createStyles)(({ token })=>{
    return {
        container: {
            display: 'flex',
            flexDirection: 'column',
            height: '100vh',
            overflow: 'auto',
            backgroundColor: token.colorBgLayout
        },
        content: {
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '32px 16px'
        },
        header: {
            marginBottom: 40,
            textAlign: 'center'
        },
        teamCard: {
            width: '100%',
            maxWidth: 600,
            marginBottom: 24
        }
    };
});
const TeamSelectPage = ()=>{
    _s();
    const [loading, setLoading] = (0, _react.useState)(false);
    const [selectedTeamId, setSelectedTeamId] = (0, _react.useState)(null);
    const [teams, setTeams] = (0, _react.useState)([]);
    const { styles } = useStyles();
    const location = (0, _max.useLocation)();
    const { initialState, setInitialState } = (0, _max.useModel)('@@initialState');
    (0, _react.useEffect)(()=>{
        var _location_state;
        // 从路由状态获取团队列表
        const teamsFromState = (_location_state = location.state) === null || _location_state === void 0 ? void 0 : _location_state.teams;
        if (teamsFromState) setTeams(teamsFromState);
        else // 如果没有团队数据，重新获取
        fetchTeams();
    }, [
        location.state
    ]);
    const fetchTeams = async ()=>{
        try {
            setLoading(true);
            const teamList = await _services.TeamService.getUserTeams();
            const teamInfos = teamList.map((team)=>({
                    id: team.id,
                    name: team.name,
                    isCreator: team.isCreator,
                    memberCount: team.memberCount,
                    lastAccessTime: team.updatedAt
                }));
            setTeams(teamInfos);
        } catch (error) {
            console.error('获取团队列表失败:', error);
            _antd.message.error('获取团队列表失败');
        } finally{
            setLoading(false);
        }
    };
    const handleTeamSelect = async ()=>{
        if (!selectedTeamId) {
            _antd.message.warning('请选择一个团队');
            return;
        }
        setLoading(true);
        try {
            const response = await _services.AuthService.selectTeam({
                teamId: selectedTeamId
            });
            // 检查后端返回的团队选择成功标识
            if (response.teamSelectionSuccess && response.team && response.team.id === selectedTeamId) {
                _antd.message.success('团队选择成功！');
                // 同步更新 initialState，等待更新完成后再跳转
                if ((initialState === null || initialState === void 0 ? void 0 : initialState.fetchTeamInfo) && (initialState === null || initialState === void 0 ? void 0 : initialState.fetchUserInfo) && setInitialState) try {
                    const [currentUser, currentTeam] = await Promise.all([
                        initialState.fetchUserInfo(),
                        initialState.fetchTeamInfo()
                    ]);
                    // 确保团队信息已正确获取
                    if (currentTeam && currentTeam.id === selectedTeamId) {
                        await setInitialState({
                            ...initialState,
                            currentUser,
                            currentTeam
                        });
                        // 等待 initialState 更新完成后再跳转
                        setTimeout(()=>{
                            _max.history.push('/dashboard');
                        }, 100);
                    } else {
                        console.error('获取的团队信息与选择的团队不匹配');
                        _antd.message.error('团队选择失败，请重试');
                    }
                } catch (error) {
                    console.error('更新 initialState 失败:', error);
                    _antd.message.error('团队选择失败，请重试');
                }
                else // 如果没有 initialState 相关方法，直接跳转
                _max.history.push('/dashboard');
            } else {
                console.error('团队选择响应异常，未返回正确的团队信息');
                _antd.message.error('团队选择失败，请重试');
            }
        } catch (error) {
            console.error('团队选择失败:', error);
            _antd.message.error('团队选择失败，请重试');
        } finally{
            setLoading(false);
        }
    };
    // 保持兼容性的方法名
    const handleTeamLogin = handleTeamSelect;
    const handleCreateTeam = ()=>{
        _max.history.push('/team/create');
    };
    const handleLogout = ()=>{
        _services.AuthService.clearTokens();
        _max.history.push('/user/login');
    };
    if (loading && teams.length === 0) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        className: styles.container,
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
            className: styles.content,
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                size: "large"
            }, void 0, false, {
                fileName: "src/pages/user/team-select/index.tsx",
                lineNumber: 161,
                columnNumber: 11
            }, this)
        }, void 0, false, {
            fileName: "src/pages/user/team-select/index.tsx",
            lineNumber: 160,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "src/pages/user/team-select/index.tsx",
        lineNumber: 159,
        columnNumber: 7
    }, this);
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        className: styles.container,
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
            className: styles.content,
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    className: styles.header,
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        direction: "vertical",
                        align: "center",
                        size: "large",
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                style: {
                                    fontSize: 48,
                                    color: '#1890ff'
                                }
                            }, void 0, false, {
                                fileName: "src/pages/user/team-select/index.tsx",
                                lineNumber: 172,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                        level: 2,
                                        children: "选择团队"
                                    }, void 0, false, {
                                        fileName: "src/pages/user/team-select/index.tsx",
                                        lineNumber: 174,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        children: "请选择要进入的团队工作空间"
                                    }, void 0, false, {
                                        fileName: "src/pages/user/team-select/index.tsx",
                                        lineNumber: 175,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/user/team-select/index.tsx",
                                lineNumber: 173,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/user/team-select/index.tsx",
                        lineNumber: 171,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/user/team-select/index.tsx",
                    lineNumber: 170,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                    className: styles.teamCard,
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_components.TeamList, {
                        teams: teams,
                        selectedTeamId: selectedTeamId,
                        onTeamSelect: setSelectedTeamId
                    }, void 0, false, {
                        fileName: "src/pages/user/team-select/index.tsx",
                        lineNumber: 181,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/user/team-select/index.tsx",
                    lineNumber: 180,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_components.ActionButtons, {
                    hasTeams: teams.length > 0,
                    selectedTeamId: selectedTeamId,
                    loading: loading,
                    onTeamLogin: handleTeamLogin,
                    onCreateTeam: handleCreateTeam,
                    onLogout: handleLogout,
                    showCreateButton: true
                }, void 0, false, {
                    fileName: "src/pages/user/team-select/index.tsx",
                    lineNumber: 188,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/user/team-select/index.tsx",
            lineNumber: 169,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "src/pages/user/team-select/index.tsx",
        lineNumber: 168,
        columnNumber: 5
    }, this);
};
_s(TeamSelectPage, "+CU4cMwFBAEWrP0ROrUWZuFjHQM=", false, function() {
    return [
        useStyles,
        _max.useLocation,
        _max.useModel
    ];
});
_c = TeamSelectPage;
var _default = TeamSelectPage;
var _c;
$RefreshReg$(_c, "TeamSelectPage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=src_pages_user_team-select_index_tsx-async.js.map