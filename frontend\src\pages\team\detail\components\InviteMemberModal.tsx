/**
 * 邀请成员模态框组件
 */

import React, { useState } from 'react';
import {
  Modal,
  Form,
  Input,
  Button,
  Space,
  Tag,
  Typography,
  Divider,
  message,
  Alert,
  Progress,
  List,
  Tooltip
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  MailOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { TeamService } from '@/services';
import type { InviteMembersRequest } from '@/types/api';

const { Text } = Typography;

interface InviteMemberModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
}

interface InviteResult {
  email: string;
  success: boolean;
  error?: string;
}

const InviteMemberModal: React.FC<InviteMemberModalProps> = ({
  visible,
  onCancel,
  onSuccess,
}) => {
  const [loading, setLoading] = useState(false);
  const [emails, setEmails] = useState<string[]>(['']);
  const [form] = Form.useForm();
  const [inviteResults, setInviteResults] = useState<InviteResult[]>([]);
  const [showResults, setShowResults] = useState(false);

  const handleAddEmail = () => {
    if (emails.length < 10) {
      setEmails([...emails, '']);
    } else {
      message.warning('一次最多邀请10个成员');
    }
  };

  const handleRemoveEmail = (index: number) => {
    if (emails.length > 1) {
      const newEmails = emails.filter((_, i) => i !== index);
      setEmails(newEmails);
    }
  };

  const handleEmailChange = (index: number, value: string) => {
    const newEmails = [...emails];
    newEmails[index] = value;
    setEmails(newEmails);
  };

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSubmit = async () => {
    // 过滤空邮箱并验证格式
    const validEmails = emails.filter(email => email.trim() !== '');
    
    if (validEmails.length === 0) {
      message.warning('请至少输入一个邮箱地址');
      return;
    }

    // 验证邮箱格式
    const invalidEmails = validEmails.filter(email => !validateEmail(email));
    if (invalidEmails.length > 0) {
      message.error(`以下邮箱格式不正确: ${invalidEmails.join(', ')}`);
      return;
    }

    // 检查重复邮箱
    const uniqueEmails = [...new Set(validEmails)];
    if (uniqueEmails.length !== validEmails.length) {
      message.warning('存在重复的邮箱地址');
      return;
    }

    try {
      setLoading(true);
      setShowResults(false);

      const request: InviteMembersRequest = {
        emails: uniqueEmails,
      };

      await TeamService.inviteMembers(request);

      // 模拟邀请结果（实际应该从后端返回）
      const results: InviteResult[] = uniqueEmails.map(email => ({
        email,
        success: true,
      }));

      setInviteResults(results);
      setShowResults(true);

      message.success(`成功发送 ${uniqueEmails.length} 份邀请`);

      // 延迟关闭模态框，让用户看到结果
      setTimeout(() => {
        onSuccess();
        handleCancel();
      }, 2000);

    } catch (error: any) {
      console.error('邀请成员失败:', error);

      // 处理部分成功的情况
      const results: InviteResult[] = uniqueEmails.map(email => ({
        email,
        success: false,
        error: error.message || '邀请失败',
      }));

      setInviteResults(results);
      setShowResults(true);
      message.error('邀请发送失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setEmails(['']);
    setInviteResults([]);
    setShowResults(false);
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title="邀请团队成员"
      open={visible}
      onCancel={handleCancel}
      footer={
        showResults ? [
          <Button key="close" type="primary" onClick={handleCancel}>
            关闭
          </Button>,
        ] : [
          <Button key="cancel" onClick={handleCancel}>
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={loading}
            onClick={handleSubmit}
            disabled={emails.filter(email => email.trim() !== '').length === 0}
          >
            发送邀请
          </Button>,
        ]
      }
      width={700}
    >
      {!showResults ? (
        <>
          <div style={{ marginBottom: 16 }}>
            <Text type="secondary">
              输入要邀请的成员邮箱地址，系统将发送邀请邮件给他们。
            </Text>
          </div>

          <Alert
            message="邀请说明"
            description={
              <ul style={{ marginTop: 8, paddingLeft: 20, marginBottom: 0 }}>
                <li>邀请邮件将发送到指定邮箱</li>
                <li>受邀者需要注册账号后才能加入团队</li>
                <li>一次最多可邀请10个成员</li>
                <li>邀请链接有效期为7天</li>
              </ul>
            }
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
        </>
      ) : (
        <div style={{ marginBottom: 16 }}>
          <Alert
            message="邀请结果"
            description={`已处理 ${inviteResults.length} 个邀请`}
            type={inviteResults.every(r => r.success) ? 'success' : 'warning'}
            showIcon
            style={{ marginBottom: 16 }}
          />

          <List
            size="small"
            dataSource={inviteResults}
            renderItem={(result) => (
              <List.Item>
                <Space>
                  {result.success ? (
                    <CheckCircleOutlined style={{ color: '#52c41a' }} />
                  ) : (
                    <ExclamationCircleOutlined style={{ color: '#faad14' }} />
                  )}
                  <Text>{result.email}</Text>
                  {result.success ? (
                    <Tag color="success">邀请已发送</Tag>
                  ) : (
                    <Tooltip title={result.error}>
                      <Tag color="warning">发送失败</Tag>
                    </Tooltip>
                  )}
                </Space>
              </List.Item>
            )}
          />
        </div>
      )}

      {!showResults && (
        <Form form={form} layout="vertical">
          <Form.Item label="邀请邮箱">
            <Space direction="vertical" style={{ width: '100%' }}>
              {emails.map((email, index) => (
                <Space key={index} style={{ width: '100%' }}>
                  <Input
                    placeholder="请输入邮箱地址"
                    value={email}
                    onChange={(e) => handleEmailChange(index, e.target.value)}
                    prefix={<MailOutlined />}
                    style={{ flex: 1 }}
                    status={email && !validateEmail(email) ? 'error' : ''}
                  />
                  {emails.length > 1 && (
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => handleRemoveEmail(index)}
                    />
                  )}
                </Space>
              ))}
            </Space>
          </Form.Item>

          {emails.length < 10 && (
            <Button
              type="dashed"
              icon={<PlusOutlined />}
              onClick={handleAddEmail}
              style={{ width: '100%', marginBottom: 16 }}
            >
              添加更多邮箱
            </Button>
          )}

          {emails.filter(email => email.trim() !== '').length > 0 && (
            <div style={{ marginTop: 16 }}>
              <Text strong>待邀请邮箱：</Text>
              <div style={{ marginTop: 8 }}>
                {emails
                  .filter(email => email.trim() !== '')
                  .map((email, index) => (
                    <Tag
                      key={index}
                      color={validateEmail(email) ? 'blue' : 'red'}
                      style={{ marginBottom: 4 }}
                    >
                      {email}
                    </Tag>
                  ))}
              </div>
            </div>
          )}
        </Form>
      )}
    </Modal>
  );
};

export default InviteMemberModal;
