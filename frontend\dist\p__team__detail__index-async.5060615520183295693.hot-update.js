globalThis.makoModuleHotUpdate('p__team__detail__index', {
    modules: {
        "src/.umi/core/route.tsx": function(module, exports, __mako_require__) {
            "use strict";
            var interop = __mako_require__("@swc/helpers/_/_interop_require_wildcard")._;
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "getRoutes", {
                enumerable: true,
                get: function() {
                    return getRoutes;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _react = _interop_require_default._(__mako_require__("node_modules/react/index.js"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            async function getRoutes() {
                const routes = {
                    "1": {
                        "path": "/user",
                        "layout": false,
                        "id": "1"
                    },
                    "2": {
                        "name": "login",
                        "path": "/user/login",
                        "parentId": "1",
                        "id": "2"
                    },
                    "3": {
                        "name": "team-select",
                        "path": "/user/team-select",
                        "parentId": "1",
                        "id": "3"
                    },
                    "4": {
                        "path": "/team/create",
                        "layout": false,
                        "id": "4"
                    },
                    "5": {
                        "path": "/dashboard",
                        "name": "仪表盘",
                        "icon": "dashboard",
                        "parentId": "ant-design-pro-layout",
                        "id": "5"
                    },
                    "6": {
                        "path": "/team",
                        "name": "团队管理",
                        "icon": "team",
                        "hideInMenu": true,
                        "parentId": "ant-design-pro-layout",
                        "id": "6"
                    },
                    "7": {
                        "path": "/team",
                        "exact": true,
                        "parentId": "6",
                        "id": "7"
                    },
                    "8": {
                        "path": "/team/detail",
                        "name": "团队详情",
                        "hideInMenu": true,
                        "parentId": "6",
                        "id": "8"
                    },
                    "9": {
                        "path": "/personal-center",
                        "name": "个人中心",
                        "icon": "user",
                        "hideInMenu": true,
                        "parentId": "ant-design-pro-layout",
                        "id": "9"
                    },
                    "10": {
                        "path": "/user-manage",
                        "name": "用户管理",
                        "icon": "user",
                        "hideInMenu": true,
                        "parentId": "ant-design-pro-layout",
                        "id": "10"
                    },
                    "11": {
                        "path": "/subscription",
                        "name": "订阅管理",
                        "icon": "crown",
                        "hideInMenu": true,
                        "parentId": "ant-design-pro-layout",
                        "id": "11"
                    },
                    "12": {
                        "path": "/friend",
                        "name": "好友管理",
                        "icon": "userAdd",
                        "hideInMenu": true,
                        "parentId": "ant-design-pro-layout",
                        "id": "12"
                    },
                    "13": {
                        "path": "/help",
                        "name": "帮助中心",
                        "icon": "question",
                        "hideInMenu": true,
                        "parentId": "ant-design-pro-layout",
                        "id": "13"
                    },
                    "14": {
                        "path": "/",
                        "redirect": "/dashboard",
                        "parentId": "ant-design-pro-layout",
                        "id": "14"
                    },
                    "15": {
                        "path": "*",
                        "layout": false,
                        "id": "15"
                    },
                    "ant-design-pro-layout": {
                        "id": "ant-design-pro-layout",
                        "path": "/",
                        "isLayout": true
                    },
                    "umi/plugin/openapi": {
                        "path": "/umi/plugin/openapi",
                        "id": "umi/plugin/openapi"
                    }
                };
                return {
                    routes,
                    routeComponents: {
                        '1': _react.default.lazy(()=>__mako_require__.ensure2("src/.umi/core/EmptyRoute.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/.umi/core/EmptyRoute.tsx")))),
                        '2': _react.default.lazy(()=>__mako_require__.ensure2("src/pages/user/login/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/user/login/index.tsx")))),
                        '3': _react.default.lazy(()=>__mako_require__.ensure2("src/pages/user/team-select/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/user/team-select/index.tsx")))),
                        '4': _react.default.lazy(()=>__mako_require__.ensure2("src/pages/team/create/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/team/create/index.tsx")))),
                        '5': _react.default.lazy(()=>__mako_require__.ensure2("src/pages/Dashboard/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/Dashboard/index.tsx")))),
                        '6': _react.default.lazy(()=>__mako_require__.ensure2("src/.umi/core/EmptyRoute.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/.umi/core/EmptyRoute.tsx")))),
                        '7': _react.default.lazy(()=>__mako_require__.ensure2("src/pages/team/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/team/index.tsx")))),
                        '8': _react.default.lazy(()=>__mako_require__.ensure2("src/pages/team/detail/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/team/detail/index.tsx")))),
                        '9': _react.default.lazy(()=>__mako_require__.ensure2("src/pages/personal-center/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/personal-center/index.tsx")))),
                        '10': _react.default.lazy(()=>__mako_require__.ensure2("src/pages/user/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/user/index.tsx")))),
                        '11': _react.default.lazy(()=>__mako_require__.ensure2("src/pages/subscription/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/subscription/index.tsx")))),
                        '12': _react.default.lazy(()=>__mako_require__.ensure2("src/pages/friend/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/friend/index.tsx")))),
                        '13': _react.default.lazy(()=>__mako_require__.ensure2("src/pages/help/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/help/index.tsx")))),
                        '14': _react.default.lazy(()=>__mako_require__.ensure2("src/.umi/core/EmptyRoute.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/.umi/core/EmptyRoute.tsx")))),
                        '15': _react.default.lazy(()=>__mako_require__.ensure2("src/pages/404.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/404.tsx")))),
                        'ant-design-pro-layout': _react.default.lazy(()=>__mako_require__.ensure2("src/.umi/plugin-layout/Layout.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/.umi/plugin-layout/Layout.tsx")))),
                        'umi/plugin/openapi': _react.default.lazy(()=>__mako_require__.ensure2("src/.umi/plugin-openapi/openapi.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/.umi/plugin-openapi/openapi.tsx"))))
                    }
                };
            }
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/pages/team/detail/index.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
            var _services = __mako_require__("src/services/index.ts");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _TeamMemberList = _interop_require_default._(__mako_require__("src/pages/team/detail/components/TeamMemberList.tsx"));
            var _EnhancedTeamDetail = _interop_require_default._(__mako_require__("src/pages/team/detail/components/EnhancedTeamDetail.tsx"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Title, Text } = _antd.Typography;
            const { TextArea } = _antd.Input;
            const TeamDetailPage = ()=>{
                _s();
                const [loading, setLoading] = (0, _react.useState)(true);
                const [teamDetail, setTeamDetail] = (0, _react.useState)(null);
                const [useEnhancedUI, setUseEnhancedUI] = (0, _react.useState)(true);
                const { setInitialState } = (0, _max.useModel)('@@initialState');
                (0, _react.useEffect)(()=>{
                    fetchTeamDetail();
                }, []);
                const fetchTeamDetail = async ()=>{
                    try {
                        setLoading(true);
                        const detail = await _services.TeamService.getCurrentTeamDetail();
                        setTeamDetail(detail);
                    } catch (error) {
                        console.error('获取团队详情失败:', error);
                        _antd.message.error('获取团队详情失败');
                    } finally{
                        setLoading(false);
                    }
                };
                const [editModalVisible, setEditModalVisible] = (0, _react.useState)(false);
                const [updating, setUpdating] = (0, _react.useState)(false);
                const [deleting, setDeleting] = (0, _react.useState)(false);
                const [form] = _antd.Form.useForm();
                const handleEdit = ()=>{
                    if (teamDetail) {
                        form.setFieldsValue({
                            name: teamDetail.name,
                            description: teamDetail.description
                        });
                        setEditModalVisible(true);
                    }
                };
                const handleUpdate = async (values)=>{
                    try {
                        setUpdating(true);
                        const updatedTeam = await _services.TeamService.updateCurrentTeam(values);
                        setTeamDetail(updatedTeam);
                        setEditModalVisible(false);
                        _antd.message.success('团队信息更新成功');
                    } catch (error) {
                        console.error('更新团队信息失败:', error);
                    } finally{
                        setUpdating(false);
                    }
                };
                const handleDeleteTeam = ()=>{
                    _antd.Modal.confirm({
                        title: '确认删除团队',
                        content: '删除团队后，所有团队数据将无法恢复。确定要删除吗？',
                        icon: (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {}, void 0, false, {
                            fileName: "src/pages/team/detail/index.tsx",
                            lineNumber: 93,
                            columnNumber: 13
                        }, this),
                        okText: '确认删除',
                        cancelText: '取消',
                        okType: 'danger',
                        onOk: async ()=>{
                            try {
                                setDeleting(true);
                                await _services.TeamService.deleteTeam(teamDetail.id);
                                _antd.message.success('团队删除成功');
                                setInitialState((s)=>({
                                        ...s,
                                        currentTeam: undefined
                                    }));
                                _max.history.push('/user/team-select');
                            } catch (error) {
                                console.error('删除团队失败:', error);
                                _antd.message.error('删除团队失败');
                            } finally{
                                setDeleting(false);
                            }
                        }
                    });
                };
                const handleGoBack = ()=>{
                    _max.history.push('/user/team-select');
                };
                if (loading) return (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
                    children: (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            textAlign: 'center',
                            padding: '50px 0'
                        },
                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                            size: "large"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/index.tsx",
                            lineNumber: 123,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/index.tsx",
                        lineNumber: 122,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team/detail/index.tsx",
                    lineNumber: 121,
                    columnNumber: 7
                }, this);
                if (!teamDetail) return (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
                    children: (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            textAlign: 'center',
                            padding: '50px 0'
                        },
                        children: (0, _jsxdevruntime.jsxDEV)(Text, {
                            type: "secondary",
                            children: "团队信息加载失败"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/index.tsx",
                            lineNumber: 133,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/index.tsx",
                        lineNumber: 132,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team/detail/index.tsx",
                    lineNumber: 131,
                    columnNumber: 7
                }, this);
                if (useEnhancedUI) return (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
                    title: (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        align: "center",
                        children: [
                            (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "text",
                                icon: (0, _jsxdevruntime.jsxDEV)(_icons.ArrowLeftOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team/detail/index.tsx",
                                    lineNumber: 147,
                                    columnNumber: 21
                                }, void 0),
                                onClick: handleGoBack,
                                style: {
                                    color: '#666'
                                },
                                children: "返回团队选择"
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/index.tsx",
                                lineNumber: 145,
                                columnNumber: 13
                            }, void 0),
                            (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {
                                type: "vertical"
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/index.tsx",
                                lineNumber: 153,
                                columnNumber: 13
                            }, void 0),
                            (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                style: {
                                    color: '#1890ff'
                                }
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/index.tsx",
                                lineNumber: 154,
                                columnNumber: 13
                            }, void 0),
                            (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Title, {
                                level: 4,
                                style: {
                                    margin: 0,
                                    color: '#333'
                                },
                                children: [
                                    (teamDetail === null || teamDetail === void 0 ? void 0 : teamDetail.name) || '团队详情',
                                    " - 已更新"
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/detail/index.tsx",
                                lineNumber: 155,
                                columnNumber: 13
                            }, void 0),
                            (teamDetail === null || teamDetail === void 0 ? void 0 : teamDetail.isCreator) && (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                type: "secondary",
                                style: {
                                    fontSize: 14
                                },
                                children: "(管理员)"
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/index.tsx",
                                lineNumber: 159,
                                columnNumber: 15
                            }, void 0)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/team/detail/index.tsx",
                        lineNumber: 144,
                        columnNumber: 11
                    }, void 0),
                    extra: (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "text",
                            size: "small",
                            onClick: ()=>setUseEnhancedUI(false),
                            style: {
                                color: '#666'
                            },
                            children: "切换到经典版"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/index.tsx",
                            lineNumber: 167,
                            columnNumber: 13
                        }, void 0)
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/index.tsx",
                        lineNumber: 166,
                        columnNumber: 11
                    }, void 0),
                    style: {
                        background: '#f5f5f5'
                    },
                    children: (0, _jsxdevruntime.jsxDEV)(_EnhancedTeamDetail.default, {
                        teamDetail: teamDetail,
                        onRefresh: fetchTeamDetail
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/index.tsx",
                        lineNumber: 179,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team/detail/index.tsx",
                    lineNumber: 142,
                    columnNumber: 7
                }, this);
                return (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
                    title: (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        align: "center",
                        children: [
                            (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "text",
                                icon: (0, _jsxdevruntime.jsxDEV)(_icons.ArrowLeftOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team/detail/index.tsx",
                                    lineNumber: 194,
                                    columnNumber: 19
                                }, void 0),
                                onClick: handleGoBack,
                                style: {
                                    color: '#666'
                                },
                                children: "返回"
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/index.tsx",
                                lineNumber: 192,
                                columnNumber: 11
                            }, void 0),
                            (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {
                                type: "vertical"
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/index.tsx",
                                lineNumber: 200,
                                columnNumber: 11
                            }, void 0),
                            (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                style: {
                                    color: '#1890ff'
                                }
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/index.tsx",
                                lineNumber: 201,
                                columnNumber: 11
                            }, void 0),
                            (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Title, {
                                level: 4,
                                style: {
                                    margin: 0,
                                    color: '#333'
                                },
                                children: teamDetail.name
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/index.tsx",
                                lineNumber: 202,
                                columnNumber: 11
                            }, void 0),
                            teamDetail.isCreator && (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                type: "secondary",
                                style: {
                                    fontSize: 14
                                },
                                children: "(管理员)"
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/index.tsx",
                                lineNumber: 206,
                                columnNumber: 13
                            }, void 0)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/team/detail/index.tsx",
                        lineNumber: 191,
                        columnNumber: 9
                    }, void 0),
                    extra: (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        children: [
                            (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "text",
                                size: "small",
                                onClick: ()=>setUseEnhancedUI(true),
                                style: {
                                    color: '#1890ff'
                                },
                                children: "切换到增强版"
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/index.tsx",
                                lineNumber: 214,
                                columnNumber: 11
                            }, void 0),
                            teamDetail.isCreator && (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        icon: (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                            fileName: "src/pages/team/detail/index.tsx",
                                            lineNumber: 225,
                                            columnNumber: 23
                                        }, void 0),
                                        onClick: handleEdit,
                                        type: "primary",
                                        children: "编辑团队"
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/index.tsx",
                                        lineNumber: 224,
                                        columnNumber: 15
                                    }, void 0),
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        icon: (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                            fileName: "src/pages/team/detail/index.tsx",
                                            lineNumber: 232,
                                            columnNumber: 23
                                        }, void 0),
                                        onClick: handleDeleteTeam,
                                        danger: true,
                                        loading: deleting,
                                        children: "删除团队"
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/index.tsx",
                                        lineNumber: 231,
                                        columnNumber: 15
                                    }, void 0)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/detail/index.tsx",
                                lineNumber: 223,
                                columnNumber: 13
                            }, void 0)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/team/detail/index.tsx",
                        lineNumber: 213,
                        columnNumber: 9
                    }, void 0),
                    children: [
                        (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            title: "团队信息",
                            style: {
                                marginBottom: 24
                            },
                            children: (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions, {
                                column: 2,
                                children: [
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                        label: "团队名称",
                                        children: teamDetail.name
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/index.tsx",
                                        lineNumber: 246,
                                        columnNumber: 11
                                    }, this),
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                        label: "成员数量",
                                        children: [
                                            teamDetail.memberCount,
                                            " 人"
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/team/detail/index.tsx",
                                        lineNumber: 249,
                                        columnNumber: 11
                                    }, this),
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                        label: "创建时间",
                                        children: new Date(teamDetail.createdAt).toLocaleString()
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/index.tsx",
                                        lineNumber: 252,
                                        columnNumber: 11
                                    }, this),
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                        label: "最后更新",
                                        children: new Date(teamDetail.updatedAt).toLocaleString()
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/index.tsx",
                                        lineNumber: 255,
                                        columnNumber: 11
                                    }, this),
                                    teamDetail.description && (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                        label: "团队描述",
                                        span: 2,
                                        children: teamDetail.description
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/index.tsx",
                                        lineNumber: 259,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/detail/index.tsx",
                                lineNumber: 245,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/index.tsx",
                            lineNumber: 244,
                            columnNumber: 7
                        }, this),
                        (0, _jsxdevruntime.jsxDEV)(_TeamMemberList.default, {
                            teamId: teamDetail.id,
                            isCreator: teamDetail.isCreator,
                            onMemberChange: fetchTeamDetail
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/index.tsx",
                            lineNumber: 266,
                            columnNumber: 7
                        }, this),
                        (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                            title: "编辑团队信息",
                            open: editModalVisible,
                            onCancel: ()=>setEditModalVisible(false),
                            footer: null,
                            children: (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                                form: form,
                                layout: "vertical",
                                onFinish: handleUpdate,
                                children: [
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        label: "团队名称",
                                        name: "name",
                                        rules: [
                                            {
                                                required: true,
                                                message: '请输入团队名称！'
                                            },
                                            {
                                                max: 100,
                                                message: '团队名称长度不能超过100字符！'
                                            }
                                        ],
                                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                            placeholder: "请输入团队名称"
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/index.tsx",
                                            lineNumber: 292,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/index.tsx",
                                        lineNumber: 284,
                                        columnNumber: 11
                                    }, this),
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        label: "团队描述",
                                        name: "description",
                                        rules: [
                                            {
                                                max: 500,
                                                message: '团队描述长度不能超过500字符！'
                                            }
                                        ],
                                        children: (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                            placeholder: "请输入团队描述（可选）",
                                            rows: 4,
                                            showCount: true,
                                            maxLength: 500
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/index.tsx",
                                            lineNumber: 302,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/index.tsx",
                                        lineNumber: 295,
                                        columnNumber: 11
                                    }, this),
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: [
                                                (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    type: "primary",
                                                    htmlType: "submit",
                                                    loading: updating,
                                                    children: "保存"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/index.tsx",
                                                    lineNumber: 312,
                                                    columnNumber: 15
                                                }, this),
                                                (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    onClick: ()=>setEditModalVisible(false),
                                                    children: "取消"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/index.tsx",
                                                    lineNumber: 319,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team/detail/index.tsx",
                                            lineNumber: 311,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/index.tsx",
                                        lineNumber: 310,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/detail/index.tsx",
                                lineNumber: 279,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/index.tsx",
                            lineNumber: 273,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/detail/index.tsx",
                    lineNumber: 189,
                    columnNumber: 5
                }, this);
            };
            _s(TeamDetailPage, "rxmLrNpsCCk14PzVItCdmNci628=", false, function() {
                return [
                    _max.useModel,
                    _antd.Form.useForm
                ];
            });
            _c = TeamDetailPage;
            var _default = TeamDetailPage;
            var _c;
            $RefreshReg$(_c, "TeamDetailPage");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/pages/team/detail/components/EnhancedTeamDetail.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _services = __mako_require__("src/services/index.ts");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _TeamMemberList = _interop_require_default._(__mako_require__("src/pages/team/detail/components/TeamMemberList.tsx"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Title, Text, Paragraph } = _antd.Typography;
            const { TextArea } = _antd.Input;
            const EnhancedTeamDetail = ({ teamDetail, onRefresh })=>{
                _s();
                const [editModalVisible, setEditModalVisible] = (0, _react.useState)(false);
                const [updating, setUpdating] = (0, _react.useState)(false);
                const [deleting, setDeleting] = (0, _react.useState)(false);
                const [form] = _antd.Form.useForm();
                const { setInitialState } = (0, _max.useModel)('@@initialState');
                const handleGoBack = ()=>{
                    _max.history.push('/user/team-select');
                };
                const handleEdit = ()=>{
                    form.setFieldsValue({
                        name: teamDetail.name,
                        description: teamDetail.description || ''
                    });
                    setEditModalVisible(true);
                };
                const handleUpdateTeam = async (values)=>{
                    try {
                        setUpdating(true);
                        await _services.TeamService.updateCurrentTeam(values);
                        _antd.message.success('团队信息更新成功');
                        setEditModalVisible(false);
                        onRefresh();
                    } catch (error) {
                        console.error('更新团队失败:', error);
                        _antd.message.error('更新团队失败');
                    } finally{
                        setUpdating(false);
                    }
                };
                const handleDeleteTeam = ()=>{
                    _antd.Modal.confirm({
                        title: '确认删除团队',
                        content: '删除团队后，所有团队数据将无法恢复。确定要删除吗？',
                        icon: (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {}, void 0, false, {
                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                            lineNumber: 92,
                            columnNumber: 13
                        }, this),
                        okText: '确认删除',
                        cancelText: '取消',
                        okType: 'danger',
                        onOk: async ()=>{
                            try {
                                setDeleting(true);
                                await new Promise((resolve)=>setTimeout(resolve, 1000));
                                _antd.message.success('团队删除成功');
                                setInitialState((s)=>({
                                        ...s,
                                        currentTeam: undefined
                                    }));
                                _max.history.push('/user/team-select');
                            } catch (error) {
                                console.error('删除团队失败:', error);
                                _antd.message.error('删除团队失败');
                            } finally{
                                setDeleting(false);
                            }
                        }
                    });
                };
                const moreMenuItems = [
                    {
                        key: 'edit',
                        icon: (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                            lineNumber: 119,
                            columnNumber: 13
                        }, this),
                        label: '编辑团队',
                        onClick: handleEdit
                    },
                    {
                        key: 'share',
                        icon: (0, _jsxdevruntime.jsxDEV)(_icons.ShareAltOutlined, {}, void 0, false, {
                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                            lineNumber: 125,
                            columnNumber: 13
                        }, this),
                        label: '分享团队',
                        onClick: ()=>{
                            _antd.message.info('分享功能开发中');
                        }
                    },
                    {
                        key: 'settings',
                        icon: (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {}, void 0, false, {
                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                            lineNumber: 134,
                            columnNumber: 13
                        }, this),
                        label: '团队设置',
                        onClick: ()=>{
                            _antd.message.info('团队设置功能开发中');
                        }
                    },
                    {
                        type: 'divider'
                    },
                    {
                        key: 'delete',
                        icon: (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                            lineNumber: 146,
                            columnNumber: 13
                        }, this),
                        label: '删除团队',
                        danger: true,
                        onClick: handleDeleteTeam
                    }
                ];
                const formatDate = (dateString)=>{
                    return new Date(dateString).toLocaleDateString('zh-CN', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                    });
                };
                const getTeamStatusColor = ()=>{
                    const memberCount = teamDetail.memberCount;
                    if (memberCount >= 10) return '#52c41a';
                    if (memberCount >= 5) return '#faad14';
                    return '#1890ff';
                };
                const getTeamStatusText = ()=>{
                    const memberCount = teamDetail.memberCount;
                    if (memberCount >= 10) return '活跃团队';
                    if (memberCount >= 5) return '正常团队';
                    return '小型团队';
                };
                return (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        padding: '0 24px'
                    },
                    children: [
                        (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            style: {
                                marginBottom: 24,
                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                                border: 'none',
                                borderRadius: 16
                            },
                            styles: {
                                body: {
                                    padding: '32px'
                                }
                            },
                            children: [
                                (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                    align: "middle",
                                    justify: "space-between",
                                    style: {
                                        marginBottom: 24
                                    },
                                    children: [
                                        (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                            children: (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                type: "text",
                                                icon: (0, _jsxdevruntime.jsxDEV)(_icons.ArrowLeftOutlined, {}, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                    lineNumber: 192,
                                                    columnNumber: 21
                                                }, void 0),
                                                onClick: handleGoBack,
                                                style: {
                                                    color: 'rgba(255, 255, 255, 0.8)',
                                                    fontSize: 16
                                                },
                                                children: "返回团队选择"
                                            }, void 0, false, {
                                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                lineNumber: 190,
                                                columnNumber: 13
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 189,
                                            columnNumber: 11
                                        }, this),
                                        (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                            children: (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                children: teamDetail.isCreator && (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                                                    children: [
                                                        (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                            icon: (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                                lineNumber: 207,
                                                                columnNumber: 27
                                                            }, void 0),
                                                            onClick: handleEdit,
                                                            style: {
                                                                background: 'rgba(255, 255, 255, 0.1)',
                                                                borderColor: 'rgba(255, 255, 255, 0.2)',
                                                                color: 'white'
                                                            },
                                                            children: "编辑团队"
                                                        }, void 0, false, {
                                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                            lineNumber: 206,
                                                            columnNumber: 19
                                                        }, this),
                                                        (0, _jsxdevruntime.jsxDEV)(_antd.Dropdown, {
                                                            menu: {
                                                                items: moreMenuItems
                                                            },
                                                            trigger: [
                                                                'click'
                                                            ],
                                                            children: (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                                icon: (0, _jsxdevruntime.jsxDEV)(_icons.MoreOutlined, {}, void 0, false, {
                                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                                    lineNumber: 222,
                                                                    columnNumber: 29
                                                                }, void 0),
                                                                loading: deleting,
                                                                style: {
                                                                    background: 'rgba(255, 255, 255, 0.1)',
                                                                    borderColor: 'rgba(255, 255, 255, 0.2)',
                                                                    color: 'white'
                                                                },
                                                                children: "更多操作"
                                                            }, void 0, false, {
                                                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                                lineNumber: 221,
                                                                columnNumber: 21
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                            lineNumber: 217,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true)
                                            }, void 0, false, {
                                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                lineNumber: 203,
                                                columnNumber: 13
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 202,
                                            columnNumber: 11
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                    lineNumber: 188,
                                    columnNumber: 9
                                }, this),
                                (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                    align: "middle",
                                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            size: "large",
                                            align: "center",
                                            children: [
                                                (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                                    size: 80,
                                                    icon: (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                        lineNumber: 245,
                                                        columnNumber: 23
                                                    }, void 0),
                                                    style: {
                                                        backgroundColor: 'rgba(255, 255, 255, 0.2)',
                                                        color: 'white',
                                                        fontSize: 32
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                    lineNumber: 243,
                                                    columnNumber: 15
                                                }, this),
                                                (0, _jsxdevruntime.jsxDEV)("div", {
                                                    children: [
                                                        (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                            align: "center",
                                                            style: {
                                                                marginBottom: 8
                                                            },
                                                            children: [
                                                                (0, _jsxdevruntime.jsxDEV)(Title, {
                                                                    level: 2,
                                                                    style: {
                                                                        color: 'white',
                                                                        margin: 0
                                                                    },
                                                                    children: teamDetail.name
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                                    lineNumber: 254,
                                                                    columnNumber: 19
                                                                }, this),
                                                                teamDetail.isCreator && (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                                    icon: (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                                                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                                        lineNumber: 259,
                                                                        columnNumber: 29
                                                                    }, void 0),
                                                                    color: "gold",
                                                                    style: {
                                                                        fontSize: 12
                                                                    },
                                                                    children: "管理员"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                                    lineNumber: 258,
                                                                    columnNumber: 21
                                                                }, this),
                                                                (0, _jsxdevruntime.jsxDEV)(_antd.Badge, {
                                                                    color: getTeamStatusColor(),
                                                                    text: (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        style: {
                                                                            color: 'rgba(255, 255, 255, 0.8)'
                                                                        },
                                                                        children: getTeamStatusText()
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                                        lineNumber: 269,
                                                                        columnNumber: 23
                                                                    }, void 0)
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                                    lineNumber: 266,
                                                                    columnNumber: 19
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                            lineNumber: 253,
                                                            columnNumber: 17
                                                        }, this),
                                                        (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                                            style: {
                                                                color: 'rgba(255, 255, 255, 0.8)',
                                                                margin: 0,
                                                                maxWidth: 400
                                                            },
                                                            ellipsis: {
                                                                rows: 2
                                                            },
                                                            children: teamDetail.description || '这个团队还没有描述'
                                                        }, void 0, false, {
                                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                            lineNumber: 275,
                                                            columnNumber: 17
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                    lineNumber: 252,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 242,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 241,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                    lineNumber: 240,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                            lineNumber: 178,
                            columnNumber: 7
                        }, this),
                        (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                            gutter: [
                                16,
                                16
                            ],
                            style: {
                                marginBottom: 24
                            },
                            children: [
                                (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 24,
                                    sm: 12,
                                    md: 6,
                                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                            title: "团队成员",
                                            value: teamDetail.memberCount,
                                            suffix: "人",
                                            prefix: (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {
                                                style: {
                                                    color: '#1890ff'
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                lineNumber: 299,
                                                columnNumber: 23
                                            }, void 0),
                                            valueStyle: {
                                                color: '#1890ff'
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 295,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 294,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                    lineNumber: 293,
                                    columnNumber: 9
                                }, this),
                                (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 24,
                                    sm: 12,
                                    md: 6,
                                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                            title: "创建时间",
                                            value: formatDate(teamDetail.createdAt),
                                            prefix: (0, _jsxdevruntime.jsxDEV)(_icons.CalendarOutlined, {
                                                style: {
                                                    color: '#52c41a'
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                lineNumber: 309,
                                                columnNumber: 23
                                            }, void 0),
                                            valueStyle: {
                                                color: '#52c41a',
                                                fontSize: 16
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 306,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 305,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                    lineNumber: 304,
                                    columnNumber: 9
                                }, this),
                                (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 24,
                                    sm: 12,
                                    md: 6,
                                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                            title: "最后活动",
                                            value: formatDate(teamDetail.updatedAt),
                                            prefix: (0, _jsxdevruntime.jsxDEV)(_icons.ClockCircleOutlined, {
                                                style: {
                                                    color: '#faad14'
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                lineNumber: 319,
                                                columnNumber: 23
                                            }, void 0),
                                            valueStyle: {
                                                color: '#faad14',
                                                fontSize: 16
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 316,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 315,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                    lineNumber: 314,
                                    columnNumber: 9
                                }, this),
                                (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 24,
                                    sm: 12,
                                    md: 6,
                                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                textAlign: 'center'
                                            },
                                            children: [
                                                (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    type: "secondary",
                                                    style: {
                                                        fontSize: 14
                                                    },
                                                    children: "团队活跃度"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                    lineNumber: 327,
                                                    columnNumber: 15
                                                }, this),
                                                (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        marginTop: 8
                                                    },
                                                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Progress, {
                                                        type: "circle",
                                                        size: 60,
                                                        percent: Math.min(teamDetail.memberCount * 10, 100),
                                                        strokeColor: getTeamStatusColor(),
                                                        format: ()=>(0, _jsxdevruntime.jsxDEV)(Text, {
                                                                style: {
                                                                    fontSize: 12,
                                                                    color: getTeamStatusColor()
                                                                },
                                                                children: teamDetail.memberCount >= 10 ? '高' : teamDetail.memberCount >= 5 ? '中' : '低'
                                                            }, void 0, false, {
                                                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                                lineNumber: 335,
                                                                columnNumber: 21
                                                            }, void 0)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                        lineNumber: 329,
                                                        columnNumber: 17
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                    lineNumber: 328,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 326,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 325,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                    lineNumber: 324,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                            lineNumber: 292,
                            columnNumber: 7
                        }, this),
                        (0, _jsxdevruntime.jsxDEV)(_TeamMemberList.default, {
                            teamId: teamDetail.id,
                            isCreator: teamDetail.isCreator,
                            onMemberChange: onRefresh
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                            lineNumber: 348,
                            columnNumber: 7
                        }, this),
                        (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                            title: "编辑团队信息",
                            open: editModalVisible,
                            onCancel: ()=>setEditModalVisible(false),
                            footer: null,
                            width: 600,
                            children: (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                                form: form,
                                layout: "vertical",
                                onFinish: handleUpdateTeam,
                                children: [
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        label: "团队名称",
                                        name: "name",
                                        rules: [
                                            {
                                                required: true,
                                                message: '请输入团队名称'
                                            },
                                            {
                                                max: 50,
                                                message: '团队名称不能超过50个字符'
                                            }
                                        ],
                                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                            placeholder: "请输入团队名称"
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 375,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 367,
                                        columnNumber: 11
                                    }, this),
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        label: "团队描述",
                                        name: "description",
                                        rules: [
                                            {
                                                max: 200,
                                                message: '团队描述不能超过200个字符'
                                            }
                                        ],
                                        children: (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                            rows: 4,
                                            placeholder: "请输入团队描述（可选）",
                                            showCount: true,
                                            maxLength: 200
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 384,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 377,
                                        columnNumber: 11
                                    }, this),
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        style: {
                                            marginBottom: 0,
                                            textAlign: 'right'
                                        },
                                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: [
                                                (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    onClick: ()=>setEditModalVisible(false),
                                                    children: "取消"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                    lineNumber: 393,
                                                    columnNumber: 15
                                                }, this),
                                                (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    type: "primary",
                                                    htmlType: "submit",
                                                    loading: updating,
                                                    children: "保存"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                                    lineNumber: 396,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                            lineNumber: 392,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                        lineNumber: 391,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                                lineNumber: 362,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                            lineNumber: 355,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/detail/components/EnhancedTeamDetail.tsx",
                    lineNumber: 176,
                    columnNumber: 5
                }, this);
            };
            _s(EnhancedTeamDetail, "UalB0vhOvdT9JI/tG4E1rE8Z8wk=", false, function() {
                return [
                    _antd.Form.useForm,
                    _max.useModel
                ];
            });
            _c = EnhancedTeamDetail;
            var _default = EnhancedTeamDetail;
            var _c;
            $RefreshReg$(_c, "EnhancedTeamDetail");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '14474540932536290694';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/.umi/plugin-openapi/openapi.tsx": [
            "vendors",
            "src/.umi/plugin-openapi/openapi.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/friend/index.tsx": [
            "p__friend__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "common",
            "p__subscription__index"
        ],
        "src/pages/team/create/index.tsx": [
            "p__team__create__index"
        ],
        "src/pages/team/detail/index.tsx": [
            "common",
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "common",
            "p__user__index"
        ],
        "src/pages/user/login/index.tsx": [
            "p__user__login__index"
        ],
        "src/pages/user/team-select/index.tsx": [
            "src/pages/user/team-select/index.tsx"
        ]
    });
    ;
});

//# sourceMappingURL=p__team__detail__index-async.5060615520183295693.hot-update.js.map