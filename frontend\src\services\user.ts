/**
 * 用户管理相关 API 服务
 */

import { apiRequest } from '@/utils/request';
import type {
  UserProfileResponse,
  UpdateUserProfileRequest,
} from '@/types/api';

/**
 * 用户服务类
 */
export class UserService {
  /**
   * 获取当前用户资料
   */
  static async getUserProfile(): Promise<UserProfileResponse> {
    const response = await apiRequest.get<UserProfileResponse>('/users/profile');
    return response.data;
  }

  /**
   * 更新用户资料
   */
  static async updateUserProfile(data: UpdateUserProfileRequest): Promise<UserProfileResponse> {
    const response = await apiRequest.put<UserProfileResponse>('/users/profile', data);
    return response.data;
  }

  /**
   * 修改密码
   */
  static async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    const data: UpdateUserProfileRequest = {
      currentPassword,
      newPassword,
    };
    
    const response = await apiRequest.put<void>('/users/profile', data);
    return response.data;
  }

  /**
   * 更新用户名
   */
  static async updateUserName(name: string): Promise<UserProfileResponse> {
    const data: UpdateUserProfileRequest = {
      name,
    };
    
    const response = await apiRequest.put<UserProfileResponse>('/users/profile', data);
    return response.data;
  }

  /**
   * 验证当前密码
   */
  static async validateCurrentPassword(password: string): Promise<boolean> {
    try {
      const response = await apiRequest.post<boolean>('/users/validate-password', { password });
      return response.data;
    } catch {
      return false;
    }
  }

  /**
   * 获取用户统计信息
   */
  static async getUserStats(): Promise<{
    totalTeams: number;
    createdTeams: number;
    joinedTeams: number;
    lastLoginTime: string;
  }> {
    // 这里可能需要后端提供专门的统计接口
    // 暂时返回模拟数据
    return {
      totalTeams: 0,
      createdTeams: 0,
      joinedTeams: 0,
      lastLoginTime: new Date().toISOString(),
    };
  }

  /**
   * 检查邮箱是否已被使用
   */
  static async checkEmailAvailable(email: string): Promise<boolean> {
    try {
      // 这里可能需要后端提供专门的检查接口
      // 暂时返回 true
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 获取用户活动日志
   */
  static async getUserActivityLog(params?: {
    startDate?: string;
    endDate?: string;
    limit?: number;
  }): Promise<Array<{
    id: number;
    action: string;
    description: string;
    timestamp: string;
    ipAddress?: string;
    userAgent?: string;
  }>> {
    // 这里需要后端提供活动日志接口
    // 暂时返回空数组
    return [];
  }

  /**
   * 导出用户数据
   */
  static async exportUserData(): Promise<Blob> {
    // 这里需要后端提供数据导出接口
    const response = await apiRequest.get('/users/export');
    return response as unknown as Blob;
  }

  /**
   * 删除用户账户
   */
  static async deleteAccount(password: string): Promise<void> {
    const response = await apiRequest.delete<void>('/users/account', {
      password,
    });
    return response.data;
  }


}

// 导出默认实例
export default UserService;
