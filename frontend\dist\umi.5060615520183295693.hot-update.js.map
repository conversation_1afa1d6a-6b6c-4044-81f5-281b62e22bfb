{"version": 3, "sources": ["umi.5060615520183295693.hot-update.js", "src/.umi/core/route.tsx", "src/pages/team/detail/index.tsx", "src/pages/team/detail/components/EnhancedTeamDetail.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='14474540932536290694';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/friend/index.tsx\":[\"p__friend__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"common\",\"p__subscription__index\"],\"src/pages/team/create/index.tsx\":[\"p__team__create__index\"],\"src/pages/team/detail/index.tsx\":[\"common\",\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"p__team__index\"],\"src/pages/user/index.tsx\":[\"common\",\"p__user__index\"],\"src/pages/user/login/index.tsx\":[\"p__user__login__index\"],\"src/pages/user/team-select/index.tsx\":[\"src/pages/user/team-select/index.tsx\"]});;\r\n  },\r\n);\r\n", "// @ts-nocheck\n// This file is generated by <PERSON><PERSON> automatically\n// DO NOT CHANGE IT MANUALLY!\nimport React from 'react';\n\nexport async function getRoutes() {\n  const routes = {\"1\":{\"path\":\"/user\",\"layout\":false,\"id\":\"1\"},\"2\":{\"name\":\"login\",\"path\":\"/user/login\",\"parentId\":\"1\",\"id\":\"2\"},\"3\":{\"name\":\"team-select\",\"path\":\"/user/team-select\",\"parentId\":\"1\",\"id\":\"3\"},\"4\":{\"path\":\"/team/create\",\"layout\":false,\"id\":\"4\"},\"5\":{\"path\":\"/dashboard\",\"name\":\"仪表盘\",\"icon\":\"dashboard\",\"parentId\":\"ant-design-pro-layout\",\"id\":\"5\"},\"6\":{\"path\":\"/team\",\"name\":\"团队管理\",\"icon\":\"team\",\"hideInMenu\":true,\"parentId\":\"ant-design-pro-layout\",\"id\":\"6\"},\"7\":{\"path\":\"/team\",\"exact\":true,\"parentId\":\"6\",\"id\":\"7\"},\"8\":{\"path\":\"/team/detail\",\"name\":\"团队详情\",\"hideInMenu\":true,\"parentId\":\"6\",\"id\":\"8\"},\"9\":{\"path\":\"/personal-center\",\"name\":\"个人中心\",\"icon\":\"user\",\"hideInMenu\":true,\"parentId\":\"ant-design-pro-layout\",\"id\":\"9\"},\"10\":{\"path\":\"/user-manage\",\"name\":\"用户管理\",\"icon\":\"user\",\"hideInMenu\":true,\"parentId\":\"ant-design-pro-layout\",\"id\":\"10\"},\"11\":{\"path\":\"/subscription\",\"name\":\"订阅管理\",\"icon\":\"crown\",\"hideInMenu\":true,\"parentId\":\"ant-design-pro-layout\",\"id\":\"11\"},\"12\":{\"path\":\"/friend\",\"name\":\"好友管理\",\"icon\":\"userAdd\",\"hideInMenu\":true,\"parentId\":\"ant-design-pro-layout\",\"id\":\"12\"},\"13\":{\"path\":\"/help\",\"name\":\"帮助中心\",\"icon\":\"question\",\"hideInMenu\":true,\"parentId\":\"ant-design-pro-layout\",\"id\":\"13\"},\"14\":{\"path\":\"/\",\"redirect\":\"/dashboard\",\"parentId\":\"ant-design-pro-layout\",\"id\":\"14\"},\"15\":{\"path\":\"*\",\"layout\":false,\"id\":\"15\"},\"ant-design-pro-layout\":{\"id\":\"ant-design-pro-layout\",\"path\":\"/\",\"isLayout\":true},\"umi/plugin/openapi\":{\"path\":\"/umi/plugin/openapi\",\"id\":\"umi/plugin/openapi\"}} as const;\n  return {\n    routes,\n    routeComponents: {\n'1': React.lazy(() => import('./EmptyRoute')),\n'2': React.lazy(() => import(/* webpackChunkName: \"p__user__login__index\" */'@/pages/user/login/index.tsx')),\n'3': React.lazy(() => import(/* webpackChunkName: \"p__user__team-select__index\" */'@/pages/user/team-select/index.tsx')),\n'4': React.lazy(() => import(/* webpackChunkName: \"p__team__create__index\" */'@/pages/team/create/index.tsx')),\n'5': React.lazy(() => import(/* webpackChunkName: \"p__Dashboard__index\" */'@/pages/Dashboard/index.tsx')),\n'6': React.lazy(() => import('./EmptyRoute')),\n'7': React.lazy(() => import(/* webpackChunkName: \"p__team__index\" */'@/pages/team/index.tsx')),\n'8': React.lazy(() => import(/* webpackChunkName: \"p__team__detail__index\" */'@/pages/team/detail/index.tsx')),\n'9': React.lazy(() => import(/* webpackChunkName: \"p__personal-center__index\" */'@/pages/personal-center/index.tsx')),\n'10': React.lazy(() => import(/* webpackChunkName: \"p__user__index\" */'@/pages/user/index.tsx')),\n'11': React.lazy(() => import(/* webpackChunkName: \"p__subscription__index\" */'@/pages/subscription/index.tsx')),\n'12': React.lazy(() => import(/* webpackChunkName: \"p__friend__index\" */'@/pages/friend/index.tsx')),\n'13': React.lazy(() => import(/* webpackChunkName: \"p__help__index\" */'@/pages/help/index.tsx')),\n'14': React.lazy(() => import('./EmptyRoute')),\n'15': React.lazy(() => import(/* webpackChunkName: \"p__404\" */'@/pages/404.tsx')),\n'ant-design-pro-layout': React.lazy(() => import(/* webpackChunkName: \"umi__plugin-layout__Layout\" */'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-layout/Layout.tsx')),\n'umi/plugin/openapi': React.lazy(() => import(/* webpackChunkName: \"umi__plugin-openapi__openapi\" */'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-openapi/openapi.tsx')),\n},\n  };\n}\n", "/**\n * 团队详情页面\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Descriptions,\n  Button,\n  Space,\n  Typography,\n  message,\n  Modal,\n  Form,\n  Input,\n  Spin,\n  Divider\n} from 'antd';\nimport {\n  TeamOutlined,\n  EditOutlined,\n  ArrowLeftOutlined,\n  DeleteOutlined,\n  ExclamationCircleOutlined\n} from '@ant-design/icons';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { TeamService } from '@/services';\nimport type { TeamDetailResponse, UpdateTeamRequest } from '@/types/api';\nimport { history, useModel } from '@umijs/max';\nimport TeamMemberList from './components/TeamMemberList';\nimport EnhancedTeamDetail from './components/EnhancedTeamDetail';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\n\nconst TeamDetailPage: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [teamDetail, setTeamDetail] = useState<TeamDetailResponse | null>(null);\n  const [useEnhancedUI, setUseEnhancedUI] = useState(true); // 控制是否使用新UI\n  const { setInitialState } = useModel('@@initialState');\n\n  useEffect(() => {\n    fetchTeamDetail();\n  }, []);\n\n  const fetchTeamDetail = async () => {\n    try {\n      setLoading(true);\n      const detail = await TeamService.getCurrentTeamDetail();\n      setTeamDetail(detail);\n    } catch (error) {\n      console.error('获取团队详情失败:', error);\n      message.error('获取团队详情失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 经典版UI的处理方法\n  const [editModalVisible, setEditModalVisible] = useState(false);\n  const [updating, setUpdating] = useState(false);\n  const [deleting, setDeleting] = useState(false);\n  const [form] = Form.useForm();\n\n  const handleEdit = () => {\n    if (teamDetail) {\n      form.setFieldsValue({\n        name: teamDetail.name,\n        description: teamDetail.description,\n      });\n      setEditModalVisible(true);\n    }\n  };\n\n  const handleUpdate = async (values: UpdateTeamRequest) => {\n    try {\n      setUpdating(true);\n      const updatedTeam = await TeamService.updateCurrentTeam(values);\n      setTeamDetail(updatedTeam);\n      setEditModalVisible(false);\n      message.success('团队信息更新成功');\n    } catch (error) {\n      console.error('更新团队信息失败:', error);\n    } finally {\n      setUpdating(false);\n    }\n  };\n\n  const handleDeleteTeam = () => {\n    Modal.confirm({\n      title: '确认删除团队',\n      content: '删除团队后，所有团队数据将无法恢复。确定要删除吗？',\n      icon: <ExclamationCircleOutlined />,\n      okText: '确认删除',\n      cancelText: '取消',\n      okType: 'danger',\n      onOk: async () => {\n        try {\n          setDeleting(true);\n          await TeamService.deleteTeam(teamDetail!.id);\n          message.success('团队删除成功');\n          // 更新全局状态，清除当前团队\n          setInitialState((s) => ({ ...s, currentTeam: undefined }));\n          history.push('/user/team-select');\n        } catch (error) {\n          console.error('删除团队失败:', error);\n          message.error('删除团队失败');\n        } finally {\n          setDeleting(false);\n        }\n      },\n    });\n  };\n\n  const handleGoBack = () => {\n    history.push('/user/team-select');\n  };\n\n  if (loading) {\n    return (\n      <PageContainer>\n        <div style={{ textAlign: 'center', padding: '50px 0' }}>\n          <Spin size=\"large\" />\n        </div>\n      </PageContainer>\n    );\n  }\n\n  if (!teamDetail) {\n    return (\n      <PageContainer>\n        <div style={{ textAlign: 'center', padding: '50px 0' }}>\n          <Text type=\"secondary\">团队信息加载失败</Text>\n        </div>\n      </PageContainer>\n    );\n  }\n\n  // 使用新的增强版UI\n  if (useEnhancedUI) {\n    return (\n      <PageContainer\n        title={\n          <Space align=\"center\">\n            <Button\n              type=\"text\"\n              icon={<ArrowLeftOutlined />}\n              onClick={handleGoBack}\n              style={{ color: '#666' }}\n            >\n              返回团队选择\n            </Button>\n            <Divider type=\"vertical\" />\n            <TeamOutlined style={{ color: '#1890ff' }} />\n            <Typography.Title level={4} style={{ margin: 0, color: '#333' }}>\n              {teamDetail?.name || '团队详情'} - 已更新\n            </Typography.Title>\n            {teamDetail?.isCreator && (\n              <Typography.Text type=\"secondary\" style={{ fontSize: 14 }}>\n                (管理员)\n              </Typography.Text>\n            )}\n          </Space>\n        }\n        extra={\n          <Space>\n            <Button\n              type=\"text\"\n              size=\"small\"\n              onClick={() => setUseEnhancedUI(false)}\n              style={{ color: '#666' }}\n            >\n              切换到经典版\n            </Button>\n          </Space>\n        }\n        style={{ background: '#f5f5f5' }}\n      >\n        <EnhancedTeamDetail\n          teamDetail={teamDetail}\n          onRefresh={fetchTeamDetail}\n        />\n      </PageContainer>\n    );\n  }\n\n  // 原有的经典UI\n  return (\n    <PageContainer\n      title={\n        <Space align=\"center\">\n          <Button\n            type=\"text\"\n            icon={<ArrowLeftOutlined />}\n            onClick={handleGoBack}\n            style={{ color: '#666' }}\n          >\n            返回\n          </Button>\n          <Divider type=\"vertical\" />\n          <TeamOutlined style={{ color: '#1890ff' }} />\n          <Typography.Title level={4} style={{ margin: 0, color: '#333' }}>\n            {teamDetail.name}\n          </Typography.Title>\n          {teamDetail.isCreator && (\n            <Typography.Text type=\"secondary\" style={{ fontSize: 14 }}>\n              (管理员)\n            </Typography.Text>\n          )}\n        </Space>\n      }\n      extra={\n        <Space>\n          <Button\n            type=\"text\"\n            size=\"small\"\n            onClick={() => setUseEnhancedUI(true)}\n            style={{ color: '#1890ff' }}\n          >\n            切换到增强版\n          </Button>\n          {teamDetail.isCreator && (\n            <Space>\n              <Button\n                icon={<EditOutlined />}\n                onClick={handleEdit}\n                type=\"primary\"\n              >\n                编辑团队\n              </Button>\n              <Button\n                icon={<DeleteOutlined />}\n                onClick={handleDeleteTeam}\n                danger\n                loading={deleting}\n              >\n                删除团队\n              </Button>\n            </Space>\n          )}\n        </Space>\n      }\n    >\n      <Card title=\"团队信息\" style={{ marginBottom: 24 }}>\n        <Descriptions column={2}>\n          <Descriptions.Item label=\"团队名称\">\n            {teamDetail.name}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"成员数量\">\n            {teamDetail.memberCount} 人\n          </Descriptions.Item>\n          <Descriptions.Item label=\"创建时间\">\n            {new Date(teamDetail.createdAt).toLocaleString()}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"最后更新\">\n            {new Date(teamDetail.updatedAt).toLocaleString()}\n          </Descriptions.Item>\n          {teamDetail.description && (\n            <Descriptions.Item label=\"团队描述\" span={2}>\n              {teamDetail.description}\n            </Descriptions.Item>\n          )}\n        </Descriptions>\n      </Card>\n\n      <TeamMemberList \n        teamId={teamDetail.id}\n        isCreator={teamDetail.isCreator}\n        onMemberChange={fetchTeamDetail}\n      />\n\n      {/* 编辑团队模态框 */}\n      <Modal\n        title=\"编辑团队信息\"\n        open={editModalVisible}\n        onCancel={() => setEditModalVisible(false)}\n        footer={null}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleUpdate}\n        >\n          <Form.Item\n            label=\"团队名称\"\n            name=\"name\"\n            rules={[\n              { required: true, message: '请输入团队名称！' },\n              { max: 100, message: '团队名称长度不能超过100字符！' },\n            ]}\n          >\n            <Input placeholder=\"请输入团队名称\" />\n          </Form.Item>\n\n          <Form.Item\n            label=\"团队描述\"\n            name=\"description\"\n            rules={[\n              { max: 500, message: '团队描述长度不能超过500字符！' },\n            ]}\n          >\n            <TextArea\n              placeholder=\"请输入团队描述（可选）\"\n              rows={4}\n              showCount\n              maxLength={500}\n            />\n          </Form.Item>\n\n          <Form.Item>\n            <Space>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                loading={updating}\n              >\n                保存\n              </Button>\n              <Button onClick={() => setEditModalVisible(false)}>\n                取消\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n    </PageContainer>\n  );\n};\n\nexport default TeamDetailPage;\n", "/**\n * 增强版团队详情组件 - 全新UI设计\n */\n\nimport React, { useState } from 'react';\nimport {\n  Card,\n  Row,\n  Col,\n  Statistic,\n  Button,\n  Space,\n  Typography,\n  Avatar,\n  Tag,\n  Progress,\n  Badge,\n  Dropdown,\n  Modal,\n  Form,\n  Input,\n  message\n} from 'antd';\nimport {\n  TeamOutlined,\n  UserOutlined,\n  CalendarOutlined,\n  EditOutlined,\n  MoreOutlined,\n  CrownOutlined,\n  ClockCircleOutlined,\n  SettingOutlined,\n  ShareAltOutlined,\n  DeleteOutlined,\n  ExclamationCircleOutlined,\n  ArrowLeftOutlined\n} from '@ant-design/icons';\nimport { TeamService } from '@/services';\nimport type { TeamDetailResponse, UpdateTeamRequest } from '@/types/api';\nimport { history, useModel } from '@umijs/max';\nimport TeamMemberList from './TeamMemberList';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { TextArea } = Input;\n\ninterface EnhancedTeamDetailProps {\n  teamDetail: TeamDetailResponse;\n  onRefresh: () => void;\n}\n\nconst EnhancedTeamDetail: React.FC<EnhancedTeamDetailProps> = ({\n  teamDetail,\n  onRefresh\n}) => {\n  const [editModalVisible, setEditModalVisible] = useState(false);\n  const [updating, setUpdating] = useState(false);\n  const [deleting, setDeleting] = useState(false);\n  const [form] = Form.useForm();\n  const { setInitialState } = useModel('@@initialState');\n\n  const handleGoBack = () => {\n    history.push('/user/team-select');\n  };\n\n  const handleEdit = () => {\n    form.setFieldsValue({\n      name: teamDetail.name,\n      description: teamDetail.description || '',\n    });\n    setEditModalVisible(true);\n  };\n\n  const handleUpdateTeam = async (values: UpdateTeamRequest) => {\n    try {\n      setUpdating(true);\n      await TeamService.updateCurrentTeam(values);\n      message.success('团队信息更新成功');\n      setEditModalVisible(false);\n      onRefresh();\n    } catch (error) {\n      console.error('更新团队失败:', error);\n      message.error('更新团队失败');\n    } finally {\n      setUpdating(false);\n    }\n  };\n\n  const handleDeleteTeam = () => {\n    Modal.confirm({\n      title: '确认删除团队',\n      content: '删除团队后，所有团队数据将无法恢复。确定要删除吗？',\n      icon: <ExclamationCircleOutlined />,\n      okText: '确认删除',\n      cancelText: '取消',\n      okType: 'danger',\n      onOk: async () => {\n        try {\n          setDeleting(true);\n          // 注意：这里需要后端提供删除团队的API\n          // 暂时使用模拟删除\n          await new Promise(resolve => setTimeout(resolve, 1000));\n          message.success('团队删除成功');\n          // 更新全局状态，清除当前团队\n          setInitialState((s) => ({ ...s, currentTeam: undefined }));\n          history.push('/user/team-select');\n        } catch (error) {\n          console.error('删除团队失败:', error);\n          message.error('删除团队失败');\n        } finally {\n          setDeleting(false);\n        }\n      },\n    });\n  };\n\n  const moreMenuItems = [\n    {\n      key: 'edit',\n      icon: <EditOutlined />,\n      label: '编辑团队',\n      onClick: handleEdit\n    },\n    {\n      key: 'share',\n      icon: <ShareAltOutlined />,\n      label: '分享团队',\n      onClick: () => {\n        // TODO: 实现分享功能\n        message.info('分享功能开发中');\n      }\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: '团队设置',\n      onClick: () => {\n        // TODO: 实现团队设置\n        message.info('团队设置功能开发中');\n      }\n    },\n    {\n      type: 'divider' as const\n    },\n    {\n      key: 'delete',\n      icon: <DeleteOutlined />,\n      label: '删除团队',\n      danger: true,\n      onClick: handleDeleteTeam\n    }\n  ];\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('zh-CN', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  const getTeamStatusColor = () => {\n    const memberCount = teamDetail.memberCount;\n    if (memberCount >= 10) return '#52c41a'; // 绿色 - 活跃\n    if (memberCount >= 5) return '#faad14'; // 橙色 - 正常\n    return '#1890ff'; // 蓝色 - 小团队\n  };\n\n  const getTeamStatusText = () => {\n    const memberCount = teamDetail.memberCount;\n    if (memberCount >= 10) return '活跃团队';\n    if (memberCount >= 5) return '正常团队';\n    return '小型团队';\n  };\n\n  return (\n    <div style={{ padding: '0 24px' }}>\n      {/* 团队头部信息卡片 */}\n      <Card\n        style={{\n          marginBottom: 24,\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          border: 'none',\n          borderRadius: 16\n        }}\n        styles={{ body: { padding: '32px' } }}\n      >\n        {/* 返回按钮和操作按钮行 */}\n        <Row align=\"middle\" justify=\"space-between\" style={{ marginBottom: 24 }}>\n          <Col>\n            <Button\n              type=\"text\"\n              icon={<ArrowLeftOutlined />}\n              onClick={handleGoBack}\n              style={{\n                color: 'rgba(255, 255, 255, 0.8)',\n                fontSize: 16\n              }}\n            >\n              返回团队选择\n            </Button>\n          </Col>\n          <Col>\n            <Space>\n              {teamDetail.isCreator && (\n                <>\n                  <Button\n                    icon={<EditOutlined />}\n                    onClick={handleEdit}\n                    style={{\n                      background: 'rgba(255, 255, 255, 0.1)',\n                      borderColor: 'rgba(255, 255, 255, 0.2)',\n                      color: 'white'\n                    }}\n                  >\n                    编辑团队\n                  </Button>\n                  <Dropdown\n                    menu={{ items: moreMenuItems }}\n                    trigger={['click']}\n                  >\n                    <Button\n                      icon={<MoreOutlined />}\n                      loading={deleting}\n                      style={{\n                        background: 'rgba(255, 255, 255, 0.1)',\n                        borderColor: 'rgba(255, 255, 255, 0.2)',\n                        color: 'white'\n                      }}\n                    >\n                      更多操作\n                    </Button>\n                  </Dropdown>\n                </>\n              )}\n            </Space>\n          </Col>\n        </Row>\n\n        {/* 团队信息展示 */}\n        <Row align=\"middle\">\n          <Col>\n            <Space size=\"large\" align=\"center\">\n              <Avatar\n                size={80}\n                icon={<TeamOutlined />}\n                style={{\n                  backgroundColor: 'rgba(255, 255, 255, 0.2)',\n                  color: 'white',\n                  fontSize: 32\n                }}\n              />\n              <div>\n                <Space align=\"center\" style={{ marginBottom: 8 }}>\n                  <Title level={2} style={{ color: 'white', margin: 0 }}>\n                    {teamDetail.name}\n                  </Title>\n                  {teamDetail.isCreator && (\n                    <Tag\n                      icon={<CrownOutlined />}\n                      color=\"gold\"\n                      style={{ fontSize: 12 }}\n                    >\n                      管理员\n                    </Tag>\n                  )}\n                  <Badge\n                    color={getTeamStatusColor()}\n                    text={\n                      <Text style={{ color: 'rgba(255, 255, 255, 0.8)' }}>\n                        {getTeamStatusText()}\n                      </Text>\n                    }\n                  />\n                </Space>\n                <Paragraph\n                  style={{\n                    color: 'rgba(255, 255, 255, 0.8)',\n                    margin: 0,\n                    maxWidth: 400\n                  }}\n                  ellipsis={{ rows: 2 }}\n                >\n                  {teamDetail.description || '这个团队还没有描述'}\n                </Paragraph>\n              </div>\n            </Space>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 团队统计信息 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"团队成员\"\n              value={teamDetail.memberCount}\n              suffix=\"人\"\n              prefix={<UserOutlined style={{ color: '#1890ff' }} />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"创建时间\"\n              value={formatDate(teamDetail.createdAt)}\n              prefix={<CalendarOutlined style={{ color: '#52c41a' }} />}\n              valueStyle={{ color: '#52c41a', fontSize: 16 }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"最后活动\"\n              value={formatDate(teamDetail.updatedAt)}\n              prefix={<ClockCircleOutlined style={{ color: '#faad14' }} />}\n              valueStyle={{ color: '#faad14', fontSize: 16 }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <div style={{ textAlign: 'center' }}>\n              <Text type=\"secondary\" style={{ fontSize: 14 }}>团队活跃度</Text>\n              <div style={{ marginTop: 8 }}>\n                <Progress\n                  type=\"circle\"\n                  size={60}\n                  percent={Math.min(teamDetail.memberCount * 10, 100)}\n                  strokeColor={getTeamStatusColor()}\n                  format={() => (\n                    <Text style={{ fontSize: 12, color: getTeamStatusColor() }}>\n                      {teamDetail.memberCount >= 10 ? '高' : \n                       teamDetail.memberCount >= 5 ? '中' : '低'}\n                    </Text>\n                  )}\n                />\n              </div>\n            </div>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 团队成员列表 */}\n      <TeamMemberList\n        teamId={teamDetail.id}\n        isCreator={teamDetail.isCreator}\n        onMemberChange={onRefresh}\n      />\n\n      {/* 编辑团队模态框 */}\n      <Modal\n        title=\"编辑团队信息\"\n        open={editModalVisible}\n        onCancel={() => setEditModalVisible(false)}\n        footer={null}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleUpdateTeam}\n        >\n          <Form.Item\n            label=\"团队名称\"\n            name=\"name\"\n            rules={[\n              { required: true, message: '请输入团队名称' },\n              { max: 50, message: '团队名称不能超过50个字符' }\n            ]}\n          >\n            <Input placeholder=\"请输入团队名称\" />\n          </Form.Item>\n          <Form.Item\n            label=\"团队描述\"\n            name=\"description\"\n            rules={[\n              { max: 200, message: '团队描述不能超过200个字符' }\n            ]}\n          >\n            <TextArea\n              rows={4}\n              placeholder=\"请输入团队描述（可选）\"\n              showCount\n              maxLength={200}\n            />\n          </Form.Item>\n          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>\n            <Space>\n              <Button onClick={() => setEditModalVisible(false)}>\n                取消\n              </Button>\n              <Button type=\"primary\" htmlType=\"submit\" loading={updating}>\n                保存\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default EnhancedTeamDetail;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;;wCCES;;;2BAAA;;;;;;mFAFJ;;;;;;;;;YAEX,eAAe;gBACpB,MAAM,SAAS;oBAAC,KAAI;wBAAC,QAAO;wBAAQ,UAAS;wBAAM,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAQ,QAAO;wBAAc,YAAW;wBAAI,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAc,QAAO;wBAAoB,YAAW;wBAAI,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAe,UAAS;wBAAM,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAa,QAAO;wBAAM,QAAO;wBAAY,YAAW;wBAAwB,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAQ,QAAO;wBAAO,QAAO;wBAAO,cAAa;wBAAK,YAAW;wBAAwB,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAQ,SAAQ;wBAAK,YAAW;wBAAI,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAe,QAAO;wBAAO,cAAa;wBAAK,YAAW;wBAAI,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAmB,QAAO;wBAAO,QAAO;wBAAO,cAAa;wBAAK,YAAW;wBAAwB,MAAK;oBAAG;oBAAE,MAAK;wBAAC,QAAO;wBAAe,QAAO;wBAAO,QAAO;wBAAO,cAAa;wBAAK,YAAW;wBAAwB,MAAK;oBAAI;oBAAE,MAAK;wBAAC,QAAO;wBAAgB,QAAO;wBAAO,QAAO;wBAAQ,cAAa;wBAAK,YAAW;wBAAwB,MAAK;oBAAI;oBAAE,MAAK;wBAAC,QAAO;wBAAU,QAAO;wBAAO,QAAO;wBAAU,cAAa;wBAAK,YAAW;wBAAwB,MAAK;oBAAI;oBAAE,MAAK;wBAAC,QAAO;wBAAQ,QAAO;wBAAO,QAAO;wBAAW,cAAa;wBAAK,YAAW;wBAAwB,MAAK;oBAAI;oBAAE,MAAK;wBAAC,QAAO;wBAAI,YAAW;wBAAa,YAAW;wBAAwB,MAAK;oBAAI;oBAAE,MAAK;wBAAC,QAAO;wBAAI,UAAS;wBAAM,MAAK;oBAAI;oBAAE,yBAAwB;wBAAC,MAAK;wBAAwB,QAAO;wBAAI,YAAW;oBAAI;oBAAE,sBAAqB;wBAAC,QAAO;wBAAsB,MAAK;oBAAoB;gBAAC;gBAC59C,OAAO;oBACL;oBACA,iBAAiB;wBACrB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,oBAAM,cAAK,CAAC,IAAI,CAAC,IAAM;wBACvB,oBAAM,cAAK,CAAC,IAAI,CAAC,IAAM;wBACvB,oBAAM,cAAK,CAAC,IAAI,CAAC,IAAM;wBACvB,oBAAM,cAAK,CAAC,IAAI,CAAC,IAAM;wBACvB,oBAAM,cAAK,CAAC,IAAI,CAAC,IAAM;wBACvB,oBAAM,cAAK,CAAC,IAAI,CAAC,IAAM;wBACvB,uCAAyB,cAAK,CAAC,IAAI,CAAC,IAAM;wBAC1C,oCAAsB,cAAK,CAAC,IAAI,CAAC,IAAM;oBACvC;gBACE;YACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCC6SA;;;2BAAA;;;;;;;oFAtU2C;yCAapC;0CAOA;kDACuB;6CACF;wCAEM;4FACP;gGACI;;;;;;;;;;YAE/B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;YAClC,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAK;YAE1B,MAAM,iBAA2B;;gBAC/B,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;gBACvC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAA4B;gBACxE,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAC,OAAO,YAAY;gBACtE,MAAM,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;gBAErC,IAAA,gBAAS,EAAC;oBACR;gBACF,GAAG,EAAE;gBAEL,MAAM,kBAAkB;oBACtB,IAAI;wBACF,WAAW;wBACX,MAAM,SAAS,MAAM,qBAAW,CAAC,oBAAoB;wBACrD,cAAc;oBAChB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,aAAO,CAAC,KAAK,CAAC;oBAChB,SAAU;wBACR,WAAW;oBACb;gBACF;gBAEA,aAAa;gBACb,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;gBACzD,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;gBACzC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;gBACzC,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;gBAE3B,MAAM,aAAa;oBACjB,IAAI,YAAY;wBACd,KAAK,cAAc,CAAC;4BAClB,MAAM,WAAW,IAAI;4BACrB,aAAa,WAAW,WAAW;wBACrC;wBACA,oBAAoB;oBACtB;gBACF;gBAEA,MAAM,eAAe,OAAO;oBAC1B,IAAI;wBACF,YAAY;wBACZ,MAAM,cAAc,MAAM,qBAAW,CAAC,iBAAiB,CAAC;wBACxD,cAAc;wBACd,oBAAoB;wBACpB,aAAO,CAAC,OAAO,CAAC;oBAClB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;oBAC7B,SAAU;wBACR,YAAY;oBACd;gBACF;gBAEA,MAAM,mBAAmB;oBACvB,WAAK,CAAC,OAAO,CAAC;wBACZ,OAAO;wBACP,SAAS;wBACT,oBAAM,2BAAC,gCAAyB;;;;;wBAChC,QAAQ;wBACR,YAAY;wBACZ,QAAQ;wBACR,MAAM;4BACJ,IAAI;gCACF,YAAY;gCACZ,MAAM,qBAAW,CAAC,UAAU,CAAC,WAAY,EAAE;gCAC3C,aAAO,CAAC,OAAO,CAAC;gCAChB,gBAAgB;gCAChB,gBAAgB,CAAC,IAAO,CAAA;wCAAE,GAAG,CAAC;wCAAE,aAAa;oCAAU,CAAA;gCACvD,YAAO,CAAC,IAAI,CAAC;4BACf,EAAE,OAAO,OAAO;gCACd,QAAQ,KAAK,CAAC,WAAW;gCACzB,aAAO,CAAC,KAAK,CAAC;4BAChB,SAAU;gCACR,YAAY;4BACd;wBACF;oBACF;gBACF;gBAEA,MAAM,eAAe;oBACnB,YAAO,CAAC,IAAI,CAAC;gBACf;gBAEA,IAAI,SACF,qBACE,2BAAC,4BAAa;8BACZ,cAAA,2BAAC;wBAAI,OAAO;4BAAE,WAAW;4BAAU,SAAS;wBAAS;kCACnD,cAAA,2BAAC,UAAI;4BAAC,MAAK;;;;;;;;;;;;;;;;gBAMnB,IAAI,CAAC,YACH,qBACE,2BAAC,4BAAa;8BACZ,cAAA,2BAAC;wBAAI,OAAO;4BAAE,WAAW;4BAAU,SAAS;wBAAS;kCACnD,cAAA,2BAAC;4BAAK,MAAK;sCAAY;;;;;;;;;;;;;;;;gBAM/B,YAAY;gBACZ,IAAI,eACF,qBACE,2BAAC,4BAAa;oBACZ,qBACE,2BAAC,WAAK;wBAAC,OAAM;;0CACX,2BAAC,YAAM;gCACL,MAAK;gCACL,oBAAM,2BAAC,wBAAiB;;;;;gCACxB,SAAS;gCACT,OAAO;oCAAE,OAAO;gCAAO;0CACxB;;;;;;0CAGD,2BAAC,aAAO;gCAAC,MAAK;;;;;;0CACd,2BAAC,mBAAY;gCAAC,OAAO;oCAAE,OAAO;gCAAU;;;;;;0CACxC,2BAAC,gBAAU,CAAC,KAAK;gCAAC,OAAO;gCAAG,OAAO;oCAAE,QAAQ;oCAAG,OAAO;gCAAO;;oCAC3D,CAAA,uBAAA,iCAAA,WAAY,IAAI,KAAI;oCAAO;;;;;;;4BAE7B,CAAA,uBAAA,iCAAA,WAAY,SAAS,mBACpB,2BAAC,gBAAU,CAAC,IAAI;gCAAC,MAAK;gCAAY,OAAO;oCAAE,UAAU;gCAAG;0CAAG;;;;;;;;;;;;oBAMjE,qBACE,2BAAC,WAAK;kCACJ,cAAA,2BAAC,YAAM;4BACL,MAAK;4BACL,MAAK;4BACL,SAAS,IAAM,iBAAiB;4BAChC,OAAO;gCAAE,OAAO;4BAAO;sCACxB;;;;;;;;;;;oBAKL,OAAO;wBAAE,YAAY;oBAAU;8BAE/B,cAAA,2BAAC,2BAAkB;wBACjB,YAAY;wBACZ,WAAW;;;;;;;;;;;gBAMnB,UAAU;gBACV,qBACE,2BAAC,4BAAa;oBACZ,qBACE,2BAAC,WAAK;wBAAC,OAAM;;0CACX,2BAAC,YAAM;gCACL,MAAK;gCACL,oBAAM,2BAAC,wBAAiB;;;;;gCACxB,SAAS;gCACT,OAAO;oCAAE,OAAO;gCAAO;0CACxB;;;;;;0CAGD,2BAAC,aAAO;gCAAC,MAAK;;;;;;0CACd,2BAAC,mBAAY;gCAAC,OAAO;oCAAE,OAAO;gCAAU;;;;;;0CACxC,2BAAC,gBAAU,CAAC,KAAK;gCAAC,OAAO;gCAAG,OAAO;oCAAE,QAAQ;oCAAG,OAAO;gCAAO;0CAC3D,WAAW,IAAI;;;;;;4BAEjB,WAAW,SAAS,kBACnB,2BAAC,gBAAU,CAAC,IAAI;gCAAC,MAAK;gCAAY,OAAO;oCAAE,UAAU;gCAAG;0CAAG;;;;;;;;;;;;oBAMjE,qBACE,2BAAC,WAAK;;0CACJ,2BAAC,YAAM;gCACL,MAAK;gCACL,MAAK;gCACL,SAAS,IAAM,iBAAiB;gCAChC,OAAO;oCAAE,OAAO;gCAAU;0CAC3B;;;;;;4BAGA,WAAW,SAAS,kBACnB,2BAAC,WAAK;;kDACJ,2BAAC,YAAM;wCACL,oBAAM,2BAAC,mBAAY;;;;;wCACnB,SAAS;wCACT,MAAK;kDACN;;;;;;kDAGD,2BAAC,YAAM;wCACL,oBAAM,2BAAC,qBAAc;;;;;wCACrB,SAAS;wCACT,MAAM;wCACN,SAAS;kDACV;;;;;;;;;;;;;;;;;;;sCAQT,2BAAC,UAAI;4BAAC,OAAM;4BAAO,OAAO;gCAAE,cAAc;4BAAG;sCAC3C,cAAA,2BAAC,kBAAY;gCAAC,QAAQ;;kDACpB,2BAAC,kBAAY,CAAC,IAAI;wCAAC,OAAM;kDACtB,WAAW,IAAI;;;;;;kDAElB,2BAAC,kBAAY,CAAC,IAAI;wCAAC,OAAM;;4CACtB,WAAW,WAAW;4CAAC;;;;;;;kDAE1B,2BAAC,kBAAY,CAAC,IAAI;wCAAC,OAAM;kDACtB,IAAI,KAAK,WAAW,SAAS,EAAE,cAAc;;;;;;kDAEhD,2BAAC,kBAAY,CAAC,IAAI;wCAAC,OAAM;kDACtB,IAAI,KAAK,WAAW,SAAS,EAAE,cAAc;;;;;;oCAE/C,WAAW,WAAW,kBACrB,2BAAC,kBAAY,CAAC,IAAI;wCAAC,OAAM;wCAAO,MAAM;kDACnC,WAAW,WAAW;;;;;;;;;;;;;;;;;sCAM/B,2BAAC,uBAAc;4BACb,QAAQ,WAAW,EAAE;4BACrB,WAAW,WAAW,SAAS;4BAC/B,gBAAgB;;;;;;sCAIlB,2BAAC,WAAK;4BACJ,OAAM;4BACN,MAAM;4BACN,UAAU,IAAM,oBAAoB;4BACpC,QAAQ;sCAER,cAAA,2BAAC,UAAI;gCACH,MAAM;gCACN,QAAO;gCACP,UAAU;;kDAEV,2BAAC,UAAI,CAAC,IAAI;wCACR,OAAM;wCACN,MAAK;wCACL,OAAO;4CACL;gDAAE,UAAU;gDAAM,SAAS;4CAAW;4CACtC;gDAAE,KAAK;gDAAK,SAAS;4CAAmB;yCACzC;kDAED,cAAA,2BAAC,WAAK;4CAAC,aAAY;;;;;;;;;;;kDAGrB,2BAAC,UAAI,CAAC,IAAI;wCACR,OAAM;wCACN,MAAK;wCACL,OAAO;4CACL;gDAAE,KAAK;gDAAK,SAAS;4CAAmB;yCACzC;kDAED,cAAA,2BAAC;4CACC,aAAY;4CACZ,MAAM;4CACN,SAAS;4CACT,WAAW;;;;;;;;;;;kDAIf,2BAAC,UAAI,CAAC,IAAI;kDACR,cAAA,2BAAC,WAAK;;8DACJ,2BAAC,YAAM;oDACL,MAAK;oDACL,UAAS;oDACT,SAAS;8DACV;;;;;;8DAGD,2BAAC,YAAM;oDAAC,SAAS,IAAM,oBAAoB;8DAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUjE;eArSM;;oBAIwB,aAAQ;oBAuBrB,UAAI,CAAC;;;iBA3BhB;gBAuSN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCC4Ef;;;2BAAA;;;;;;;oFAlZgC;yCAkBzB;0CAcA;6CACqB;wCAEM;4FACP;;;;;;;;;;YAE3B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,gBAAU;YAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAK;YAO1B,MAAM,qBAAwD,CAAC,EAC7D,UAAU,EACV,SAAS,EACV;;gBACC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;gBACzD,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;gBACzC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;gBACzC,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;gBAC3B,MAAM,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;gBAErC,MAAM,eAAe;oBACnB,YAAO,CAAC,IAAI,CAAC;gBACf;gBAEA,MAAM,aAAa;oBACjB,KAAK,cAAc,CAAC;wBAClB,MAAM,WAAW,IAAI;wBACrB,aAAa,WAAW,WAAW,IAAI;oBACzC;oBACA,oBAAoB;gBACtB;gBAEA,MAAM,mBAAmB,OAAO;oBAC9B,IAAI;wBACF,YAAY;wBACZ,MAAM,qBAAW,CAAC,iBAAiB,CAAC;wBACpC,aAAO,CAAC,OAAO,CAAC;wBAChB,oBAAoB;wBACpB;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;wBACzB,aAAO,CAAC,KAAK,CAAC;oBAChB,SAAU;wBACR,YAAY;oBACd;gBACF;gBAEA,MAAM,mBAAmB;oBACvB,WAAK,CAAC,OAAO,CAAC;wBACZ,OAAO;wBACP,SAAS;wBACT,oBAAM,2BAAC,gCAAyB;;;;;wBAChC,QAAQ;wBACR,YAAY;wBACZ,QAAQ;wBACR,MAAM;4BACJ,IAAI;gCACF,YAAY;gCACZ,sBAAsB;gCACtB,WAAW;gCACX,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gCACjD,aAAO,CAAC,OAAO,CAAC;gCAChB,gBAAgB;gCAChB,gBAAgB,CAAC,IAAO,CAAA;wCAAE,GAAG,CAAC;wCAAE,aAAa;oCAAU,CAAA;gCACvD,YAAO,CAAC,IAAI,CAAC;4BACf,EAAE,OAAO,OAAO;gCACd,QAAQ,KAAK,CAAC,WAAW;gCACzB,aAAO,CAAC,KAAK,CAAC;4BAChB,SAAU;gCACR,YAAY;4BACd;wBACF;oBACF;gBACF;gBAEA,MAAM,gBAAgB;oBACpB;wBACE,KAAK;wBACL,oBAAM,2BAAC,mBAAY;;;;;wBACnB,OAAO;wBACP,SAAS;oBACX;oBACA;wBACE,KAAK;wBACL,oBAAM,2BAAC,uBAAgB;;;;;wBACvB,OAAO;wBACP,SAAS;4BACP,eAAe;4BACf,aAAO,CAAC,IAAI,CAAC;wBACf;oBACF;oBACA;wBACE,KAAK;wBACL,oBAAM,2BAAC,sBAAe;;;;;wBACtB,OAAO;wBACP,SAAS;4BACP,eAAe;4BACf,aAAO,CAAC,IAAI,CAAC;wBACf;oBACF;oBACA;wBACE,MAAM;oBACR;oBACA;wBACE,KAAK;wBACL,oBAAM,2BAAC,qBAAc;;;;;wBACrB,OAAO;wBACP,QAAQ;wBACR,SAAS;oBACX;iBACD;gBAED,MAAM,aAAa,CAAC;oBAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;wBACtD,MAAM;wBACN,OAAO;wBACP,KAAK;oBACP;gBACF;gBAEA,MAAM,qBAAqB;oBACzB,MAAM,cAAc,WAAW,WAAW;oBAC1C,IAAI,eAAe,IAAI,OAAO,WAAW,UAAU;oBACnD,IAAI,eAAe,GAAG,OAAO,WAAW,UAAU;oBAClD,OAAO,WAAW,WAAW;gBAC/B;gBAEA,MAAM,oBAAoB;oBACxB,MAAM,cAAc,WAAW,WAAW;oBAC1C,IAAI,eAAe,IAAI,OAAO;oBAC9B,IAAI,eAAe,GAAG,OAAO;oBAC7B,OAAO;gBACT;gBAEA,qBACE,2BAAC;oBAAI,OAAO;wBAAE,SAAS;oBAAS;;sCAE9B,2BAAC,UAAI;4BACH,OAAO;gCACL,cAAc;gCACd,YAAY;gCACZ,QAAQ;gCACR,cAAc;4BAChB;4BACA,QAAQ;gCAAE,MAAM;oCAAE,SAAS;gCAAO;4BAAE;;8CAGpC,2BAAC,SAAG;oCAAC,OAAM;oCAAS,SAAQ;oCAAgB,OAAO;wCAAE,cAAc;oCAAG;;sDACpE,2BAAC,SAAG;sDACF,cAAA,2BAAC,YAAM;gDACL,MAAK;gDACL,oBAAM,2BAAC,wBAAiB;;;;;gDACxB,SAAS;gDACT,OAAO;oDACL,OAAO;oDACP,UAAU;gDACZ;0DACD;;;;;;;;;;;sDAIH,2BAAC,SAAG;sDACF,cAAA,2BAAC,WAAK;0DACH,WAAW,SAAS,kBACnB;;sEACE,2BAAC,YAAM;4DACL,oBAAM,2BAAC,mBAAY;;;;;4DACnB,SAAS;4DACT,OAAO;gEACL,YAAY;gEACZ,aAAa;gEACb,OAAO;4DACT;sEACD;;;;;;sEAGD,2BAAC,cAAQ;4DACP,MAAM;gEAAE,OAAO;4DAAc;4DAC7B,SAAS;gEAAC;6DAAQ;sEAElB,cAAA,2BAAC,YAAM;gEACL,oBAAM,2BAAC,mBAAY;;;;;gEACnB,SAAS;gEACT,OAAO;oEACL,YAAY;oEACZ,aAAa;oEACb,OAAO;gEACT;0EACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAWb,2BAAC,SAAG;oCAAC,OAAM;8CACT,cAAA,2BAAC,SAAG;kDACF,cAAA,2BAAC,WAAK;4CAAC,MAAK;4CAAQ,OAAM;;8DACxB,2BAAC,YAAM;oDACL,MAAM;oDACN,oBAAM,2BAAC,mBAAY;;;;;oDACnB,OAAO;wDACL,iBAAiB;wDACjB,OAAO;wDACP,UAAU;oDACZ;;;;;;8DAEF,2BAAC;;sEACC,2BAAC,WAAK;4DAAC,OAAM;4DAAS,OAAO;gEAAE,cAAc;4DAAE;;8EAC7C,2BAAC;oEAAM,OAAO;oEAAG,OAAO;wEAAE,OAAO;wEAAS,QAAQ;oEAAE;8EACjD,WAAW,IAAI;;;;;;gEAEjB,WAAW,SAAS,kBACnB,2BAAC,SAAG;oEACF,oBAAM,2BAAC,oBAAa;;;;;oEACpB,OAAM;oEACN,OAAO;wEAAE,UAAU;oEAAG;8EACvB;;;;;;8EAIH,2BAAC,WAAK;oEACJ,OAAO;oEACP,oBACE,2BAAC;wEAAK,OAAO;4EAAE,OAAO;wEAA2B;kFAC9C;;;;;;;;;;;;;;;;;sEAKT,2BAAC;4DACC,OAAO;gEACL,OAAO;gEACP,QAAQ;gEACR,UAAU;4DACZ;4DACA,UAAU;gEAAE,MAAM;4DAAE;sEAEnB,WAAW,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASvC,2BAAC,SAAG;4BAAC,QAAQ;gCAAC;gCAAI;6BAAG;4BAAE,OAAO;gCAAE,cAAc;4BAAG;;8CAC/C,2BAAC,SAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAI,IAAI;8CACvB,cAAA,2BAAC,UAAI;kDACH,cAAA,2BAAC,eAAS;4CACR,OAAM;4CACN,OAAO,WAAW,WAAW;4CAC7B,QAAO;4CACP,sBAAQ,2BAAC,mBAAY;gDAAC,OAAO;oDAAE,OAAO;gDAAU;;;;;;4CAChD,YAAY;gDAAE,OAAO;4CAAU;;;;;;;;;;;;;;;;8CAIrC,2BAAC,SAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAI,IAAI;8CACvB,cAAA,2BAAC,UAAI;kDACH,cAAA,2BAAC,eAAS;4CACR,OAAM;4CACN,OAAO,WAAW,WAAW,SAAS;4CACtC,sBAAQ,2BAAC,uBAAgB;gDAAC,OAAO;oDAAE,OAAO;gDAAU;;;;;;4CACpD,YAAY;gDAAE,OAAO;gDAAW,UAAU;4CAAG;;;;;;;;;;;;;;;;8CAInD,2BAAC,SAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAI,IAAI;8CACvB,cAAA,2BAAC,UAAI;kDACH,cAAA,2BAAC,eAAS;4CACR,OAAM;4CACN,OAAO,WAAW,WAAW,SAAS;4CACtC,sBAAQ,2BAAC,0BAAmB;gDAAC,OAAO;oDAAE,OAAO;gDAAU;;;;;;4CACvD,YAAY;gDAAE,OAAO;gDAAW,UAAU;4CAAG;;;;;;;;;;;;;;;;8CAInD,2BAAC,SAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAI,IAAI;8CACvB,cAAA,2BAAC,UAAI;kDACH,cAAA,2BAAC;4CAAI,OAAO;gDAAE,WAAW;4CAAS;;8DAChC,2BAAC;oDAAK,MAAK;oDAAY,OAAO;wDAAE,UAAU;oDAAG;8DAAG;;;;;;8DAChD,2BAAC;oDAAI,OAAO;wDAAE,WAAW;oDAAE;8DACzB,cAAA,2BAAC,cAAQ;wDACP,MAAK;wDACL,MAAM;wDACN,SAAS,KAAK,GAAG,CAAC,WAAW,WAAW,GAAG,IAAI;wDAC/C,aAAa;wDACb,QAAQ,kBACN,2BAAC;gEAAK,OAAO;oEAAE,UAAU;oEAAI,OAAO;gEAAqB;0EACtD,WAAW,WAAW,IAAI,KAAK,MAC/B,WAAW,WAAW,IAAI,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAWrD,2BAAC,uBAAc;4BACb,QAAQ,WAAW,EAAE;4BACrB,WAAW,WAAW,SAAS;4BAC/B,gBAAgB;;;;;;sCAIlB,2BAAC,WAAK;4BACJ,OAAM;4BACN,MAAM;4BACN,UAAU,IAAM,oBAAoB;4BACpC,QAAQ;4BACR,OAAO;sCAEP,cAAA,2BAAC,UAAI;gCACH,MAAM;gCACN,QAAO;gCACP,UAAU;;kDAEV,2BAAC,UAAI,CAAC,IAAI;wCACR,OAAM;wCACN,MAAK;wCACL,OAAO;4CACL;gDAAE,UAAU;gDAAM,SAAS;4CAAU;4CACrC;gDAAE,KAAK;gDAAI,SAAS;4CAAgB;yCACrC;kDAED,cAAA,2BAAC,WAAK;4CAAC,aAAY;;;;;;;;;;;kDAErB,2BAAC,UAAI,CAAC,IAAI;wCACR,OAAM;wCACN,MAAK;wCACL,OAAO;4CACL;gDAAE,KAAK;gDAAK,SAAS;4CAAiB;yCACvC;kDAED,cAAA,2BAAC;4CACC,MAAM;4CACN,aAAY;4CACZ,SAAS;4CACT,WAAW;;;;;;;;;;;kDAGf,2BAAC,UAAI,CAAC,IAAI;wCAAC,OAAO;4CAAE,cAAc;4CAAG,WAAW;wCAAQ;kDACtD,cAAA,2BAAC,WAAK;;8DACJ,2BAAC,YAAM;oDAAC,SAAS,IAAM,oBAAoB;8DAAQ;;;;;;8DAGnD,2BAAC,YAAM;oDAAC,MAAK;oDAAU,UAAS;oDAAS,SAAS;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAS1E;eAlWM;;oBAOW,UAAI,CAAC;oBACQ,aAAQ;;;iBARhC;gBAoWN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IHnZD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,8BAA6B;YAAC;SAAmB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;YAAS;SAAyB;QAAC,mCAAkC;YAAC;SAAyB;QAAC,mCAAkC;YAAC;YAAS;SAAyB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,kCAAiC;YAAC;SAAwB;QAAC,wCAAuC;YAAC;SAAuC;IAAA;;AAC38B"}