/**
 * 用户资料内容组件
 */

import React, { useState, useEffect } from 'react';
import { 
  Form, 
  Input, 
  Button, 
  Space, 
  Typography, 
  message, 
  Divider,
  Avatar,
  Upload,
  Modal
} from 'antd';
import { 
  UserOutlined, 
  EditOutlined, 
  LockOutlined,
  MailOutlined,
  SaveOutlined,
  UploadOutlined
} from '@ant-design/icons';
import { UserService } from '@/services';
import type { UserProfileResponse, UpdateUserProfileRequest } from '@/types/api';

const { Title, Text } = Typography;

const UserProfileContent: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [editing, setEditing] = useState(false);
  const [userProfile, setUserProfile] = useState<UserProfileResponse | null>(null);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [passwordForm] = Form.useForm();

  useEffect(() => {
    fetchUserProfile();
  }, []);

  const fetchUserProfile = async () => {
    try {
      setLoading(true);
      const profile = await UserService.getUserProfile();
      setUserProfile(profile);
      form.setFieldsValue({
        username: profile.username,
        email: profile.email,
        phone: profile.phone,
        realName: profile.realName,
      });
    } catch (error) {
      console.error('获取用户资料失败:', error);
      message.error('获取用户资料失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveProfile = async (values: any) => {
    try {
      setSaving(true);
      const updateData: UpdateUserProfileRequest = {
        email: values.email,
        phone: values.phone,
        realName: values.realName,
      };
      
      const updatedProfile = await UserService.updateUserProfile(updateData);
      setUserProfile(updatedProfile);
      setEditing(false);
      message.success('个人资料更新成功');
    } catch (error) {
      console.error('更新个人资料失败:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleChangePassword = async (values: any) => {
    try {
      await UserService.changePassword({
        oldPassword: values.oldPassword,
        newPassword: values.newPassword,
      });
      setPasswordModalVisible(false);
      passwordForm.resetFields();
      message.success('密码修改成功');
    } catch (error) {
      console.error('修改密码失败:', error);
    }
  };

  const handleCancel = () => {
    setEditing(false);
    if (userProfile) {
      form.setFieldsValue({
        username: userProfile.username,
        email: userProfile.email,
        phone: userProfile.phone,
        realName: userProfile.realName,
      });
    }
  };

  if (loading || !userProfile) {
    return <div>加载中...</div>;
  }

  return (
    <div>
      <div style={{ display: 'flex', alignItems: 'flex-start', gap: 24 }}>
        {/* 头像部分 */}
        <div style={{ textAlign: 'center' }}>
          <Avatar size={120} icon={<UserOutlined />} />
          <div style={{ marginTop: 16 }}>
            <Upload
              showUploadList={false}
              beforeUpload={() => {
                message.info('头像上传功能暂未实现');
                return false;
              }}
            >
              <Button icon={<UploadOutlined />} size="small">
                更换头像
              </Button>
            </Upload>
          </div>
        </div>

        {/* 表单部分 */}
        <div style={{ flex: 1 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
            <Title level={4} style={{ margin: 0 }}>
              <UserOutlined /> 基本信息
            </Title>
            {!editing && (
              <Button
                type="primary"
                icon={<EditOutlined />}
                onClick={() => setEditing(true)}
              >
                编辑资料
              </Button>
            )}
          </div>

          <Form
            form={form}
            layout="vertical"
            onFinish={handleSaveProfile}
            disabled={!editing}
          >
            <Form.Item
              label="用户名"
              name="username"
            >
              <Input 
                prefix={<UserOutlined />} 
                disabled 
                placeholder="用户名不可修改"
              />
            </Form.Item>

            <Form.Item
              label="邮箱地址"
              name="email"
              rules={[
                { required: true, message: '请输入邮箱地址' },
                { type: 'email', message: '请输入有效的邮箱地址' }
              ]}
            >
              <Input 
                prefix={<MailOutlined />} 
                placeholder="请输入邮箱地址"
              />
            </Form.Item>

            <Form.Item
              label="手机号码"
              name="phone"
              rules={[
                { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' }
              ]}
            >
              <Input placeholder="请输入手机号码" />
            </Form.Item>

            <Form.Item
              label="真实姓名"
              name="realName"
              rules={[
                { max: 20, message: '真实姓名不能超过20个字符' }
              ]}
            >
              <Input placeholder="请输入真实姓名" />
            </Form.Item>

            {editing && (
              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={saving}
                    icon={<SaveOutlined />}
                  >
                    保存修改
                  </Button>
                  <Button onClick={handleCancel}>
                    取消
                  </Button>
                </Space>
              </Form.Item>
            )}
          </Form>
        </div>
      </div>

      <Divider />

      {/* 安全设置 */}
      <div>
        <Title level={4} style={{ marginBottom: 16 }}>
          <LockOutlined /> 安全设置
        </Title>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '16px 0' }}>
          <div>
            <Text strong>登录密码</Text>
            <br />
            <Text type="secondary">定期更换密码可以提高账户安全性</Text>
          </div>
          <Button
            onClick={() => setPasswordModalVisible(true)}
          >
            修改密码
          </Button>
        </div>
      </div>

      {/* 修改密码模态框 */}
      <Modal
        title="修改密码"
        open={passwordModalVisible}
        onCancel={() => {
          setPasswordModalVisible(false);
          passwordForm.resetFields();
        }}
        footer={null}
      >
        <Form
          form={passwordForm}
          layout="vertical"
          onFinish={handleChangePassword}
        >
          <Form.Item
            label="当前密码"
            name="oldPassword"
            rules={[{ required: true, message: '请输入当前密码' }]}
          >
            <Input.Password placeholder="请输入当前密码" />
          </Form.Item>

          <Form.Item
            label="新密码"
            name="newPassword"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 6, message: '密码长度至少6位' }
            ]}
          >
            <Input.Password placeholder="请输入新密码" />
          </Form.Item>

          <Form.Item
            label="确认新密码"
            name="confirmPassword"
            dependencies={['newPassword']}
            rules={[
              { required: true, message: '请确认新密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('newPassword') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password placeholder="请再次输入新密码" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setPasswordModalVisible(false);
                passwordForm.resetFields();
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                确认修改
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default UserProfileContent;
