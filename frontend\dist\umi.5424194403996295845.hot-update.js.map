{"version": 3, "sources": ["umi.5424194403996295845.hot-update.js", "src/.umi/core/route.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='10484809924179591538';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/friend/index.tsx\":[\"p__friend__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"common\",\"p__subscription__index\"],\"src/pages/team/create/index.tsx\":[\"p__team__create__index\"],\"src/pages/team/index.tsx\":[\"p__team__index\"],\"src/pages/user/index.tsx\":[\"common\",\"p__user__index\"],\"src/pages/user/login/index.tsx\":[\"p__user__login__index\"],\"src/pages/user/team-select/index.tsx\":[\"src/pages/user/team-select/index.tsx\"]});;\r\n  },\r\n);\r\n", "// @ts-nocheck\n// This file is generated by <PERSON><PERSON> automatically\n// DO NOT CHANGE IT MANUALLY!\nimport React from 'react';\n\nexport async function getRoutes() {\n  const routes = {\"1\":{\"path\":\"/user\",\"layout\":false,\"id\":\"1\"},\"2\":{\"name\":\"login\",\"path\":\"/user/login\",\"parentId\":\"1\",\"id\":\"2\"},\"3\":{\"name\":\"team-select\",\"path\":\"/user/team-select\",\"parentId\":\"1\",\"id\":\"3\"},\"4\":{\"path\":\"/team/create\",\"layout\":false,\"id\":\"4\"},\"5\":{\"path\":\"/dashboard\",\"name\":\"仪表盘\",\"icon\":\"dashboard\",\"parentId\":\"ant-design-pro-layout\",\"id\":\"5\"},\"6\":{\"path\":\"/team\",\"name\":\"团队管理\",\"icon\":\"team\",\"hideInMenu\":true,\"parentId\":\"ant-design-pro-layout\",\"id\":\"6\"},\"7\":{\"path\":\"/personal-center\",\"name\":\"个人中心\",\"icon\":\"user\",\"hideInMenu\":true,\"parentId\":\"ant-design-pro-layout\",\"id\":\"7\"},\"8\":{\"path\":\"/user-manage\",\"name\":\"用户管理\",\"icon\":\"user\",\"hideInMenu\":true,\"parentId\":\"ant-design-pro-layout\",\"id\":\"8\"},\"9\":{\"path\":\"/subscription\",\"name\":\"订阅管理\",\"icon\":\"crown\",\"hideInMenu\":true,\"parentId\":\"ant-design-pro-layout\",\"id\":\"9\"},\"10\":{\"path\":\"/friend\",\"name\":\"好友管理\",\"icon\":\"userAdd\",\"hideInMenu\":true,\"parentId\":\"ant-design-pro-layout\",\"id\":\"10\"},\"11\":{\"path\":\"/help\",\"name\":\"帮助中心\",\"icon\":\"question\",\"hideInMenu\":true,\"parentId\":\"ant-design-pro-layout\",\"id\":\"11\"},\"12\":{\"path\":\"/\",\"redirect\":\"/dashboard\",\"parentId\":\"ant-design-pro-layout\",\"id\":\"12\"},\"13\":{\"path\":\"*\",\"layout\":false,\"id\":\"13\"},\"ant-design-pro-layout\":{\"id\":\"ant-design-pro-layout\",\"path\":\"/\",\"isLayout\":true},\"umi/plugin/openapi\":{\"path\":\"/umi/plugin/openapi\",\"id\":\"umi/plugin/openapi\"}} as const;\n  return {\n    routes,\n    routeComponents: {\n'1': React.lazy(() => import('./EmptyRoute')),\n'2': React.lazy(() => import(/* webpackChunkName: \"p__user__login__index\" */'@/pages/user/login/index.tsx')),\n'3': React.lazy(() => import(/* webpackChunkName: \"p__user__team-select__index\" */'@/pages/user/team-select/index.tsx')),\n'4': React.lazy(() => import(/* webpackChunkName: \"p__team__create__index\" */'@/pages/team/create/index.tsx')),\n'5': React.lazy(() => import(/* webpackChunkName: \"p__Dashboard__index\" */'@/pages/Dashboard/index.tsx')),\n'6': React.lazy(() => import(/* webpackChunkName: \"p__team__index\" */'@/pages/team/index.tsx')),\n'7': React.lazy(() => import(/* webpackChunkName: \"p__personal-center__index\" */'@/pages/personal-center/index.tsx')),\n'8': React.lazy(() => import(/* webpackChunkName: \"p__user__index\" */'@/pages/user/index.tsx')),\n'9': React.lazy(() => import(/* webpackChunkName: \"p__subscription__index\" */'@/pages/subscription/index.tsx')),\n'10': React.lazy(() => import(/* webpackChunkName: \"p__friend__index\" */'@/pages/friend/index.tsx')),\n'11': React.lazy(() => import(/* webpackChunkName: \"p__help__index\" */'@/pages/help/index.tsx')),\n'12': React.lazy(() => import('./EmptyRoute')),\n'13': React.lazy(() => import(/* webpackChunkName: \"p__404\" */'@/pages/404.tsx')),\n'ant-design-pro-layout': React.lazy(() => import(/* webpackChunkName: \"umi__plugin-layout__Layout\" */'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-layout/Layout.tsx')),\n'umi/plugin/openapi': React.lazy(() => import(/* webpackChunkName: \"umi__plugin-openapi__openapi\" */'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-openapi/openapi.tsx')),\n},\n  };\n}\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;;wCCES;;;2BAAA;;;;;;mFAFJ;;;;;;;;;YAEX,eAAe;gBACpB,MAAM,SAAS;oBAAC,KAAI;wBAAC,QAAO;wBAAQ,UAAS;wBAAM,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAQ,QAAO;wBAAc,YAAW;wBAAI,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAc,QAAO;wBAAoB,YAAW;wBAAI,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAe,UAAS;wBAAM,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAa,QAAO;wBAAM,QAAO;wBAAY,YAAW;wBAAwB,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAQ,QAAO;wBAAO,QAAO;wBAAO,cAAa;wBAAK,YAAW;wBAAwB,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAmB,QAAO;wBAAO,QAAO;wBAAO,cAAa;wBAAK,YAAW;wBAAwB,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAe,QAAO;wBAAO,QAAO;wBAAO,cAAa;wBAAK,YAAW;wBAAwB,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAgB,QAAO;wBAAO,QAAO;wBAAQ,cAAa;wBAAK,YAAW;wBAAwB,MAAK;oBAAG;oBAAE,MAAK;wBAAC,QAAO;wBAAU,QAAO;wBAAO,QAAO;wBAAU,cAAa;wBAAK,YAAW;wBAAwB,MAAK;oBAAI;oBAAE,MAAK;wBAAC,QAAO;wBAAQ,QAAO;wBAAO,QAAO;wBAAW,cAAa;wBAAK,YAAW;wBAAwB,MAAK;oBAAI;oBAAE,MAAK;wBAAC,QAAO;wBAAI,YAAW;wBAAa,YAAW;wBAAwB,MAAK;oBAAI;oBAAE,MAAK;wBAAC,QAAO;wBAAI,UAAS;wBAAM,MAAK;oBAAI;oBAAE,yBAAwB;wBAAC,MAAK;wBAAwB,QAAO;wBAAI,YAAW;oBAAI;oBAAE,sBAAqB;wBAAC,QAAO;wBAAsB,MAAK;oBAAoB;gBAAC;gBAC10C,OAAO;oBACL;oBACA,iBAAiB;wBACrB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,oBAAM,cAAK,CAAC,IAAI,CAAC,IAAM;wBACvB,oBAAM,cAAK,CAAC,IAAI,CAAC,IAAM;wBACvB,oBAAM,cAAK,CAAC,IAAI,CAAC,IAAM;wBACvB,oBAAM,cAAK,CAAC,IAAI,CAAC,IAAM;wBACvB,uCAAyB,cAAK,CAAC,IAAI,CAAC,IAAM;wBAC1C,oCAAsB,cAAK,CAAC,IAAI,CAAC,IAAM;oBACvC;gBACE;YACF;;;;;;;;;;;;;;;;;;;;;;;IDxBc;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,8BAA6B;YAAC;SAAmB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;YAAS;SAAyB;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,kCAAiC;YAAC;SAAwB;QAAC,wCAAuC;YAAC;SAAuC;IAAA;;AACr4B"}