{"level":30,"time":1753256699475,"pid":25104,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 默认使用 esbuild 作为 JavaScript 压缩工具，也可通过 jsMinifier 配置项切换到 terser 或 uglifyJs 等，详见 https://umijs.org/docs/api/config#jsminifier-webpack\u001b[39m"}
{"level":30,"time":1753256699481,"pid":25104,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753256699482,"pid":25104,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753256699484,"pid":25104,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753256701585,"pid":25104,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753256819056,"pid":21572,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果你需要使用 Jest 来测试 Umi 项目, max g jest 就可以一键完成配置，详见 https://umijs.org/docs/guides/generator#jest-配置生成器\u001b[39m"}
{"level":30,"time":1753256819061,"pid":21572,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753256819062,"pid":21572,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753256819065,"pid":21572,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753256821280,"pid":21572,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":32,"time":1753257212602,"pid":21572,"hostname":"DESKTOP-N6C2H1N","msg":"config routes, routes, routes, routes, routes, routes, routes, routes, routes, routes changed, regenerate tmp files..."}
{"level":30,"time":1753257236827,"pid":1088,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] @umijs/max 是蚂蚁内网框架 Bigfish 的对外版本。\u001b[39m"}
{"level":30,"time":1753257236832,"pid":1088,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753257236833,"pid":1088,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753257236835,"pid":1088,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753257238940,"pid":1088,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":32,"time":1753257247878,"pid":1088,"hostname":"DESKTOP-N6C2H1N","msg":"config layout changed, regenerate tmp files..."}
{"level":50,"time":1753257637184,"pid":1088,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not read from file: H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\umi.ts"}
{"level":50,"time":1753257637308,"pid":1088,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1753257637826,"pid":1088,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 23 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":30,"time":1753257637043,"pid":24608,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 页面加载慢、产物体积大怎么办？试试做代码拆分，详见 https://umijs.org/blog/code-splitting\u001b[39m"}
{"level":30,"time":1753257637049,"pid":24608,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753257637050,"pid":24608,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753257637052,"pid":24608,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753257639366,"pid":24608,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753257639457,"pid":1088,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 24 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":50,"time":1753257639822,"pid":1088,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 21 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":30,"time":1753258211367,"pid":18716,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] COMPRESS=none max build 可以关闭项目构建时的代码压缩功能, 方便调试项目的构建产物。\u001b[39m"}
{"level":30,"time":1753258211375,"pid":18716,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753258211377,"pid":18716,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753258211386,"pid":18716,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753258213566,"pid":18716,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753260214478,"pid":23828,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 全局数据存储，在 React 之外修改数据怎么办？试试一键上手 valtio，详见 https://umijs.org/docs/max/valtio\u001b[39m"}
{"level":30,"time":1753260214485,"pid":23828,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753260214486,"pid":23828,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753260214492,"pid":23828,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753260219341,"pid":23828,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753260577172,"pid":29780,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果想预览构建后产物, 可尝试 max preview，详见 https://umijs.org/docs/api/commands#preview\u001b[39m"}
{"level":30,"time":1753260577179,"pid":29780,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753260577182,"pid":29780,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753260577185,"pid":29780,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753260579243,"pid":29780,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753260688261,"pid":26316,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 请求加载态、数据管理、避免竟态问题，用 react-query 帮你全部解决，详见 https://umijs.org/docs/max/react-query\u001b[39m"}
{"level":30,"time":1753260688267,"pid":26316,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753260688268,"pid":26316,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753260688271,"pid":26316,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753260690422,"pid":26316,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753260790944,"pid":29220,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果想检测未使用的文件和导出，可尝试新出的 deadCode 配置项，详见 https://umijs.org/docs/api/config#deadcode\u001b[39m"}
{"level":30,"time":1753260790949,"pid":29220,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753260790950,"pid":29220,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753260790954,"pid":29220,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753260793147,"pid":29220,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753261097715,"pid":26700,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果你需要使用 Jest 来测试 Umi 项目, max g jest 就可以一键完成配置，详见 https://umijs.org/docs/guides/generator#jest-配置生成器\u001b[39m"}
{"level":30,"time":1753261097723,"pid":26700,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753261097724,"pid":26700,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753261097727,"pid":26700,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753261099991,"pid":26700,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753261734500,"pid":5868,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 你知道可以通过 UMI_ENV 定义多个环境的配置吗，详见 https://umijs.org/docs/guides/env-variables#umi_env\u001b[39m"}
{"level":30,"time":1753261734505,"pid":5868,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753261734505,"pid":5868,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753261734508,"pid":5868,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753261736633,"pid":5868,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753261776089,"pid":9864,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 请求加载态、数据管理、避免竟态问题，用 react-query 帮你全部解决，详见 https://umijs.org/docs/max/react-query\u001b[39m"}
{"level":30,"time":1753261776095,"pid":9864,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753261776097,"pid":9864,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753261776101,"pid":9864,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753261778361,"pid":9864,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753261784015,"pid":9864,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/components/index.ts:19:31: ERROR: Could not resolve \"./TeamSwitcher/SimpleTeamSwitcher\""}
{"level":32,"time":1753261784584,"pid":9864,"hostname":"DESKTOP-N6C2H1N","msg":"config routes, routes, routes, routes, routes, routes, routes, routes, routes changed, regenerate tmp files..."}
{"level":30,"time":1753261875514,"pid":29168,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 全局布局用 layout ，多层布局用 wrappers ，从文档了解更多路由的控制方法，详见 https://umijs.org/docs/guides/routes\u001b[39m"}
{"level":30,"time":1753261875520,"pid":29168,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753261875522,"pid":29168,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753261875525,"pid":29168,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753261877740,"pid":29168,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":32,"time":1753261953027,"pid":29168,"hostname":"DESKTOP-N6C2H1N","msg":"config layout changed, regenerate tmp files..."}
{"level":50,"time":1753262309906,"pid":29168,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1753262310503,"pid":29168,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 23 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":30,"time":1753262309696,"pid":30064,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] max g tsconfig 可一键完成项目的 TypeScript 配置。\u001b[39m"}
{"level":30,"time":1753262309703,"pid":30064,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753262309704,"pid":30064,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753262309708,"pid":30064,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753262312037,"pid":30064,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753262312333,"pid":29168,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 18 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":50,"time":1753262312680,"pid":29168,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 18 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":50,"time":1753262773558,"pid":29168,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1753262773668,"pid":29168,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1753262774091,"pid":29168,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 22 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":30,"time":1753262773413,"pid":13136,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] max g tsconfig 可一键完成项目的 TypeScript 配置。\u001b[39m"}
{"level":30,"time":1753262773428,"pid":13136,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753262773429,"pid":13136,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753262773432,"pid":13136,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753262775639,"pid":13136,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753262775776,"pid":29168,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 23 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":50,"time":1753262776198,"pid":29168,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 20 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":40,"time":1753262776382,"pid":13136,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m"}
{"level":40,"time":*************,"pid":13136,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/currentUser is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":13136,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/account is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":13136,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/outLogin is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":13136,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":13136,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m"}
{"level":40,"time":1753262776411,"pid":13136,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/tags is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\monitor.mock.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\monitor.mock.ts\u001b[39m"}
{"level":40,"time":1753262776416,"pid":13136,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/notices is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\notices.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\notices.ts\u001b[39m"}
{"level":40,"time":1753262776421,"pid":13136,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/currentUser is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753262776421,"pid":13136,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m"}
{"level":40,"time":*************,"pid":13136,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/outLogin is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":13136,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/account is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":13136,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/auth_routes is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\route.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\route.ts\u001b[39m"}
{"level":40,"time":*************,"pid":13136,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/currentUser is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":13136,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/users is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":13136,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/account is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":13136,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/outLogin is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":13136,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/register is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":13136,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/500 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753262776438,"pid":13136,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/404 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753262776439,"pid":13136,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/403 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753262776439,"pid":13136,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/401 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753262776440,"pid":13136,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/login/captcha is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":30,"time":1753262851955,"pid":17884,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果要支持低版本浏览器，可尝试新出的 legacy 配置项，详见 https://umijs.org/blog/legacy-browser\u001b[39m"}
{"level":30,"time":1753262851961,"pid":17884,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753262851961,"pid":17884,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753262851964,"pid":17884,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753262854070,"pid":17884,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753263306344,"pid":17884,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not read from file: H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\umi.ts"}
{"level":50,"time":1753263307046,"pid":17884,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1753263308598,"pid":17884,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 22 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":30,"time":1753263305962,"pid":29360,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] dev 模式下访问 /__umi 路由，可以发现很多有用的内部信息。\u001b[39m"}
{"level":30,"time":1753263305989,"pid":29360,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753263305991,"pid":29360,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753263306044,"pid":29360,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":**********111,"pid":29360,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":**********251,"pid":17884,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 23 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":50,"time":**********540,"pid":17884,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 20 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":40,"time":**********638,"pid":29360,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m"}
{"level":40,"time":*************,"pid":29360,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/currentUser is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":29360,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/account is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":29360,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/outLogin is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":29360,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":29360,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m"}
{"level":40,"time":**********659,"pid":29360,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/tags is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\monitor.mock.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\monitor.mock.ts\u001b[39m"}
{"level":40,"time":**********662,"pid":29360,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/notices is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\notices.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\notices.ts\u001b[39m"}
{"level":40,"time":**********667,"pid":29360,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/currentUser is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":**********668,"pid":29360,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m"}
{"level":40,"time":**********668,"pid":29360,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/outLogin is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":**********668,"pid":29360,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/account is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":**********671,"pid":29360,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/auth_routes is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\route.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\route.ts\u001b[39m"}
{"level":40,"time":**********676,"pid":29360,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/currentUser is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":**********676,"pid":29360,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/users is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":29360,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/account is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":29360,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/outLogin is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":29360,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/register is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":**********678,"pid":29360,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/500 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":**********678,"pid":29360,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/404 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":**********679,"pid":29360,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/403 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":**********679,"pid":29360,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/401 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":**********680,"pid":29360,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/login/captcha is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":30,"time":1753263457854,"pid":22800,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果想预览构建后产物, 可尝试 max preview，详见 https://umijs.org/docs/api/commands#preview\u001b[39m"}
{"level":30,"time":1753263457919,"pid":22800,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753263457920,"pid":22800,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753263457924,"pid":22800,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753263460079,"pid":22800,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":40,"time":1753263460715,"pid":22800,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m"}
{"level":40,"time":*************,"pid":22800,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/currentUser is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":22800,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/account is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":22800,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/outLogin is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":22800,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":22800,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m"}
{"level":40,"time":1753263460746,"pid":22800,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/tags is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\monitor.mock.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\monitor.mock.ts\u001b[39m"}
{"level":40,"time":1753263460750,"pid":22800,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/notices is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\notices.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\notices.ts\u001b[39m"}
{"level":40,"time":1753263460757,"pid":22800,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/currentUser is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753263460757,"pid":22800,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m"}
{"level":40,"time":*************,"pid":22800,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/outLogin is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":22800,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/account is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":22800,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/auth_routes is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\route.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\route.ts\u001b[39m"}
{"level":40,"time":*************,"pid":22800,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/currentUser is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":22800,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/users is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":22800,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/account is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":22800,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/outLogin is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":22800,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/register is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":22800,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/500 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753263460774,"pid":22800,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/404 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753263460775,"pid":22800,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/403 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753263460775,"pid":22800,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/401 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753263460776,"pid":22800,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/login/captcha is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":32,"time":1753264176150,"pid":22800,"hostname":"DESKTOP-N6C2H1N","msg":"config routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes changed, regenerate tmp files..."}
{"level":30,"time":1753264455395,"pid":24460,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] HMR=none max dev 可以关闭 Umi 开发服务器的模块热替换功能。\u001b[39m"}
{"level":30,"time":1753264455411,"pid":24460,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753264455411,"pid":24460,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753264455415,"pid":24460,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753264457475,"pid":24460,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":40,"time":1753264458157,"pid":24460,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m"}
{"level":40,"time":*************,"pid":24460,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/currentUser is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":24460,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/account is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":24460,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/outLogin is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":24460,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":24460,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m"}
{"level":40,"time":1753264458187,"pid":24460,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/tags is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\monitor.mock.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\monitor.mock.ts\u001b[39m"}
{"level":40,"time":1753264458192,"pid":24460,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/notices is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\notices.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\notices.ts\u001b[39m"}
{"level":40,"time":1753264458199,"pid":24460,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/currentUser is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753264458199,"pid":24460,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m"}
{"level":40,"time":*************,"pid":24460,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/outLogin is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":24460,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/account is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":24460,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/auth_routes is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\route.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\route.ts\u001b[39m"}
{"level":40,"time":*************,"pid":24460,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/currentUser is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":24460,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/users is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":24460,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/account is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":24460,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/outLogin is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":24460,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/register is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":24460,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/500 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753264458216,"pid":24460,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/404 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753264458216,"pid":24460,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/403 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753264458217,"pid":24460,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/401 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753264458217,"pid":24460,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/login/captcha is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":30,"time":1753264589544,"pid":27056,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果想检测未使用的文件和导出，可尝试新出的 deadCode 配置项，详见 https://umijs.org/docs/api/config#deadcode\u001b[39m"}
{"level":30,"time":1753264589561,"pid":27056,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753264589562,"pid":27056,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753264589565,"pid":27056,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753264591810,"pid":27056,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":40,"time":1753264592490,"pid":27056,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m"}
{"level":40,"time":*************,"pid":27056,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/currentUser is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":27056,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/account is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":27056,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/outLogin is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":27056,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":27056,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m"}
{"level":40,"time":1753264592519,"pid":27056,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/tags is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\monitor.mock.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\monitor.mock.ts\u001b[39m"}
{"level":40,"time":1753264592524,"pid":27056,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/notices is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\notices.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\notices.ts\u001b[39m"}
{"level":40,"time":1753264592530,"pid":27056,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/currentUser is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753264592531,"pid":27056,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m"}
{"level":40,"time":*************,"pid":27056,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/outLogin is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":27056,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/account is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":27056,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/auth_routes is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\route.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\route.ts\u001b[39m"}
{"level":40,"time":*************,"pid":27056,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/currentUser is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":27056,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/users is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":27056,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/account is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":27056,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/outLogin is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":27056,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/register is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":27056,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/500 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":27056,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/404 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753264592547,"pid":27056,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/403 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753264592548,"pid":27056,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/401 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753264592548,"pid":27056,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/login/captcha is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":30,"time":1753322052297,"pid":19236,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果你需要增加 Git 提交消息校验和自动代码格式化, max g precommit 就可以一键完成配置，详见 https://umijs.org/docs/guides/generator#precommit-配置生成器\u001b[39m"}
{"level":30,"time":1753322052303,"pid":19236,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753322052304,"pid":19236,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753322052307,"pid":19236,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753322055346,"pid":19236,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753324517624,"pid":19236,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not read from file: H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\umi.ts"}
{"level":50,"time":1753324517744,"pid":19236,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1753324518452,"pid":19236,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 25 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":30,"time":1753324517478,"pid":27152,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] max g page 可以快速生成页面模板，详见 https://umijs.org/docs/guides/generator#页面生成器\u001b[39m"}
{"level":30,"time":1753324517496,"pid":27152,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753324517497,"pid":27152,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753324517499,"pid":27152,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753324519962,"pid":27152,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753324520293,"pid":19236,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 20 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":50,"time":1753324520664,"pid":19236,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 20 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":40,"time":1753324520816,"pid":27152,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m"}
{"level":40,"time":*************,"pid":27152,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/currentUser is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":27152,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/account is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":27152,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/outLogin is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":27152,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":27152,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m"}
{"level":40,"time":1753324520845,"pid":27152,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/tags is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\monitor.mock.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\monitor.mock.ts\u001b[39m"}
{"level":40,"time":1753324520849,"pid":27152,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/notices is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\notices.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\notices.ts\u001b[39m"}
{"level":40,"time":1753324520856,"pid":27152,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/currentUser is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":27152,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m"}
{"level":40,"time":*************,"pid":27152,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/outLogin is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":27152,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/account is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":27152,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/auth_routes is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\route.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\route.ts\u001b[39m"}
{"level":40,"time":*************,"pid":27152,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/currentUser is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":27152,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/users is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":27152,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/account is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":27152,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/outLogin is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":27152,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/register is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":27152,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/500 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753324520872,"pid":27152,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/404 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753324520872,"pid":27152,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/403 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753324520873,"pid":27152,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/401 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753324520874,"pid":27152,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/login/captcha is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":30,"time":1753324721378,"pid":20576,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 页面加载慢、产物体积大怎么办？试试做代码拆分，详见 https://umijs.org/blog/code-splitting\u001b[39m"}
{"level":30,"time":1753324721383,"pid":20576,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753324721384,"pid":20576,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753324721388,"pid":20576,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753324723713,"pid":20576,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753324840227,"pid":20576,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/user/team-select/index.tsx:14:9: ERROR: No matching export in \"src/app.tsx\" for import \"setTeamSelectionInProgress\""}
{"level":50,"time":1753324895600,"pid":20576,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/user/team-select/index.tsx:14:9: ERROR: No matching export in \"src/app.tsx\" for import \"setTeamSelectionInProgress\""}
{"level":50,"time":1753324909852,"pid":20576,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/user/team-select/index.tsx:14:9: ERROR: No matching export in \"src/app.tsx\" for import \"setTeamSelectionInProgress\""}
{"level":50,"time":1753324956085,"pid":20576,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not read from file: H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\umi.ts"}
{"level":50,"time":1753324956196,"pid":20576,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1753324956762,"pid":20576,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 25 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":50,"time":1753324958338,"pid":20576,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 23 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":50,"time":1753324958720,"pid":20576,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 20 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":30,"time":1753324955888,"pid":21980,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] PORT=9000 max dev 可以指定 Umi 开发服务器的端口。\u001b[39m"}
{"level":30,"time":1753324955908,"pid":21980,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753324955909,"pid":21980,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753324955912,"pid":21980,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753324958154,"pid":21980,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":40,"time":1753324958840,"pid":21980,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m"}
{"level":40,"time":*************,"pid":21980,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/currentUser is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":21980,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/account is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":21980,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/outLogin is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":21980,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":21980,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m"}
{"level":40,"time":1753324958866,"pid":21980,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/tags is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\monitor.mock.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\monitor.mock.ts\u001b[39m"}
{"level":40,"time":1753324958869,"pid":21980,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/notices is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\notices.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\notices.ts\u001b[39m"}
{"level":40,"time":1753324958875,"pid":21980,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/currentUser is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753324958876,"pid":21980,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m"}
{"level":40,"time":*************,"pid":21980,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/outLogin is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":21980,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/account is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":21980,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/auth_routes is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\route.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\route.ts\u001b[39m"}
{"level":40,"time":*************,"pid":21980,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/currentUser is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":21980,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/users is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":21980,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/account is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":21980,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/outLogin is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":21980,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/register is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":21980,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/500 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753324958889,"pid":21980,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/404 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753324958889,"pid":21980,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/403 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753324958890,"pid":21980,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/401 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753324958891,"pid":21980,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/login/captcha is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":50,"time":1753325147137,"pid":20576,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1753325148015,"pid":20576,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 25 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":30,"time":1753325146964,"pid":21532,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] @umijs/max 是蚂蚁内网框架 Bigfish 的对外版本。\u001b[39m"}
{"level":30,"time":1753325146979,"pid":21532,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753325146980,"pid":21532,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753325146983,"pid":21532,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753325149311,"pid":21532,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753325149417,"pid":20576,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 26 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":50,"time":1753325149839,"pid":20576,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 23 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":40,"time":1753325149988,"pid":21532,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m"}
{"level":40,"time":*************,"pid":21532,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/currentUser is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":21532,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/account is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":21532,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/outLogin is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":21532,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":21532,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m"}
{"level":40,"time":1753325150020,"pid":21532,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/tags is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\monitor.mock.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\monitor.mock.ts\u001b[39m"}
{"level":40,"time":1753325150025,"pid":21532,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/notices is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\notices.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\notices.ts\u001b[39m"}
{"level":40,"time":1753325150031,"pid":21532,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/currentUser is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753325150031,"pid":21532,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m"}
{"level":40,"time":*************,"pid":21532,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/outLogin is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":21532,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/account is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":21532,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/auth_routes is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\route.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\route.ts\u001b[39m"}
{"level":40,"time":*************,"pid":21532,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/currentUser is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":21532,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/users is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":21532,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/account is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":21532,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/outLogin is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":21532,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/register is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":21532,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/500 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753325150046,"pid":21532,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/404 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753325150046,"pid":21532,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/403 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753325150047,"pid":21532,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/401 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753325150048,"pid":21532,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/login/captcha is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":50,"time":1753326771046,"pid":20576,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 7 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nsrc/.umi/umi.ts:4:7: ERROR: Could not resolve \"./core/polyfill\"\nsrc/.umi/umi.ts:9:26: ERROR: Could not resolve \"./core/route\"\nsrc/.umi/umi.ts:10:36: ERROR: Could not resolve \"./core/plugin\"\n..."}
{"level":50,"time":1753326771166,"pid":20576,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1753326771563,"pid":20576,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 26 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":50,"time":1753326771920,"pid":20576,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 25 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":30,"time":1753326770851,"pid":13016,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 全局数据存储，在 React 之外修改数据怎么办？试试一键上手 valtio，详见 https://umijs.org/docs/max/valtio\u001b[39m"}
{"level":30,"time":1753326770857,"pid":13016,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753326770857,"pid":13016,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753326770860,"pid":13016,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753326773081,"pid":13016,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753326773162,"pid":20576,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 26 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":50,"time":1753326773549,"pid":20576,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 23 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":50,"time":1753326836326,"pid":20576,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 7 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nsrc/.umi/umi.ts:4:7: ERROR: Could not resolve \"./core/polyfill\"\nsrc/.umi/umi.ts:9:26: ERROR: Could not resolve \"./core/route\"\nsrc/.umi/umi.ts:10:36: ERROR: Could not resolve \"./core/plugin\"\n..."}
{"level":50,"time":1753326836438,"pid":20576,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1753326836945,"pid":20576,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 25 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":50,"time":1753326838429,"pid":20576,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 26 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":50,"time":1753326838829,"pid":20576,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 23 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":30,"time":1753326836141,"pid":1948,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 你知道可以通过 UMI_ENV 定义多个环境的配置吗，详见 https://umijs.org/docs/guides/env-variables#umi_env\u001b[39m"}
{"level":30,"time":1753326836147,"pid":1948,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753326836147,"pid":1948,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753326836149,"pid":1948,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753326838319,"pid":1948,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753328360945,"pid":15580,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 默认使用 esbuild 作为 JavaScript 压缩工具，也可通过 jsMinifier 配置项切换到 terser 或 uglifyJs 等，详见 https://umijs.org/docs/api/config#jsminifier-webpack\u001b[39m"}
{"level":30,"time":1753328360949,"pid":15580,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753328360950,"pid":15580,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753328360953,"pid":15580,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753328363007,"pid":15580,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753328374133,"pid":15580,"hostname":"DESKTOP-N6C2H1N","msg":"Memory Usage: 141.96 MB (RSS: 629.17 MB)"}
{"level":32,"time":1753328374222,"pid":15580,"hostname":"DESKTOP-N6C2H1N","msg":"Build user\\index.html"}
{"level":32,"time":1753328374224,"pid":15580,"hostname":"DESKTOP-N6C2H1N","msg":"Build user\\login\\index.html"}
{"level":32,"time":1753328374226,"pid":15580,"hostname":"DESKTOP-N6C2H1N","msg":"Build user\\team-select\\index.html"}
{"level":32,"time":1753328374229,"pid":15580,"hostname":"DESKTOP-N6C2H1N","msg":"Build team\\create\\index.html"}
{"level":32,"time":1753328374231,"pid":15580,"hostname":"DESKTOP-N6C2H1N","msg":"Build dashboard\\index.html"}
{"level":32,"time":1753328374232,"pid":15580,"hostname":"DESKTOP-N6C2H1N","msg":"Build team\\index.html"}
{"level":32,"time":1753328374234,"pid":15580,"hostname":"DESKTOP-N6C2H1N","msg":"Build personal-center\\index.html"}
{"level":32,"time":1753328374237,"pid":15580,"hostname":"DESKTOP-N6C2H1N","msg":"Build user-manage\\index.html"}
{"level":32,"time":1753328374239,"pid":15580,"hostname":"DESKTOP-N6C2H1N","msg":"Build subscription\\index.html"}
{"level":32,"time":1753328374242,"pid":15580,"hostname":"DESKTOP-N6C2H1N","msg":"Build index.html"}
{"level":32,"time":1753328374244,"pid":15580,"hostname":"DESKTOP-N6C2H1N","msg":"Build 404.html"}
{"level":50,"time":1753328525718,"pid":1948,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/app.tsx:21:9: ERROR: No matching export in \"src/utils/tokenUtils.ts\" for import \"hasTeamInCurrentToken\""}
{"level":50,"time":1753328661981,"pid":1948,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1753328662609,"pid":1948,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 25 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":50,"time":1753328664312,"pid":1948,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 26 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":50,"time":1753328664733,"pid":1948,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 23 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":30,"time":1753328661821,"pid":16840,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果你需要增加 Git 提交消息校验和自动代码格式化, max g precommit 就可以一键完成配置，详见 https://umijs.org/docs/guides/generator#precommit-配置生成器\u001b[39m"}
{"level":30,"time":1753328661827,"pid":16840,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753328661828,"pid":16840,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753328661831,"pid":16840,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753328664180,"pid":16840,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753333318143,"pid":16000,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] HMR=none max dev 可以关闭 Umi 开发服务器的模块热替换功能。\u001b[39m"}
{"level":30,"time":1753333318150,"pid":16000,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753333318151,"pid":16000,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753333318154,"pid":16000,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753333320442,"pid":16000,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753333687870,"pid":27964,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果你需要使用 Tailwind CSS, max g tailwindcss 就可以一键完成配置，详见 https://umijs.org/docs/guides/generator#tailwind-css-配置生成器\u001b[39m"}
{"level":30,"time":1753333687875,"pid":27964,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753333687876,"pid":27964,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753333687880,"pid":27964,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753333690236,"pid":27964,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753333701834,"pid":27964,"hostname":"DESKTOP-N6C2H1N","msg":"Memory Usage: 141.69 MB (RSS: 613.72 MB)"}
{"level":32,"time":1753333701929,"pid":27964,"hostname":"DESKTOP-N6C2H1N","msg":"Build user\\index.html"}
{"level":32,"time":1753333701932,"pid":27964,"hostname":"DESKTOP-N6C2H1N","msg":"Build user\\login\\index.html"}
{"level":32,"time":1753333701933,"pid":27964,"hostname":"DESKTOP-N6C2H1N","msg":"Build user\\team-select\\index.html"}
{"level":32,"time":1753333701938,"pid":27964,"hostname":"DESKTOP-N6C2H1N","msg":"Build team\\create\\index.html"}
{"level":32,"time":1753333701941,"pid":27964,"hostname":"DESKTOP-N6C2H1N","msg":"Build dashboard\\index.html"}
{"level":32,"time":1753333701943,"pid":27964,"hostname":"DESKTOP-N6C2H1N","msg":"Build team\\index.html"}
{"level":32,"time":1753333701945,"pid":27964,"hostname":"DESKTOP-N6C2H1N","msg":"Build personal-center\\index.html"}
{"level":32,"time":1753333701948,"pid":27964,"hostname":"DESKTOP-N6C2H1N","msg":"Build user-manage\\index.html"}
{"level":32,"time":1753333701950,"pid":27964,"hostname":"DESKTOP-N6C2H1N","msg":"Build subscription\\index.html"}
{"level":32,"time":1753333701953,"pid":27964,"hostname":"DESKTOP-N6C2H1N","msg":"Build index.html"}
{"level":32,"time":1753333701956,"pid":27964,"hostname":"DESKTOP-N6C2H1N","msg":"Build 404.html"}
{"level":50,"time":1753333804989,"pid":16000,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not read from file: H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\umi.ts"}
{"level":50,"time":1753333805099,"pid":16000,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1753333805648,"pid":16000,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 25 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":50,"time":1753333807309,"pid":16000,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 26 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":50,"time":1753333807809,"pid":16000,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 23 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":30,"time":1753333804853,"pid":28016,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 默认使用 esbuild 作为 JavaScript 压缩工具，也可通过 jsMinifier 配置项切换到 terser 或 uglifyJs 等，详见 https://umijs.org/docs/api/config#jsminifier-webpack\u001b[39m"}
{"level":30,"time":1753333804858,"pid":28016,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753333804859,"pid":28016,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753333804862,"pid":28016,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753333807083,"pid":28016,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753334289678,"pid":31556,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 你知道可以通过 UMI_ENV 定义多个环境的配置吗，详见 https://umijs.org/docs/guides/env-variables#umi_env\u001b[39m"}
{"level":30,"time":1753334289684,"pid":31556,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753334289685,"pid":31556,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753334289688,"pid":31556,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753334291825,"pid":31556,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753334578478,"pid":31556,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1753334579032,"pid":31556,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 25 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":30,"time":1753334578322,"pid":14940,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] max g component 可以快速生成组件模板，详见 https://umijs.org/docs/guides/generator#组件生成器\u001b[39m"}
{"level":30,"time":1753334578327,"pid":14940,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753334578328,"pid":14940,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753334578330,"pid":14940,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753334580484,"pid":14940,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753334580648,"pid":31556,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 26 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":50,"time":1753334581094,"pid":31556,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 23 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":32,"time":1753335151569,"pid":14940,"hostname":"DESKTOP-N6C2H1N","msg":"config layout, layout changed, regenerate tmp files..."}
{"level":32,"time":1753335151589,"pid":31556,"hostname":"DESKTOP-N6C2H1N","msg":"config layout, layout changed, regenerate tmp files..."}
{"level":50,"time":1753335198597,"pid":14940,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/app.tsx:10:2: ERROR: No matching export in \"src/components/index.ts\" for import \"Question\""}
{"level":50,"time":1753335198812,"pid":31556,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/app.tsx:10:2: ERROR: No matching export in \"src/components/index.ts\" for import \"Question\""}
{"level":50,"time":1753335212194,"pid":14940,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/app.tsx:10:2: ERROR: No matching export in \"src/components/index.ts\" for import \"Question\""}
{"level":50,"time":1753335212205,"pid":31556,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/app.tsx:10:2: ERROR: No matching export in \"src/components/index.ts\" for import \"Question\""}
{"level":32,"time":1753335785809,"pid":14940,"hostname":"DESKTOP-N6C2H1N","msg":"config routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes changed, regenerate tmp files..."}
{"level":32,"time":1753335785828,"pid":31556,"hostname":"DESKTOP-N6C2H1N","msg":"config routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes changed, regenerate tmp files..."}
{"level":32,"time":1753336710442,"pid":31556,"hostname":"DESKTOP-N6C2H1N","msg":"config routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes changed, regenerate tmp files..."}
{"level":50,"time":1753336710952,"pid":31556,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 3 errors:\nsrc/pages/friend/components/AddFriend.tsx:25:9: ERROR: No matching export in \"src/services/index.ts\" for import \"FriendService\"\nsrc/pages/friend/components/FriendList.tsx:24:9: ERROR: No matching export in \"src/services/index.ts\" for import \"FriendService\"\nsrc/pages/friend/index.tsx:20:9: ERROR: No matching export in \"src/services/index.ts\" for import \"FriendService\""}
{"level":30,"time":1753340266583,"pid":30396,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] max g component 可以快速生成组件模板，详见 https://umijs.org/docs/guides/generator#组件生成器\u001b[39m"}
{"level":30,"time":1753340266589,"pid":30396,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753340266590,"pid":30396,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753340266593,"pid":30396,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753340269022,"pid":30396,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753342568913,"pid":30396,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/index.tsx:26:32: ERROR: Could not resolve \"../user/components/UserSettingsContent\"\nsrc/pages/user/index.tsx:20:32: ERROR: Could not resolve \"./components/UserSettingsContent\""}
{"level":50,"time":1753342575731,"pid":30396,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/index.tsx:25:32: ERROR: Could not resolve \"../user/components/UserSettingsContent\"\nsrc/pages/user/index.tsx:20:32: ERROR: Could not resolve \"./components/UserSettingsContent\""}
{"level":50,"time":1753342592380,"pid":30396,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/user/index.tsx:20:32: ERROR: Could not resolve \"./components/UserSettingsContent\""}
{"level":50,"time":1753342608341,"pid":30396,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/index.tsx:118:2: ERROR: Expected \"}\" but found \"]\"\nsrc/pages/user/index.tsx:20:32: ERROR: Could not resolve \"./components/UserSettingsContent\""}
{"level":50,"time":1753342626327,"pid":30396,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/index.tsx:118:2: ERROR: Expected \"}\" but found \"]\"\nsrc/pages/user/index.tsx:20:32: ERROR: Could not resolve \"./components/UserSettingsContent\""}
{"level":50,"time":1753342660318,"pid":30396,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/personal-center/index.tsx:118:2: ERROR: Expected \"}\" but found \"]\""}
{"level":50,"time":1753342755201,"pid":30396,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/personal-center/index.tsx:118:2: ERROR: Expected \"}\" but found \"]\""}
{"level":50,"time":1753342773816,"pid":30396,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/personal-center/index.tsx:118:2: ERROR: Expected \"}\" but found \"]\""}
{"level":50,"time":1753342783648,"pid":30396,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/personal-center/index.tsx:118:2: ERROR: Expected \"}\" but found \"]\""}
{"level":50,"time":1753342795676,"pid":30396,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/personal-center/index.tsx:118:2: ERROR: Expected \"}\" but found \"]\""}
{"level":50,"time":1753342913995,"pid":30396,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/personal-center/index.tsx:118:2: ERROR: Expected \"}\" but found \"]\""}
{"level":50,"time":1753342929176,"pid":30396,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/personal-center/index.tsx:118:2: ERROR: Expected \"}\" but found \"]\""}
{"level":50,"time":1753342943096,"pid":30396,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/personal-center/index.tsx:118:2: ERROR: Expected \"}\" but found \"]\""}
{"level":50,"time":1753342975516,"pid":30396,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/personal-center/index.tsx:118:2: ERROR: Expected \"}\" but found \"]\""}
{"level":50,"time":1753342996804,"pid":30396,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/personal-center/index.tsx:118:2: ERROR: Expected \"}\" but found \"]\""}
{"level":50,"time":1753343020378,"pid":30396,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/personal-center/index.tsx:118:2: ERROR: Expected \"}\" but found \"]\""}
{"level":50,"time":1753343196446,"pid":30396,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/personal-center/index.tsx:118:2: ERROR: Expected \"}\" but found \"]\""}
{"level":30,"time":1753343433299,"pid":9876,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 页面加载慢、产物体积大怎么办？试试做代码拆分，详见 https://umijs.org/blog/code-splitting\u001b[39m"}
{"level":30,"time":1753343433304,"pid":9876,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753343433304,"pid":9876,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753343433307,"pid":9876,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753343435323,"pid":9876,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":60,"time":1753343435702,"pid":9876,"hostname":"DESKTOP-N6C2H1N","err":{"type":"Error","message":"Build failed with 1 error:\nsrc/pages/personal-center/index.tsx:118:2: ERROR: Expected \"}\" but found \"]\"","stack":"Error: Build failed with 1 error:\nsrc/pages/personal-center/index.tsx:118:2: ERROR: Expected \"}\" but found \"]\"\n    at failureErrorWithLog (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:1472:15)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:945:25\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:1353:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","errors":[{"id":"","location":{"column":2,"file":"src/pages/personal-center/index.tsx","length":1,"line":118,"lineText":"  ];","namespace":"","suggestion":"}"},"notes":[],"pluginName":"","text":"Expected \"}\" but found \"]\""}],"warnings":[]},"msg":"Build failed with 1 error:\nsrc/pages/personal-center/index.tsx:118:2: ERROR: Expected \"}\" but found \"]\""}
{"level":60,"time":1753343436245,"pid":9876,"hostname":"DESKTOP-N6C2H1N","msg":"A complete log of this run can be found in:"}
{"level":60,"time":1753343436259,"pid":9876,"hostname":"DESKTOP-N6C2H1N","msg":"H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\.cache\\logger\\umi.log"}
{"level":60,"time":1753343436275,"pid":9876,"hostname":"DESKTOP-N6C2H1N","msg":"Consider reporting a GitHub issue on https://github.com/umijs/umi/issues"}
{"level":30,"time":1753343473969,"pid":14688,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 全局数据存储，在 React 之外修改数据怎么办？试试一键上手 valtio，详见 https://umijs.org/docs/max/valtio\u001b[39m"}
{"level":30,"time":1753343473974,"pid":14688,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753343473975,"pid":14688,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753343473977,"pid":14688,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753343476026,"pid":14688,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753343487207,"pid":14688,"hostname":"DESKTOP-N6C2H1N","msg":"Memory Usage: 122.89 MB (RSS: 560.36 MB)"}
{"level":32,"time":1753343487296,"pid":14688,"hostname":"DESKTOP-N6C2H1N","msg":"Build user\\index.html"}
{"level":32,"time":1753343487298,"pid":14688,"hostname":"DESKTOP-N6C2H1N","msg":"Build user\\login\\index.html"}
{"level":32,"time":1753343487300,"pid":14688,"hostname":"DESKTOP-N6C2H1N","msg":"Build user\\team-select\\index.html"}
{"level":32,"time":1753343487303,"pid":14688,"hostname":"DESKTOP-N6C2H1N","msg":"Build team\\create\\index.html"}
{"level":32,"time":1753343487306,"pid":14688,"hostname":"DESKTOP-N6C2H1N","msg":"Build dashboard\\index.html"}
{"level":32,"time":1753343487308,"pid":14688,"hostname":"DESKTOP-N6C2H1N","msg":"Build team\\index.html"}
{"level":32,"time":1753343487310,"pid":14688,"hostname":"DESKTOP-N6C2H1N","msg":"Build personal-center\\index.html"}
{"level":32,"time":1753343487311,"pid":14688,"hostname":"DESKTOP-N6C2H1N","msg":"Build user-manage\\index.html"}
{"level":32,"time":1753343487314,"pid":14688,"hostname":"DESKTOP-N6C2H1N","msg":"Build subscription\\index.html"}
{"level":32,"time":1753343487316,"pid":14688,"hostname":"DESKTOP-N6C2H1N","msg":"Build friend\\index.html"}
{"level":32,"time":1753343487318,"pid":14688,"hostname":"DESKTOP-N6C2H1N","msg":"Build help\\index.html"}
{"level":32,"time":1753343487320,"pid":14688,"hostname":"DESKTOP-N6C2H1N","msg":"Build index.html"}
{"level":32,"time":1753343487322,"pid":14688,"hostname":"DESKTOP-N6C2H1N","msg":"Build 404.html"}
{"level":50,"time":1753343758679,"pid":30396,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1753343759332,"pid":30396,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 24 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":30,"time":1753343758500,"pid":17212,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] HMR=none max dev 可以关闭 Umi 开发服务器的模块热替换功能。\u001b[39m"}
{"level":30,"time":1753343758516,"pid":17212,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753343758517,"pid":17212,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753343758520,"pid":17212,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753343760775,"pid":17212,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753343760889,"pid":30396,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 25 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":50,"time":1753343761307,"pid":30396,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 22 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":40,"time":1753343761538,"pid":17212,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m"}
{"level":40,"time":*************,"pid":17212,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/currentUser is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":17212,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/account is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":17212,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/outLogin is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":17212,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":17212,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m"}
{"level":40,"time":1753343761567,"pid":17212,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/tags is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\monitor.mock.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\monitor.mock.ts\u001b[39m"}
{"level":40,"time":1753343761572,"pid":17212,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/notices is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\notices.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\notices.ts\u001b[39m"}
{"level":40,"time":1753343761578,"pid":17212,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/currentUser is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":17212,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/rule is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\listTableList.ts\u001b[39m"}
{"level":40,"time":*************,"pid":17212,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/outLogin is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":17212,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/account is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":17212,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/auth_routes is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\route.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\route.ts\u001b[39m"}
{"level":40,"time":*************,"pid":17212,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/currentUser is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":17212,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/users is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":17212,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/account is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":17212,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/login/outLogin is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\requestRecord.mock.js\u001b[39m"}
{"level":40,"time":*************,"pid":17212,"hostname":"DESKTOP-N6C2H1N","msg":"POST /api/register is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":*************,"pid":17212,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/500 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753343761594,"pid":17212,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/404 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753343761594,"pid":17212,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/403 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753343761595,"pid":17212,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/401 is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":40,"time":1753343761595,"pid":17212,"hostname":"DESKTOP-N6C2H1N","msg":"GET /api/login/captcha is duplicated in \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m and \u001b[33mH:\\projects\\IdeaProjects\\teamAuth\\frontend\\mock\\user.ts\u001b[39m"}
{"level":30,"time":1753345107293,"pid":18012,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 编写 src/loading.(jsx|tsx) 可以自定义页面的加载动画。\u001b[39m"}
{"level":30,"time":1753345107298,"pid":18012,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753345107299,"pid":18012,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753345107301,"pid":18012,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753345109376,"pid":18012,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753345121345,"pid":18012,"hostname":"DESKTOP-N6C2H1N","msg":"Memory Usage: 122.9 MB (RSS: 567.71 MB)"}
{"level":32,"time":1753345121433,"pid":18012,"hostname":"DESKTOP-N6C2H1N","msg":"Build user\\index.html"}
{"level":32,"time":1753345121435,"pid":18012,"hostname":"DESKTOP-N6C2H1N","msg":"Build user\\login\\index.html"}
{"level":32,"time":1753345121437,"pid":18012,"hostname":"DESKTOP-N6C2H1N","msg":"Build user\\team-select\\index.html"}
{"level":32,"time":1753345121440,"pid":18012,"hostname":"DESKTOP-N6C2H1N","msg":"Build team\\create\\index.html"}
{"level":32,"time":1753345121442,"pid":18012,"hostname":"DESKTOP-N6C2H1N","msg":"Build dashboard\\index.html"}
{"level":32,"time":1753345121444,"pid":18012,"hostname":"DESKTOP-N6C2H1N","msg":"Build team\\index.html"}
{"level":32,"time":1753345121445,"pid":18012,"hostname":"DESKTOP-N6C2H1N","msg":"Build personal-center\\index.html"}
{"level":32,"time":1753345121447,"pid":18012,"hostname":"DESKTOP-N6C2H1N","msg":"Build user-manage\\index.html"}
{"level":32,"time":1753345121449,"pid":18012,"hostname":"DESKTOP-N6C2H1N","msg":"Build subscription\\index.html"}
{"level":32,"time":1753345121450,"pid":18012,"hostname":"DESKTOP-N6C2H1N","msg":"Build friend\\index.html"}
{"level":32,"time":1753345121452,"pid":18012,"hostname":"DESKTOP-N6C2H1N","msg":"Build help\\index.html"}
{"level":32,"time":1753345121454,"pid":18012,"hostname":"DESKTOP-N6C2H1N","msg":"Build index.html"}
{"level":32,"time":1753345121455,"pid":18012,"hostname":"DESKTOP-N6C2H1N","msg":"Build 404.html"}
{"level":30,"time":1753345288332,"pid":31360,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果想检测未使用的文件和导出，可尝试新出的 deadCode 配置项，详见 https://umijs.org/docs/api/config#deadcode\u001b[39m"}
{"level":30,"time":1753345288337,"pid":31360,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753345288338,"pid":31360,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753345288341,"pid":31360,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753345290377,"pid":31360,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753406199831,"pid":18604,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果想点击组件跳转至编辑器源码位置，可尝试新出的 clickToComponent 配置项，详见 https://umijs.org/docs/api/config#clicktocomponent\u001b[39m"}
{"level":30,"time":1753406199837,"pid":18604,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753406199838,"pid":18604,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753406199850,"pid":18604,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753406203435,"pid":18604,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753409738341,"pid":18604,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/friend/components/FriendRequests.tsx:201:2: ERROR: Unexpected \")\""}
{"level":50,"time":1753409764484,"pid":18604,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/friend/components/FriendRequests.tsx:129:18: ERROR: Expected identifier but found \"(\""}
{"level":30,"time":1753410150611,"pid":19260,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] max g tsconfig 可一键完成项目的 TypeScript 配置。\u001b[39m"}
{"level":30,"time":1753410150617,"pid":19260,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753410150618,"pid":19260,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753410150620,"pid":19260,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753410152915,"pid":19260,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753412901184,"pid":17604,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果想检测未使用的文件和导出，可尝试新出的 deadCode 配置项，详见 https://umijs.org/docs/api/config#deadcode\u001b[39m"}
{"level":30,"time":1753412901191,"pid":17604,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753412901192,"pid":17604,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753412901195,"pid":17604,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753412903268,"pid":17604,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":32,"time":1753413582799,"pid":17604,"hostname":"DESKTOP-N6C2H1N","msg":"config routes, routes changed, regenerate tmp files..."}
{"level":32,"time":1753413636343,"pid":17604,"hostname":"DESKTOP-N6C2H1N","msg":"config routes, routes changed, regenerate tmp files..."}
{"level":50,"time":1753413932172,"pid":17604,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not read from file: H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\umi.ts"}
{"level":50,"time":1753413932294,"pid":17604,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1753413932942,"pid":17604,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 24 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":50,"time":1753413934516,"pid":17604,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 25 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":50,"time":1753413934901,"pid":17604,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 22 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":30,"time":1753413932031,"pid":22988,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果你需要增加 Git 提交消息校验和自动代码格式化, max g precommit 就可以一键完成配置，详见 https://umijs.org/docs/guides/generator#precommit-配置生成器\u001b[39m"}
{"level":30,"time":1753413932040,"pid":22988,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753413932040,"pid":22988,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753413932042,"pid":22988,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753413934551,"pid":22988,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
