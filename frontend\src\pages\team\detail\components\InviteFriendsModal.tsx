/**
 * 从好友列表邀请成员模态框组件
 */

import React, { useState, useEffect } from 'react';
import { 
  Modal, 
  List, 
  Avatar, 
  Button, 
  Space, 
  Typography, 
  message,
  Alert,
  Empty,
  Checkbox,
  Input,
  Divider
} from 'antd';
import { 
  UserOutlined, 
  UserAddOutlined,
  SearchOutlined,
  TeamOutlined
} from '@ant-design/icons';
import { FriendService, TeamService } from '@/services';
import type { Account, InviteFriendsRequest } from '@/types/api';

const { Text, Title } = Typography;
const { Search } = Input;

interface InviteFriendsModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
}

const InviteFriendsModal: React.FC<InviteFriendsModalProps> = ({
  visible,
  onCancel,
  onSuccess
}) => {
  const [loading, setLoading] = useState(false);
  const [inviting, setInviting] = useState(false);
  const [friends, setFriends] = useState<Account[]>([]);
  const [selectedFriends, setSelectedFriends] = useState<number[]>([]);
  const [searchKeyword, setSearchKeyword] = useState('');

  // 获取好友列表
  const fetchFriends = async () => {
    try {
      setLoading(true);
      const friendList = await FriendService.getFriends();
      setFriends(friendList);
    } catch (error) {
      console.error('获取好友列表失败:', error);
      message.error('获取好友列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible) {
      fetchFriends();
      setSelectedFriends([]);
      setSearchKeyword('');
    }
  }, [visible]);

  // 过滤好友列表
  const filteredFriends = friends.filter(friend => 
    friend.name.toLowerCase().includes(searchKeyword.toLowerCase()) ||
    friend.email.toLowerCase().includes(searchKeyword.toLowerCase())
  );

  /**
   * 处理单个好友的选择状态变化
   *
   * @param friendId 好友ID
   * @param checked 是否选中
   */
  const handleFriendSelect = (friendId: number, checked: boolean) => {
    if (checked) {
      // 添加到选中列表
      setSelectedFriends(prev => [...prev, friendId]);
    } else {
      // 从选中列表中移除
      setSelectedFriends(prev => prev.filter(id => id !== friendId));
    }
  };

  /**
   * 处理全选/取消全选操作
   *
   * @param checked 是否全选
   */
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      // 选中当前过滤结果中的所有好友
      setSelectedFriends(filteredFriends.map(friend => friend.id));
    } else {
      // 清空选择
      setSelectedFriends([]);
    }
  };

  /**
   * 邀请选中的好友加入团队
   *
   * 执行流程：
   * 1. 验证是否选择了好友
   * 2. 设置邀请状态，显示加载动画
   * 3. 构造邀请请求对象
   * 4. 调用API邀请好友加入团队
   * 5. 显示成功消息并关闭模态框
   * 6. 调用父组件成功回调，刷新团队成员列表
   * 7. 处理错误情况
   */
  const handleInviteFriends = async () => {
    if (selectedFriends.length === 0) {
      message.warning('请选择要邀请的好友');
      return;
    }

    try {
      setInviting(true);
      const request: InviteFriendsRequest = {
        friendIds: selectedFriends
      };
      await FriendService.inviteFriendsToTeam(request);
      message.success(`成功邀请 ${selectedFriends.length} 位好友加入团队`);
      onSuccess();
      onCancel();
    } catch (error) {
      console.error('邀请好友失败:', error);
      message.error('邀请好友失败，请稍后重试');
    } finally {
      setInviting(false);
    }
  };

  const isAllSelected = filteredFriends.length > 0 && 
    filteredFriends.every(friend => selectedFriends.includes(friend.id));
  const isIndeterminate = selectedFriends.length > 0 && !isAllSelected;

  return (
    <Modal
      title={
        <Space>
          <TeamOutlined />
          从好友列表邀请成员
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      width={600}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="invite"
          type="primary"
          icon={<UserAddOutlined />}
          loading={inviting}
          onClick={handleInviteFriends}
          disabled={selectedFriends.length === 0}
        >
          邀请选中的好友 ({selectedFriends.length})
        </Button>
      ]}
    >
      <Alert
        message="邀请好友加入团队"
        description="从您的好友列表中选择要邀请加入当前团队的成员。被邀请的好友将收到团队邀请通知。"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      {/* 搜索框 */}
      <Search
        placeholder="搜索好友姓名或邮箱"
        allowClear
        value={searchKeyword}
        onChange={(e) => setSearchKeyword(e.target.value)}
        style={{ marginBottom: 16 }}
      />

      {/* 全选控制 */}
      {filteredFriends.length > 0 && (
        <>
          <div style={{ marginBottom: 16 }}>
            <Checkbox
              indeterminate={isIndeterminate}
              checked={isAllSelected}
              onChange={(e) => handleSelectAll(e.target.checked)}
            >
              全选 ({filteredFriends.length} 个好友)
            </Checkbox>
          </div>
          <Divider style={{ margin: '8px 0' }} />
        </>
      )}

      {/* 好友列表 */}
      {friends.length === 0 ? (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="暂无好友"
          style={{ padding: '50px 0' }}
        >
          <Text type="secondary">
            您还没有添加任何好友，请先添加好友后再邀请加入团队
          </Text>
        </Empty>
      ) : filteredFriends.length === 0 ? (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={`没有找到包含 "${searchKeyword}" 的好友`}
          style={{ padding: '50px 0' }}
        />
      ) : (
        <List
          loading={loading}
          dataSource={filteredFriends}
          renderItem={(friend) => (
            <List.Item>
              <List.Item.Meta
                avatar={
                  <Checkbox
                    checked={selectedFriends.includes(friend.id)}
                    onChange={(e) => handleFriendSelect(friend.id, e.target.checked)}
                  />
                }
                title={
                  <Space>
                    <Avatar 
                      size={32} 
                      icon={<UserOutlined />}
                      style={{ backgroundColor: '#1890ff' }}
                    >
                      {friend.name.charAt(0).toUpperCase()}
                    </Avatar>
                    <Text strong>{friend.name}</Text>
                  </Space>
                }
                description={
                  <Text type="secondary">{friend.email}</Text>
                }
              />
            </List.Item>
          )}
          style={{ maxHeight: 400, overflowY: 'auto' }}
        />
      )}
    </Modal>
  );
};

export default InviteFriendsModal;
